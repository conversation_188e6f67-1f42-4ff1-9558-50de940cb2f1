{"name": "@kleros/cross-chain-realitio-evidence-display", "version": "0.6.1", "description": "Evidence display for cross-chain Reality.eth integration", "type": "module", "main": "index.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "./", "scripts": {"dev": "vite", "build": "tsc && vite build && mv dist evidence-display-v${npm_package_version}", "preview": "vite preview", "clean": "rm -rf dist evidence-display-v${npm_package_version}", "format": "biome format --write ./src", "lint": "biome lint ./src", "check": "biome check --write ./src", "example": "vite --open 'http://localhost:5173/?example=true&chainID=10200'"}, "dependencies": {"@kleros/cross-chain-realitio-sdk": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss7-compat": "^2.2.17", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.1", "typescript": "^5.8.2", "vite": "^5.4.14", "vite-plugin-node-polyfills": "^0.23.0"}, "volta": {"node": "20.19.0"}}
{"name": "@kleros/cross-chain-realitio-sdk", "version": "0.1.0", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "format": "biome format --write ./src", "lint": "biome lint ./src", "check": "biome check --write ./src", "test": "vitest --run", "test:integration": "vitest --run integration", "test:watch": "vitest"}, "dependencies": {"@reality.eth/contracts": "^3.2.13", "@reality.eth/reality-eth-lib": "^3.4.15", "viem": "^2.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "dotenv": "^16.5.0", "tsup": "^8.0.0", "typescript": "^5.0.0", "vitest": "^1.4.0"}}
{"name": "@kleros/cross-chain-realitio-bots", "version": "0.1.0", "description": "Automation bots for cross-chain arbitration for Realitio", "main": "index.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"deploy:staging": "serverless deploy -s staging", "deploy:production": "serverless deploy -s production"}, "dependencies": {"@kleros/contract-deployments": "^0.3.2", "@kleros/cross-chain-realitio-contracts": "^1.0.0", "aws-sdk": "^2.730.0", "core-js": "^3.0.0", "dayjs": "^1.8.33", "deepmerge": "^4.2.2", "ramda": "^0.27.1", "web3": "^1.10.4", "web3-batched-send": "^1.0.3"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.3", "babel-plugin-ramda": "^2.0.0", "clean-webpack-plugin": "^3.0.0", "serverless": "^2.72.2", "serverless-dotenv-plugin": "^3.0.0", "serverless-dynamodb-client": "^0.0.2", "serverless-dynamodb-local": "^0.2.39", "serverless-offline": "^6.5.0", "serverless-plugin-ifelse": "^1.0.5", "serverless-prune-plugin": "^1.4.3", "serverless-webpack": "^5.3.5", "webpack": "^4.47.0", "webpack-node-externals": "^2.5.2"}}
# Welcome to Serverless!
#
# This file is the main config file for your service.
# It's very minimal at this point and uses default values.
# You can always add more config options for more control.
# We've included some commented out config examples here.
# Just uncomment any of them to get that config option.
#
# For full config options, check the docs:
#    docs.serverless.com
#
# Happy Coding!

service: CrossChainRealitio
# app and org for use with dashboard.serverless.com
#app: your-app-name
#org: your-org-name

# You can pin your service to only deploy with a specific Serverless version
# Check out our docs for more details
# frameworkVersion: "=X.X.X"
useDotenv: true

plugins:
  - serverless-webpack
  - serverless-dotenv-plugin
  - serverless-plugin-ifelse
  - serverless-dynamodb-local
  - serverless-offline

custom:
  webpack:
    webpackConfig: 'webpack.config.js'   # Name of webpack configuration file
    includeModules: # enable auto-packing of external modules
      forceExclude:
        - aws-sdk
    packager: 'yarn'   # Packager that will be used to package your external modules

  dynamodb:
    stages:
      - test
    start:
      port: 8000
      inMemory: true
      heapInitial: 200m
      heapMax: 1g
      migrate: true
      seed: true
      convertEmptyValues: true
    # Uncomment only if you already have a DynamoDB running locally
    # noStart: true

  currentStage: ${opt:stage, self:provider.stage}
  serverlessIfElse:
    - If: '"${self:custom.currentStage}" == "production"'
      Set:
        provider.environment.HOME_CHAIN_ID: 100
        provider.environment.FOREIGN_CHAIN_ID: 1
      ElseSet:
        provider.environment.HOME_CHAIN_ID: 77
        provider.environment.FOREIGN_CHAIN_ID: 42

  prune:
    automatic: true
    number: 4

provider:
  name: aws
  region: ${env:AWS_REGION,'us-east-2'}
  stage: staging
  runtime: nodejs12.x
  lambdaHashingVersion: "20201221"
  environment:
    CHAIN_METADATA_TABLE_NAME: CrossChainRealitio_ChainMetadata_${self:custom.currentStage}
    REQUESTS_TABLE_NAME: CrossChainRealitio_Requests_${self:custom.currentStage}

# you can add statements to the Lambda function's IAM Role here
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "s3:ListBucket"
      Resource: arn:aws:s3:::kleros-bots-private-keys
    - Effect: "Allow"
      Action:
        - "s3:GetObject"
      Resource:
        Fn::Join:
          - ""
          - - arn:aws:s3:::kleros-bots-private-keys
            - "/cross-chain-realitio-proxy.json"
    - Effect: "Allow"
      Action:
        - "dynamodb:*"
      Resource:
        - Fn::GetAtt: [ ChainMetadataTable, Arn ]
        - Fn::GetAtt: [ RequestsTable, Arn ]
        - Fn::Join:
          - ""
          - - Fn::GetAtt: [ RequestsTable, Arn ]
            - "/index/byChainIdAndStatus"

# you can define service wide environment variables here
#  environment:
#    variable1: value1

# you can add packaging information here
#package:
#  include:
#    - include-me.js
#    - include-me-dir/**
#  exclude:
#    - exclude-me.js
#    - exclude-me-dir/**

functions:
  checkNotifiedRequests:
    handler: src/handler.checkNotifiedRequests
    timeout: 600
    events:
      - schedule:
          rate: cron(0,20,40 * * * ? *)

  checkRejectedRequests:
    handler: src/handler.checkRejectedRequests
    timeout: 600
    events:
      - schedule:
          rate: cron(10,30,50 * * * ? *)

  checkRequestedArbitrations:
    handler: src/handler.checkRequestedArbitrations
    timeout: 600
    events:
      - schedule:
          rate: cron(20,40,0 * * * ? *)

  checkArbitratorAnswers:
    handler: src/handler.checkArbitratorAnswers
    timeout: 600
    events:
      - schedule:
          rate: cron(30,50,10 * * * ? *)

# you can add CloudFormation resource templates here
resources:
  Resources:
    ChainMetadataTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.CHAIN_METADATA_TABLE_NAME}
        AttributeDefinitions:
          - AttributeName: key
            AttributeType: S
        KeySchema:
          - AttributeName: key
            KeyType: HASH
        ProvisionedThroughput:
          ReadCapacityUnits: 1
          WriteCapacityUnits: 1

    RequestsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.REQUESTS_TABLE_NAME}
        AttributeDefinitions:
          - AttributeName: chainId
            AttributeType: N
          - AttributeName: requestId
            AttributeType: S
          - AttributeName: status
            AttributeType: N
        KeySchema:
          - AttributeName: requestId
            KeyType: HASH
          - AttributeName: chainId
            KeyType: RANGE
        ProvisionedThroughput:
          ReadCapacityUnits: 1
          WriteCapacityUnits: 1
        GlobalSecondaryIndexes:
          - IndexName: byChainIdAndStatus
            KeySchema:
              - AttributeName: chainId
                KeyType: HASH
              - AttributeName: status
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
            ProvisionedThroughput:
              ReadCapacityUnits: 1
              WriteCapacityUnits: 1
  # Outputs:
  #    NewOutput:
  #      Description: "Description for the output"
  #      Value: "Some output value"


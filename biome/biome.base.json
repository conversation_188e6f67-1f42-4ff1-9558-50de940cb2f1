{"$schema": "../node_modules/@biomejs/biome/configuration_schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noExplicitAny": "error"}, "style": {"noNonNullAssertion": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "ignore": []}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "es5", "semicolons": "always"}}}
<!-- For testing only, no need to upload to IPFS -->
<html>
<script src="../dist/index.iife.js"></script>
<script type="text/javascript">
  const message = Object.fromEntries(new URLSearchParams(decodeURIComponent(window.location.search.substring(1))));

  const {
    arbitrableContractAddress,
    disputeID,
    arbitratorChainID,
    arbitrableChainID,
    chainID,
    arbitratorJsonRpcUrl,
    arbitrableJsonRpcUrl,
    jsonRpcUrl,
  } = message;

  const rpcURL = arbitrableJsonRpcUrl || arbitratorJsonRpcUrl || jsonRpcUrl;
  const cid = arbitrableChainID || arbitratorChainID || chainID;

  if (!rpcURL || !disputeID || !cid) {
    console.error("Evidence display is missing critical information.");
    throw new Error("Evidence display is missing information");
  }

  const scriptParameters = {
    arbitrableContractAddress,
    disputeID,
    arbitratorChainID,
    arbitrableChainID,
    chainID,
    arbitratorJsonRpcUrl,
    arbitrableJsonRpcUrl,
    jsonRpcUrl,
  }

  let resolveScript
  let rejectScript
  const returnPromise = new Promise((resolve, reject) => {
    resolveScript = resolve
    rejectScript = reject
  })

  const evaluator = () => {
    getMetaEvidence()
  }
  evaluator()

  returnPromise
    .then(result => {
      console.log(result);
      const element = document.getElementById("container");
      element.appendChild(
        document.createTextNode(JSON.stringify(result, null, 2))
      );
    })
    .catch(error => { console.error('Error:', error) });
</script>

<body>
  <div>
    <h1>Test Result:</h1>
    <p id="container" style="white-space: pre;" />
  </div>
</body>

</html>
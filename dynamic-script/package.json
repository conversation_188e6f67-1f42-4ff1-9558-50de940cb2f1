{"name": "@kleros/cross-chain-realitio-dynamic-script", "version": "0.6.1", "description": "Dynamic script for cross-chain Reality.eth integration", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "rm -rf dist", "build": "vite build", "format": "biome format --write ./src", "lint": "biome lint ./src", "check": "biome check --write ./src"}, "dependencies": {"@kleros/cross-chain-realitio-sdk": "workspace:*", "buffer": "^6.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@rollup/plugin-inject": "^5.0.5", "@types/node": "^20.11.24", "rollup-plugin-node-polyfills": "^0.2.1", "typescript": "^5.8.2", "vite": "^5.4.14"}, "volta": {"node": "20.19.0"}}
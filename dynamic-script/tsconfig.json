{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "ESNext", "moduleResolution": "bundler", "outDir": "./dist", "rootDir": "./src", "strict": true, "declaration": true, "declarationMap": true, "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}
{"name": "@kleros/cross-chain-realitio-contracts", "version": "1.3.0", "main": "index.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"clean": "hardhat clean", "build": "hardhat compile", "test": "hardhat test", "clean-zksync": "yarn hardhat-zksync clean", "build-zksync": "yarn hardhat-zksync compile", "test-zksync": "TESTING=true yarn hardhat-zksync test", "hardhat-zksync": "hardhat --config hardhat.config.zksync.ts", "zksync:proof:staging": "yarn hardhat-zksync run ./scripts/execute_proof.js --network zkSyncSepolia", "zksync:proof:production": "yarn hardhat-zksync run ./scripts/execute_proof.js --network zkSyncMainnet", "etherscan-verify": "hardhat etherscan-verify", "format:js": "biome format --write scripts deploy tasks", "check:js": "biome check --write scripts deploy tasks", "lint:js": "biome lint scripts deploy tasks", "lint:sol": "solhint 'src/**/*.sol'", "fix:sol": "prettier --write 'src/**/*.sol'", "metaevidence:chiado": "hardhat generate-metaevidence --deployment chiado", "metaevidence:unichainSepolia": "hardhat generate-metaevidence --deployment unichainSepolia", "metaevidence:optimismSepolia": "hardhat generate-metaevidence --deployment optimismSepolia", "metaevidence:arbitrumSepolia": "hardhat generate-metaevidence --deployment arbitrumSepolia", "metaevidence:amoy": "hardhat generate-metaevidence --deployment amoy", "metaevidence:gnosis": "hardhat generate-metaevidence --deployment gnosis", "metaevidence:unichain": "hardhat generate-metaevidence --deployment unichain", "metaevidence:optimism": "hardhat generate-metaevidence --deployment optimism", "metaevidence:redstone": "hardhat generate-metaevidence --deployment redstone", "metaevidence:base": "hardhat generate-metaevidence --deployment base", "metaevidence:arbitrum": "hardhat generate-metaevidence --deployment arbitrum", "metaevidence:polygon": "hardhat generate-metaevidence --deployment polygon", "deploy:chiado:home": "hardhat deploy --network chiado --tags HomeChain", "deploy:chiado:foreign": "HOME_NETWORK=chiado hardhat deploy --network sepolia --tags ForeignChain", "deploy:chiado": "yarn deploy:chiado:home && yarn deploy:chiado:foreign", "deploy:unichainSepolia:home": "hardhat deploy --network unichainSepolia --tags HomeChain", "deploy:unichainSepolia:foreign": "HOME_NETWORK=unichainSepolia hardhat deploy --network sepolia --tags ForeignChain", "deploy:unichainSepolia": "yarn deploy:unichainSepolia:home && yarn deploy:unichainSepolia:foreign", "deploy:optimismSepolia:home": "hardhat deploy --network optimismSepolia --tags HomeChain", "deploy:optimismSepolia:foreign": "HOME_NETWORK=optimismSepolia hardhat deploy --network sepolia --tags ForeignChain", "deploy:optimismSepolia": "yarn deploy:optimismSepolia:home && yarn deploy:optimismSepolia:foreign", "deploy:arbitrumSepolia:home": "hardhat deploy --network arbitrumSepolia --tags HomeChain", "deploy:arbitrumSepolia:foreign": "HOME_NETWORK=arbitrumSepolia hardhat deploy --network sepolia --tags ForeignChain", "deploy:arbitrumSepolia": "yarn deploy:arbitrumSepolia:home && yarn deploy:arbitrumSepolia:foreign", "deploy:amoy:home": "hardhat deploy --network amoy --tags HomeChain", "deploy:amoy:foreign": "HOME_NETWORK=amoy hardhat deploy --network sepolia --tags ForeignChain", "deploy:amoy": "yarn deploy:amoy:home && yarn deploy:amoy:foreign", "deploy:gnosis:home": "hardhat deploy --network gnosis --tags HomeChain", "deploy:gnosis:foreign": "HOME_NETWORK=gnosis hardhat deploy --network mainnet --tags Foreign<PERSON><PERSON><PERSON>", "deploy:gnosis": "yarn deploy:gnosis:home && yarn deploy:gnosis:foreign", "deploy:unichain:home": "hardhat deploy --network unichain --tags HomeChain", "deploy:unichain:foreign": "HOME_NETWORK=unichain hardhat deploy --network mainnet --tags ForeignChain", "deploy:unichain": "yarn deploy:unichain:home && yarn deploy:unichain:foreign", "deploy:optimism:home": "hardhat deploy --network optimism --tags HomeChain", "deploy:optimism:foreign": "HOME_NETWORK=optimism hardhat deploy --network mainnet --tags ForeignChain", "deploy:optimism": "yarn deploy:optimism:home && yarn deploy:optimism:foreign", "deploy:redstone:home": "hardhat deploy --network redstone --tags HomeChain", "deploy:redstone:foreign": "HOME_NETWORK=redstone hardhat deploy --network mainnet --tags ForeignChain", "deploy:redstone": "yarn deploy:redstone:home && yarn deploy:redstone:foreign", "deploy:base:home": "hardhat deploy --network base --tags HomeChain", "deploy:base:foreign": "HOME_NETWORK=base hardhat deploy --network mainnet --tags ForeignChain", "deploy:base": "yarn deploy:base:home && yarn deploy:base:foreign", "deploy:arbitrum:home": "hardhat deploy --network arbitrum --tags HomeChain", "deploy:arbitrum:foreign": "HOME_NETWORK=arbitrum hardhat deploy --network mainnet --tags ForeignChain", "deploy:arbitrum": "yarn deploy:arbitrum:home && yarn deploy:arbitrum:foreign", "deploy:polygon:home": "hardhat deploy --network polygon --tags HomeChain", "deploy:polygon:foreign": "HOME_NETWORK=polygon hardhat deploy --network mainnet --tags ForeignChain", "deploy:polygon": "yarn deploy:polygon:home && yarn deploy:polygon:foreign"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@matterlabs/hardhat-zksync-deploy": "^1.6.0", "@matterlabs/hardhat-zksync-solc": "^1.2.5", "@matterlabs/hardhat-zksync-verify": "^1.7.1", "@nomicfoundation/hardhat-chai-matchers": "^2.0.8", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-ignition": "^0.15.9", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.9", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.12", "@nomicfoundation/ignition-core": "^0.15.9", "@reality.eth/contracts": "^3.2.13", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.20", "@types/mocha": "^9.1.1", "@types/node": "^22.14.0", "chai": "^4.5.0", "ethers": "^6.13.5", "hardhat": "^2.22.18", "hardhat-deploy": "^0.14.0", "hardhat-deploy-ethers": "^0.4.2", "hardhat-gas-reporter": "^2.2.2", "prettier": "^3.5.3", "prettier-plugin-solidity": "^1.4.2", "solhint-community": "^4.0.1", "solhint-plugin-prettier": "^0.1.0", "solidity-coverage": "^0.8.14", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.8.3", "zksync-ethers": "^6.15.4"}, "dependencies": {"@arbitrum/nitro-contracts": "^1.3.0", "@arbitrum/sdk": "^v3.1.9", "@kleros/dispute-resolver-interface-contract-0.7": "npm:@kleros/dispute-resolver-interface-contract@^2.0.0", "@kleros/dispute-resolver-interface-contract-0.8": "npm:@kleros/dispute-resolver-interface-contract@^8.0.0", "@kleros/ethereum-libraries": "^7.0.0", "@matterlabs/zksync-contracts": "^0.6.1", "dotenv": "^16.4.7"}}
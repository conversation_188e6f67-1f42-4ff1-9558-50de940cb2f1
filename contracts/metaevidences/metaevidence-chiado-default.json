{"_v": "1.0.0", "category": "Oracle", "title": "A reality.eth question", "description": "A reality.eth question has been raised to arbitration.", "question": "Give an answer to the question.", "evidenceDisplayInterfaceURI": "/ipfs/QmSKx7zsJUFr86gpCci9JpMeKA36jhambezL23jFfoXxGD/index.html", "dynamicScriptURI": "/ipfs/QmaG5RFGXp5gbu4EeA2nidFa1ks4qnFNDrET9WpDetKpXr", "fileURI": "/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf", "arbitrableChainID": "10200", "arbitratorChainID": "11155111", "dynamicScriptRequiredParams": ["disputeID", "arbitrableChainID", "arbitrableJsonRpcUrl", "arbitrableContractAddress"], "evidenceDisplayInterfaceRequiredParams": ["disputeID", "arbitrableChainID", "arbitrableJsonRpcUrl", "arbitrableContractAddress"]}
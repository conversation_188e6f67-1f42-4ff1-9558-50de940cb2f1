# @kleros/cross-chain-realitio-contracts

Smart contracts for the Reality.eth cross-chain proxies.

---
- **[Deployments](#deployments)**
  - **[Mainnets](#mainnets)**
    - **[Gnosis](#gnosis)**
    - **[Unichain](#unichain)**
    - **[Base](#base)**
    - **[Polygon](#polygon)**
  - **[Testnets](#testnets)**
    - **[Chiado](#chiado)**
    - **[UnichainSepolia](#unichainsepolia)**
    - **[OptimismSepolia](#optimismsepolia)**
    - **[ArbitrumSepolia](#arbitrumsepolia)**
    - **[ZksyncSepolia](#zksyncsepolia)**
- **[Sequence diagram](#sequence-diagram)**
- **[Contributing](#contributing)**
  - **[Install Dependencies](#install-dependencies)**
  - **[Run Tests](#run-tests)**
  - **[Compile the Contracts](#compile-the-contracts)**
  - **[Deploy Instructions](#deploy-instructions)**
    - **[0. Set the Environment Variables](#0-set-the-environment-variables)**
    - **[1. Update the Constructor Parameters (optional)](#1-update-the-constructor-parameters-optional)**
    - **[2. Deploy the Proxies](#2-deploy-the-proxies)**
---

## Deployments

Refresh the list of deployed contracts by running `./scripts/populateReadme.sh`.

### Mainnets

#### Gnosis

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.1.2 | [Default 2](deployments/gnosis/RealitioProxy-v1.1.2.json#L6) | [0x68154E..7491Dc](https://gnosisscan.io/address/******************************************) | [0xFe0eb5..c0Aa68](https://etherscan.io/address/******************************************) | 0 | 31 | [RealityETH_v3_0](https://gnosisscan.io/address/******************************************) | [seer](https://cdn.kleros.link/ipfs/QmPmRkXFUmzP4rq2YfD3wNwL8bg3WDxkYuvTP9A9UZm9gJ/seer-markets-resolution-policy.pdf) |  |
| v1.1.1 | [Polkamarkets Foreland](deployments/gnosis/RealitioProxy-v1.1.1.json#L6) | [0x5AFa42..86F90e](https://gnosisscan.io/address/******************************************) | [0x8453bA..552425](https://etherscan.io/address/******************************************) | 0 | 5 | [RealityETH_ERC20_v3_0](https://gnosisscan.io/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) | :warning: bad metadata |
| v1.1.1 | [Default](deployments/gnosis/RealitioProxy-v1.1.1.json#L29) | [0x29f39d..fce222](https://gnosisscan.io/address/******************************************) | [0x2F0895..695F21](https://etherscan.io/address/******************************************) | 0 | 31 | [RealityETH_v3_0](https://gnosisscan.io/address/******************************************) | [noAnswerTooSoon](https://cdn.kleros.link/ipfs/QmaUr6hnSVxYD899xdcn2GUVtXVjXoSXKZbce3zFtGWw4H/Question_Resolution_Policy.pdf) | :warning: bad metadata |
| v1.1.0 | [Omen AI bis](deployments/gnosis/RealitioProxy-v1.1.0.json#L6) | [0x5562ac..96cdf2](https://gnosisscan.io/address/******************************************) | [0xeF2Ae6..6B2f59](https://etherscan.io/address/******************************************) | 0 | 31 | [Realitio_v2_1](https://gnosisscan.io/address/******************************************) | [omen](https://cdn.kleros.link/ipfs/QmZM12kkguXFk2C94ykrKpambt4iUVKsVsxGxDEdLS68ws/omen-rules.pdf) | :warning: bad metadata |
| v1.0.0 | [Omen AI](deployments/gnosis/RealitioProxy-v1.0.0.json#L6) | [0xe40DD8..a75ecd](https://gnosisscan.io/address/******************************************) | [0x79d046..eA3D49](https://etherscan.io/address/******************************************) | 0 | 500 | [Realitio_v2_1](https://gnosisscan.io/address/******************************************) | [omen](https://cdn.kleros.link/ipfs/QmZM12kkguXFk2C94ykrKpambt4iUVKsVsxGxDEdLS68ws/omen-rules.pdf) | :warning: bad metadata |


#### Unichain

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.3.0 | [Default](deployments/unichain/RealitioProxy-v1.3.0.json#L6) | [0xcB4B48..b045a6](https://uniscan.xyz/address/******************************************) | [0x122D6B..b7954D](https://etherscan.io/address/******************************************) | 24 | 15 | [RealityUnverified](https://uniscan.xyz/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |
| v1.3.0 | [Butter](deployments/unichain/RealitioProxy-v1.3.0.json#L29) | [0x8FeAB3..EC44b6](https://uniscan.xyz/address/******************************************) | [0x3FB831..48Ac17](https://etherscan.io/address/******************************************) | 24 | 15 | [RealityUnverified](https://uniscan.xyz/address/******************************************) | [butter](https://cdn.kleros.link/ipfs/QmSv9ohhChMtyqwqsvfgeJtZQBWkwAboBc1n3UGvprfdd7/Conditional_Funding_Markets_-_Question_Resolution_Policy.pdf) |  |


#### Base

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.3.0 | [Default](deployments/base/RealitioProxy-v1.3.0.json#L6) | [0xcB4B48..b045a6](https://basescan.org/address/******************************************) | [0x87f58F..e2EAf9](https://etherscan.io/address/******************************************) | 24 | 15 | [RealityETH_v3_0](https://basescan.org/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |
| v1.3.0 | [Zodiac SafeSnap](deployments/base/RealitioProxy-v1.3.0.json#L29) | [0xBeeB21..FBe96B](https://basescan.org/address/******************************************) | [0x20E1D4..aAe373](https://etherscan.io/address/******************************************) | 24 | 15 | [RealityETH_v3_0](https://basescan.org/address/******************************************) | [zodiac](https://cdn.kleros.link/ipfs/QmXyo9M4Z2XY6Nw9UfuuUNzKXXNhvt24q6pejuN9RYWPMr/Reality_Module_Governance_Oracle-Question_Resolution_Policy.pdf) |  |


#### Polygon

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.1.0 | [Default](deployments/polygon/RealitioProxy-v1.1.0.json#L6) | [0x5AFa42..86F90e](https://polygonscan.com/address/******************************************) | [0x776e58..231e52](https://etherscan.io/address/******************************************) | 0 | 31 | [RealityETH_v3_0](https://polygonscan.com/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |
| v1.1.0 | [Polkamarkets](deployments/polygon/RealitioProxy-v1.1.0.json#L29) | [0x68154E..7491Dc](https://polygonscan.com/address/******************************************) | [0x68c4cc..bAFE28](https://etherscan.io/address/******************************************) | 0 | 31 | [RealityETH_ERC20_v3_0](https://polygonscan.com/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |


### Testnets

#### Chiado

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.3.0 | [Chiado default](deployments/chiado/RealitioProxy-v1.3.0.json#L6) | [0x87f58F..e2EAf9](https://gnosis-chiado.blockscout.com/address/******************************************) | [0x781Bfb..904aE0](https://sepolia.etherscan.io/address/******************************************) | 3 | 1 | [RealityUnverified](https://gnosis-chiado.blockscout.com/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |
| v1.1.0 | [Default](deployments/chiado/RealitioProxy-v1.1.0.json#L6) | [0xE62094..FffeD7](https://gnosis-chiado.blockscout.com/address/******************************************) | [0x5d7cB7..969D42](https://sepolia.etherscan.io/address/******************************************) | 0 | 0 | [RealityUnverified](https://gnosis-chiado.blockscout.com/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) | :warning: bad metadata |


#### UnichainSepolia

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.3.0 | [Default](deployments/unichainSepolia/RealitioProxy-v1.3.0.json#L6) | [0x122D6B..b7954D](https://sepolia.uniscan.xyz/address/******************************************) | [0x4C10F0..26191D](https://sepolia.etherscan.io/address/******************************************) | 3 | 1 | [RealityETH_v3_0](https://sepolia.uniscan.xyz/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |
| v1.3.0 | [Butter](deployments/unichainSepolia/RealitioProxy-v1.3.0.json#L29) | [0x87f58F..e2EAf9](https://sepolia.uniscan.xyz/address/******************************************) | [0xA42986..fFDF15](https://sepolia.etherscan.io/address/******************************************) | 3 | 1 | [RealityETH_v3_0](https://sepolia.uniscan.xyz/address/******************************************) | [butter](https://cdn.kleros.link/ipfs/QmSv9ohhChMtyqwqsvfgeJtZQBWkwAboBc1n3UGvprfdd7/Conditional_Funding_Markets_-_Question_Resolution_Policy.pdf) |  |


#### OptimismSepolia

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.1.0 | [Default](deployments/optimismSepolia/RealitioProxy-v1.1.0.json#L6) | [0xFe0eb5..c0Aa68](https://sepolia-optimism.etherscan.io/address/******************************************) | [0x6a41AF..5DC3bA](https://sepolia.etherscan.io/address/******************************************) | 0 | 0 | [RealityUnverified](https://sepolia-optimism.etherscan.io/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |


#### ArbitrumSepolia

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.1.0 | [Default](deployments/arbitrumSepolia/RealitioProxy-v1.1.0.json#L6) | [0x890deB..d6770e](https://sepolia.arbiscan.io/address/******************************************) | [0x26222E..A821F9](https://sepolia.etherscan.io/address/******************************************) | 0 | 0 | [RealityUnverified](https://sepolia.arbiscan.io/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |


#### ZksyncSepolia

| Version | Name | Home Proxy | Foreign Proxy | CourtID | MinJurors | Reality | Policy | Comment |
|---------|------|------------|---------------|---------|-----------|---------|---------|---------|
| v1.1.0 | [Default](deployments/zksyncSepolia/RealitioProxy-v1.1.0.json#L6) | [0x473DC4..41530D](https://sepolia.explorer.zksync.io/address/******************************************) | [0x54C68f..9d5612](https://sepolia.etherscan.io/address/******************************************) | 0 | 0 | [RealityETH_zksync_v3_0](https://sepolia.explorer.zksync.io/address/******************************************) | [default](https://cdn.kleros.link/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf) |  |

## Sequence diagram

Example for Optimism.

```mermaid
sequenceDiagram
    participant User
    participant L1 as Foreign Proxy (L1)
    participant Arbitrator
    participant Messenger as CrossDomainMessenger
    participant L2 as Home Proxy (L2)
    participant Realitio

    %% Initial Arbitration Request
    User->>L1: requestArbitration(questionID, maxPrevious) $arbitrationCost
    activate L1
    L1->>L1: Store arbitration request
    Note over L1: emit ArbitrationRequested
    L1->>Messenger: sendMessage(receiveArbitrationRequest)
    deactivate L1

    Messenger->>L2: receiveArbitrationRequest()
    activate L2
    L2->>Realitio: notifyOfArbitrationRequest()
    
    alt Success
        Realitio-->>L2: Success
        L2->>L2: Set status = Notified
        Note over L2: emit RequestNotified
    else Failure
        Realitio-->>L2: Failure
        L2->>L2: Set status = Rejected
        Note over L2: emit RequestRejected
    end
    deactivate L2

    %% Handle Notified Request
    User->>L2: handleNotifiedRequest()
    activate L2
    L2->>L2: Set status = AwaitingRuling
    Note over L2: emit RequestAcknowledged
    L2->>Messenger: sendMessage(receiveArbitrationAcknowledgement)
    deactivate L2

    rect rgb(136, 136, 136)
        Note over Messenger,L1: 7-day Optimistic Bridge Message
        Messenger-->>L1: receiveArbitrationAcknowledgement()
    end

    activate L1
    L1->>Arbitrator: createDispute() $arbitrationCost
    L1->>L1: Set status = Created
    Note over L1: emit ArbitrationCreated
    Note over L1: emit Dispute
    deactivate L1

    %% Appeal Flow
    User->>L1: fundAppeal(arbitrationID, answer) $appealCost
    activate L1
    alt Fully Funded
        L1->>L1: Store contribution
        Note over L1: emit Contribution
        Note over L1: emit RulingFunded
        L1->>Arbitrator: appeal() $appealCost
    else Not Fully Funded
        L1->>L1: Store partial contribution
        Note over L1: emit Contribution
        L1->>User: Return excess funds
    end
    deactivate L1

    %% Arbitrator Ruling
    Arbitrator->>L1: rule(disputeID, ruling)
    activate L1
    L1->>L1: Set status = Ruled
    Note over L1: emit Ruling
    L1->>Messenger: sendMessage(receiveArbitrationAnswer)
    deactivate L1

    Messenger->>L2: receiveArbitrationAnswer()
    activate L2
    L2->>L2: Set status = Ruled
    Note over L2: emit ArbitratorAnswered
    deactivate L2

    %% Report Answer
    User->>L2: reportArbitrationAnswer()
    activate L2
    L2->>Realitio: assignWinnerAndSubmitAnswerByArbitrator()
    L2->>L2: Set status = Finished
    Note over L2: emit ArbitrationFinished
    deactivate L2

    %% Withdraw Fees
    User->>L1: withdrawFeesAndRewards()
    activate L1
    alt Has Rewards
        L1->>User: Send rewards $
        Note over L1: emit Withdrawal
    end
    deactivate L1
```

## Contributing

### Install Dependencies

```bash
yarn install
```

### Run Tests

```bash
yarn test
```

### Compile the Contracts

```bash
yarn build
```

### Deploy Instructions

**IMPORTANT:** new versions of any of the contracts require publishing **both** Home and Foreign proxies, as their binding is immutable.

**NOTICE:** the commands bellow work only if you are inside the `contracts/` directory.

#### 0. Set the Environment Variables

Copy `.env.example` file as `.env` and edit it accordingly.

```bash
cp .env.example .env
```

Set the following env vars:
- `PRIVATE_KEY`: the private key of the deployer account.
- `INFURA_API_KEY`: the API key for infura.
- `ETHERSCAN_API_KEY`: the API key to verify the contracts on Etherscan.

#### 1. Update the Constructor Parameters (optional)

If some of the constructor parameters (such as the Meta Evidence) needs to change, you need to update the files in the `deploy/` directory.

#### 2. Deploy the Proxies

```bash
yarn deploy:chiado:home
yarn deploy:chiado:foreign
```

The deployed addresses should be output to the screen after the deployment is complete.
If you miss that, you can always go to the `deployments/<network>` directory and look for the respective file.


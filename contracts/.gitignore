# Hardhat files
cache
artifacts
cache-zk
artifacts-zk

# Hardhat deploy files
deployments/localhost
deployments/hardhat
deployments/*/solcInputs/

# POA solidity flattener output
out/

# Created by https://www.toptal.com/developers/gitignore/api/vim,node,visualstudiocode,yarn
# Edit at https://www.toptal.com/developers/gitignore?templates=vim,node,visualstudiocode,yarn

### Node ###
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

## Hardhat typechain bindings/types
typechain
typechain/**
typechain-types
typechain-types/**

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

### Vim ###
# Swap
[._]*.s[a-v][a-z]
!*.svg  # comment out if you don't need vector files
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Session
Session.vim
Sessionx.vim

# Temporary
.netrwhist
*~
# Auto-generated tag files
tags
# Persistent undo
[._]*.un~

### VisualStudioCode ###
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

### VisualStudioCode Patch ###
# Ignore all local history of files
.history

### yarn ###
# https://yarnpkg.com/advanced/qa#which-files-should-be-gitignored

# .yarn/unplugged and .yarn/build-state.yml should likely always be ignored since
# they typically hold machine-specific build artifacts. Ignoring them might however
# prevent Zero-Installs from working (to prevent this, set enableScripts to false).
.yarn/unplugged
.yarn/build-state.yml

# .yarn/cache and .pnp.* may be safely ignored, but you'll need to run yarn install
# to regenerate them between each branch switch.
# Uncomment the following lines if you're not using Zero-Installs:
# .yarn/cache
# .pnp.*

# End of https://www.toptal.com/developers/gitignore/api/vim,node,visualstudiocode,yarn

.env*
.flaskenv*
!.env.project
!.env.vault
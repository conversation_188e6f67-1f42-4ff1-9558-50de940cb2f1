# Use regular arrays to preserve the ordering
MAINNET_NETWORKS=("gnosis" "unichain" "optimism" "redstone" "base" "arbitrum" "polygon" "zksyncMainnet")
declare -A HOME_MAINNET_EXPLORERS=(
    ["gnosis"]="https://gnosisscan.io/address/"
    ["unichain"]="https://uniscan.xyz/address/"
    ["optimism"]="https://optimistic.etherscan.io/address/"
    ["redstone"]="https://explorer.redstone.xyz/address/"
    ["base"]="https://basescan.org/address/"
    ["arbitrum"]="https://arbiscan.io/address/"
    ["polygon"]="https://polygonscan.com/address/"
    ["zksyncMainnet"]="https://explorer.zksync.io/address/"
)

TESTNET_NETWORKS=("chiado" "unichainSepolia" "optimismSepolia" "arbitrumSepolia" "amoy" "zksyncSepolia")
declare -A HOME_TESTNETS_EXPLORERS=(
    ["chiado"]="https://gnosis-chiado.blockscout.com/address/"
    ["unichainSepolia"]="https://sepolia.uniscan.xyz/address/"
    ["optimismSepolia"]="https://sepolia-optimism.etherscan.io/address/"
    ["arbitrumSepolia"]="https://sepolia.arbiscan.io/address/"
    ["amoy"]="https://amoy.polygonscan.com/address/"
    ["zksyncSepolia"]="https://sepolia.explorer.zksync.io/address/"
)

declare -A FOREIGN_NETWORK_EXPLORERS=(
    ["sepolia"]="https://sepolia.etherscan.io/address/"
    ["mainnet"]="https://etherscan.io/address/"
)
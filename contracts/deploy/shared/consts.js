const chains = require("./chains");
const { generateMetadata } = require("./utils");

const { mainnet, sepolia } = chains.foreignChains;

const arbitrators = {
  [mainnet.chainId]: "******************************************", // KlerosLiquid
  [sepolia.chainId]: "******************************************", // KlerosLiquid
};

const wNatives = {
  [mainnet.chainId]: "******************************************", // WETH address
  [sepolia.chainId]: "******************************************", // WETH address
};

const courts = {
  [mainnet.chainId]: {
    general: 1,
    oracle: 24,
  },
  [sepolia.chainId]: {
    general: 1,
    oracle: 3,
  },
};

const metadata = generateMetadata("default");

module.exports = {
  arbitrators,
  wNatives,
  courts,
  metadata,
};

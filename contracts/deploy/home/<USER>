const { realityETHConfig } = require("@reality.eth/contracts");
const { ethers } = require("hardhat");
const { homeChains, metadata } = require("../shared");
const { amoy, polygon } = homeChains;

const homeParameters = {
  [amoy.chainId]: {
    // https://github.com/RealityETH/reality-eth-monorepo/blob/main/packages/contracts/chains/deployments/80002/
    // realitio: realityETHConfig(amoy.chainId, "MATIC", "3.0").address,
    realitio: "******************************************", // NOT DEPLOYED YET
    // https://docs.polygon.technology/pos/how-to/bridging/l1-l2-communication/state-transfer/#prerequisites
    fxChild: "******************************************",
  },
  [polygon.chainId]: {
    realitio: realityETHConfig(polygon.chainId, "MATIC", "3.0").address,
    // https://docs.polygon.technology/pos/how-to/bridging/l1-l2-communication/state-transfer/#prerequisites
    fxChild: "******************************************",
  },
};

async function deployHomeProxy({ deploy, from, parameters, foreignChainId, foreignProxy }) {
  const { realitio, fxChild } = parameters;
  const deployed = await deploy("RealitioHomeProxyPolygon", {
    from,
    args: [realitio, metadata, foreignChainId, fxChild],
    gas: 8000000,
    log: true,
  });

  console.log(`Linking to foreign proxy ${foreignProxy}`);
  const homeProxy = await ethers.getContract("RealitioHomeProxyPolygon");
  await homeProxy.setFxRootTunnel(foreignProxy);
  return deployed;
}

const supportedChainIds = Object.keys(homeParameters).map(Number);

module.exports = { deployHomeProxy, homeParameters, supportedChainIds };

const { realityETHConfig } = require("@reality.eth/contracts");
const { homeChains, gwei, metadata } = require("../shared");
const { chiado, gnosis } = homeChains;

const homeParameters = {
  [chiado.chainId]: {
    realitio: realityETHConfig(chiado.chainId, "XDAI", "3.2").address,
    // https://docs.gnosischain.com/developers/Usefulcontracts#chiado-bridge-contract-addresses
    homeAmb: "******************************************",
  },
  [gnosis.chainId]: {
    realitio: realityETHConfig(gnosis.chainId, "XDAI", "3.0").address,
    // https://docs.gnosischain.com/developers/Usefulcontracts#gnosis-chain-bridge-contract-addresses
    homeAmb: "******************************************",
  },
};

async function deployHomeProxy({ deploy, from, parameters, foreignChainId, foreignProxy }) {
  const { realitio, homeAmb } = parameters;

  // Fully qualified contract name because there's also an 0.7 artifact
  return await deploy("RealitioHomeProxyGnosis", {
    contract: "src/0.8/RealitioHomeProxyGnosis.sol:RealitioHomeProxyGnosis",
    from,
    args: [realitio, metadata, foreignProxy, foreignChainId, homeAmb],
    maxPriorityFeePerGas: gwei("2"),
    maxFeePerGas: gwei("20"),
    log: true,
  });
}

const supportedChainIds = Object.keys(homeParameters).map(Number);

module.exports = { deployHomeProxy, homeParameters, supportedChainIds };

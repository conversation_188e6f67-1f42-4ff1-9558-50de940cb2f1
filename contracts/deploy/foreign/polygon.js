const { ethers } = require("hardhat");
const { encodeExtraData, getMetaEvidenceCID } = require("../shared");

// Bridge addresses:
// https://docs.polygon.technology/pos/how-to/bridging/l1-l2-communication/state-transfer/#prerequisites

// The parameters are keyed by home network name rather than by chainId because several home proxies point to the same foreign proxy.
const foreignParameters = {
  amoy: {
    numberOfJurors: 1,
    checkpointManager: "******************************************",
    fxRoot: "******************************************",
  },
  polygon: {
    numberOfJurors: 15,
    checkpointManager: "******************************************",
    fxRoot: "******************************************",
  },
};

async function deployForeignProxy({
  deploy,
  from,
  parameters,
  homeNetworkName,
  homeProxy,
  wNative,
  arbitrator,
  courts,
  multipliers,
}) {
  const { numberOfJurors, checkpointManager, fxRoot } = parameters;
  const metaEvidence = getMetaEvidenceCID(homeNetworkName);
  const arbitratorExtraData = encodeExtraData(courts.oracle, numberOfJurors);
  const deployed = await deploy("RealitioForeignProxyPolygon", {
    from,
    args: [wNative, arbitrator, arbitratorExtraData, metaEvidence, ...multipliers, checkpointManager, fxRoot],
    log: true,
    gas: 8000000,
  });

  console.log(`Linking to home proxy ${homeProxy}`);
  const foreignProxy = await ethers.getContract("RealitioForeignProxyPolygon");
  await foreignProxy.setFxChildTunnel(homeProxy);
  return deployed;
}

const getHomeProxyName = () => "RealitioHomeProxyPolygon";

const supportedHomeChains = Object.keys(foreignParameters).map(String);

module.exports = { foreignParameters, supportedHomeChains, deployForeignProxy, getHomeProxyName };

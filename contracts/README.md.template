# @kleros/cross-chain-realitio-contracts

Smart contracts for the Reality.eth cross-chain proxies.

---
$toc
---

## Deployments

Refresh the list of deployed contracts by running `./scripts/populateReadme.sh`.

$deployments

## Sequence diagram

Example for Optimism.

```mermaid
sequenceDiagram
    participant User
    participant L1 as Foreign Proxy (L1)
    participant Arbitrator
    participant Messenger as CrossDomainMessenger
    participant L2 as Home Proxy (L2)
    participant Realitio

    %% Initial Arbitration Request
    User->>L1: requestArbitration(questionID, maxPrevious) $arbitrationCost
    activate L1
    L1->>L1: Store arbitration request
    Note over L1: emit ArbitrationRequested
    L1->>Messenger: sendMessage(receiveArbitrationRequest)
    deactivate L1

    Messenger->>L2: receiveArbitrationRequest()
    activate L2
    L2->>Realitio: notifyOfArbitrationRequest()
    
    alt Success
        Realitio-->>L2: Success
        L2->>L2: Set status = Notified
        Note over L2: emit RequestNotified
    else Failure
        Realitio-->>L2: Failure
        L2->>L2: Set status = Rejected
        Note over L2: emit RequestRejected
    end
    deactivate L2

    %% Handle Notified Request
    User->>L2: handleNotifiedRequest()
    activate L2
    L2->>L2: Set status = AwaitingRuling
    Note over L2: emit RequestAcknowledged
    L2->>Messenger: sendMessage(receiveArbitrationAcknowledgement)
    deactivate L2

    rect rgb(136, 136, 136)
        Note over Messenger,L1: 7-day Optimistic Bridge Message
        Messenger-->>L1: receiveArbitrationAcknowledgement()
    end

    activate L1
    L1->>Arbitrator: createDispute() $arbitrationCost
    L1->>L1: Set status = Created
    Note over L1: emit ArbitrationCreated
    Note over L1: emit Dispute
    deactivate L1

    %% Appeal Flow
    User->>L1: fundAppeal(arbitrationID, answer) $appealCost
    activate L1
    alt Fully Funded
        L1->>L1: Store contribution
        Note over L1: emit Contribution
        Note over L1: emit RulingFunded
        L1->>Arbitrator: appeal() $appealCost
    else Not Fully Funded
        L1->>L1: Store partial contribution
        Note over L1: emit Contribution
        L1->>User: Return excess funds
    end
    deactivate L1

    %% Arbitrator Ruling
    Arbitrator->>L1: rule(disputeID, ruling)
    activate L1
    L1->>L1: Set status = Ruled
    Note over L1: emit Ruling
    L1->>Messenger: sendMessage(receiveArbitrationAnswer)
    deactivate L1

    Messenger->>L2: receiveArbitrationAnswer()
    activate L2
    L2->>L2: Set status = Ruled
    Note over L2: emit ArbitratorAnswered
    deactivate L2

    %% Report Answer
    User->>L2: reportArbitrationAnswer()
    activate L2
    L2->>Realitio: assignWinnerAndSubmitAnswerByArbitrator()
    L2->>L2: Set status = Finished
    Note over L2: emit ArbitrationFinished
    deactivate L2

    %% Withdraw Fees
    User->>L1: withdrawFeesAndRewards()
    activate L1
    alt Has Rewards
        L1->>User: Send rewards $
        Note over L1: emit Withdrawal
    end
    deactivate L1
```

## Contributing

### Install Dependencies

```bash
yarn install
```

### Run Tests

```bash
yarn test
```

### Compile the Contracts

```bash
yarn build
```

### Deploy Instructions

**IMPORTANT:** new versions of any of the contracts require publishing **both** Home and Foreign proxies, as their binding is immutable.

**NOTICE:** the commands bellow work only if you are inside the `contracts/` directory.

#### 0. Set the Environment Variables

Copy `.env.example` file as `.env` and edit it accordingly.

```bash
cp .env.example .env
```

Set the following env vars:
- `PRIVATE_KEY`: the private key of the deployer account.
- `INFURA_API_KEY`: the API key for infura.
- `ETHERSCAN_API_KEY`: the API key to verify the contracts on Etherscan.

#### 1. Update the Constructor Parameters (optional)

If some of the constructor parameters (such as the Meta Evidence) needs to change, you need to update the files in the `deploy/` directory.

#### 2. Deploy the Proxies

```bash
yarn deploy:chiado:home
yarn deploy:chiado:foreign
```

The deployed addresses should be output to the screen after the deployment is complete.
If you miss that, you can always go to the `deployments/<network>` directory and look for the respective file.


{"address": "0x122D6B4197531bF4e9314fD00259b1dc1Db7954D", "abi": [{"inputs": [{"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "uint256", "name": "_winnerMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserAppealPeriodMultiplier", "type": "uint256"}, {"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "address", "name": "_messenger", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "Contribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "RulingFunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_ruling", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "META_EVIDENCE_ID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTIPLIER_DIVISOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFUSE_TO_ARBITRATE_REALITIO", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationCreatedBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignProxyOptimism.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}, {"internalType": "uint256", "name": "disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "answer", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "disputeIDToDisputeDetails", "outputs": [{"internalType": "uint256", "name": "arbitrationID", "type": "uint256"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_externalDisputeID", "type": "uint256"}], "name": "externalIDtoLocalID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "fundAppeal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "address", "name": "_contributor", "type": "address"}], "name": "getContributionsToSuccessfulFundings", "outputs": [{"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "contributions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "getFundingStatus", "outputs": [{"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "bool", "name": "fullyFunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMultipliers", "outputs": [{"internalType": "uint256", "name": "winner", "type": "uint256"}, {"internalType": "uint256", "name": "loser", "type": "uint256"}, {"internalType": "uint256", "name": "loserAppealPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "divisor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}], "name": "getNumberOfRounds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256[]", "name": "paidFees", "type": "uint256[]"}, {"internalType": "uint256", "name": "feeRewards", "type": "uint256"}, {"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "getTotalWithdrawableAmount", "outputs": [{"internalType": "uint256", "name": "sum", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserAppealPeriodMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "messenger", "outputs": [{"internalType": "contract ICrossDomainMessenger", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minGasLimit", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "numberOfRulingOptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "questionIDToArbitrationID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "string", "name": "_evidenceURI", "type": "string"}], "name": "submitEvidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "winner<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "withdrawFeesAndRewards", "outputs": [{"internalType": "uint256", "name": "reward", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "withdrawFeesAndRewardsForAllRounds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0x1d7e1eacc2650c54eb2cb03714b3d4062622732ff83c9922d31fc31c16ecc7e8", "receipt": {"to": null, "from": "0x0efFC4A996045aff0489774051f94f42F2D6dfc9", "contractAddress": "0x122D6B4197531bF4e9314fD00259b1dc1Db7954D", "transactionIndex": 93, "gasUsed": "2821243", "logsBloom": "0x00000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000020000000020000000000000000000800000000000000000000000000000000000000000000000000000000000000800000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xd435c12f61d1639b0bc648cc94954aea3a83dd5502179fadddfc4180d55553ac", "transactionHash": "0x1d7e1eacc2650c54eb2cb03714b3d4062622732ff83c9922d31fc31c16ecc7e8", "logs": [{"transactionIndex": 93, "blockNumber": 21889639, "transactionHash": "0x1d7e1eacc2650c54eb2cb03714b3d4062622732ff83c9922d31fc31c16ecc7e8", "address": "0x122D6B4197531bF4e9314fD00259b1dc1Db7954D", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000342f697066732f516d6551783852796255757a395941566d3844665170637951566d4a6746705a6d72336355537554695070565737000000000000000000000000", "logIndex": 193, "blockHash": "0xd435c12f61d1639b0bc648cc94954aea3a83dd5502179fadddfc4180d55553ac"}], "blockNumber": 21889639, "cumulativeGasUsed": "11129290", "status": 1, "byzantium": true}, "args": ["0x988b3a538b618c7a603e1c11ab82cd16dbe28069", "0x0000000000000000000000000000000000000000000000000000000000000018000000000000000000000000000000000000000000000000000000000000000f", "/ipfs/QmeQx8RybUuz9YAVm8DfQpcyQVmJgFpZmr3cUSuTiPpVW7", 3000, 7000, 5000, "0xcB4B48d2A7a44247A00048963F169d2b4Ab045a6", "0x9A3D64E386C18Cb1d6d5179a9596A4B5736e98A6"], "numDeployments": 1, "solcInputHash": "df6967adfff140ad55877edd79fe5918", "metadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_winnerMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserAppealPeriodMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_homeProxy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_messenger\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Contribution\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"RulingFunded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_ruling\",\"type\":\"bytes32\"}],\"name\":\"RulingRelayed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_reward\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"META_EVIDENCE_ID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MULTIPLIER_DIVISOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REFUSE_TO_ARBITRATE_REALITIO\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationCreatedBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignProxyOptimism.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"},{\"internalType\":\"uint256\",\"name\":\"disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"answer\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"disputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_externalDisputeID\",\"type\":\"uint256\"}],\"name\":\"externalIDtoLocalID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"fundAppeal\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"}],\"name\":\"getContributionsToSuccessfulFundings\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"contributions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"getFundingStatus\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"fullyFunded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMultipliers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"winner\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loser\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loserAppealPeriod\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"divisor\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"}],\"name\":\"getNumberOfRounds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"paidFees\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"feeRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"getTotalWithdrawableAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sum\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserAppealPeriodMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"messenger\",\"outputs\":[{\"internalType\":\"contract ICrossDomainMessenger\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minGasLimit\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"numberOfRulingOptions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"questionIDToArbitrationID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_evidenceURI\",\"type\":\"string\"}],\"name\":\"submitEvidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"winnerMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewardsForAllRounds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"https://docs.optimism.io/builders/app-developers/bridging/messaging\",\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationCreated(bytes32,address,uint256)\":{\"params\":{\"_disputeID\":\"The ID of the dispute.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationFailed(bytes32,address)\":{\"details\":\"This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.\",\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationRequested(bytes32,address,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"Contribution(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contribution is made, inside fundAppeal function.\",\"params\":{\"_amount\":\"Contribution amount.\",\"_contributor\":\"Caller of fundAppeal function.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"The round number the contribution was made to.\",\"ruling\":\"Indicates the ruling option which got the contribution.\"}},\"Dispute(address,uint256,uint256,uint256)\":{\"details\":\"To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_evidenceGroupID\":\"Unique identifier of the evidence group that is linked to this dispute.\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Evidence(address,uint256,address,string)\":{\"details\":\"To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_evidence\":\"A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\",\"_evidenceGroupID\":\"Unique identifier of the evidence group the evidence belongs to.\",\"_party\":\"The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\"}},\"MetaEvidence(uint256,string)\":{\"details\":\"To be emitted when meta-evidence is submitted.\",\"params\":{\"_evidence\":\"A link to the meta-evidence JSON.\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Ruling(address,uint256,uint256)\":{\"details\":\"To be raised when a ruling is given.\",\"params\":{\"_arbitrator\":\"The arbitrator giving the ruling.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_ruling\":\"The ruling which was given.\"}},\"RulingFunded(uint256,uint256,uint256)\":{\"details\":\"To be raised when a ruling option is fully funded for appeal.\",\"params\":{\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"Number of the round this ruling option was fully funded in.\",\"_ruling\":\"The ruling option which just got fully funded.\"}},\"RulingRelayed(bytes32,bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question with the ruling to relay.\",\"_ruling\":\"Ruling converted into Realitio format.\"}},\"Withdrawal(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contributor withdraws non-zero value.\",\"params\":{\"_contributor\":\"The beneficiary of withdrawal.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_reward\":\"Total amount of withdrawal, consists of reimbursed deposits plus rewards.\",\"_round\":\"The round number the withdrawal was made from.\",\"_ruling\":\"Indicates the ruling option which contributor gets rewards from.\"}}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_homeProxy\":\"Proxy on L2.\",\"_loserAppealPeriodMultiplier\":\"Multiplier for calculating the appeal period for the losing answer.\",\"_loserMultiplier\":\"Multiplier for calculating the appeal cost of the losing answer.\",\"_messenger\":\"contract for L1 -> L2 tx\",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_winnerMultiplier\":\"Multiplier for calculating the appeal cost of the winning answer.\"}},\"externalIDtoLocalID(uint256)\":{\"params\":{\"_externalDisputeID\":\"Dispute id as in arbitrator side.\"},\"returns\":{\"_0\":\"localDisputeID Dispute id as in arbitrable contract.\"}},\"fundAppeal(uint256,uint256)\":{\"params\":{\"_answer\":\"One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.\",\"_arbitrationID\":\"The ID of the arbitration, which is questionID cast into uint256.\"},\"returns\":{\"_0\":\"Whether the answer was fully funded or not.\"}},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_contributor\":\"The address whose contributions to query.\",\"_round\":\"The round to query.\"},\"returns\":{\"contributions\":\"The amount contributed to each funded answer by the contributor.\",\"fundedAnswers\":\"IDs of the answers that are fully funded.\"}},\"getDisputeFee(bytes32)\":{\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"getFundingStatus(uint256,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer choice to get funding status for.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"fullyFunded\":\"Whether the answer is fully funded or not.\",\"raised\":\"The amount paid for this answer.\"}},\"getMultipliers()\":{\"returns\":{\"divisor\":\"Multiplier divisor.\",\"loser\":\"Losers stake multiplier.\",\"loserAppealPeriod\":\"Multiplier for calculating an appeal period duration for the losing side.\",\"winner\":\"Winners stake multiplier.\"}},\"getNumberOfRounds(uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\"},\"returns\":{\"_0\":\"The number of rounds.\"}},\"getRoundInfo(uint256,uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"feeRewards\":\"The amount of fees that will be used as rewards.\",\"fundedAnswers\":\"IDs of fully funded answers.\",\"paidFees\":\"The amount of fees paid for each fully funded answer.\"}},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The contributor for which to query.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"},\"returns\":{\"sum\":\"The total amount available to withdraw.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"numberOfRulingOptions(uint256)\":{\"returns\":{\"_0\":\"count The number of ruling options.\"}},\"questionIDToArbitrationID(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"},\"returns\":{\"_0\":\"The ID of the arbitration.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}},\"submitEvidence(uint256,string)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\",\"_evidenceURI\":\"Link to evidence.\"}},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer to query the reward from.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address to send reward to.\",\"_round\":\"The round from which to withdraw.\"},\"returns\":{\"reward\":\"The withdrawn amount.\"}},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address that made contributions.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"}}},\"title\":\"Arbitration proxy for Realitio on foreign chain (eg. mainnet).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"},\"RulingRelayed(bytes32,bytes32)\":{\"notice\":\"Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain (L1).\"},\"externalIDtoLocalID(uint256)\":{\"notice\":\"Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\"},\"fundAppeal(uint256,uint256)\":{\"notice\":\"Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\"},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"notice\":\"Gets contributions to the answers that are fully funded.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"getFundingStatus(uint256,uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question for a specific answer choice.\"},\"getMultipliers()\":{\"notice\":\"Returns stake multipliers.\"},\"getNumberOfRounds(uint256)\":{\"notice\":\"Gets the number of rounds of the specific question.\"},\"getRoundInfo(uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question.\"},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"notice\":\"Returns the sum of withdrawable amount.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover Optimism fees.\"},\"numberOfRulingOptions(uint256)\":{\"notice\":\"Returns number of possible ruling options. Valid rulings are [0, return value].\"},\"questionIDToArbitrationID(bytes32)\":{\"notice\":\"Casts question ID into uint256 thus returning the related arbitration ID.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute. Can only be called by the arbitrator.\"},\"submitEvidence(uint256,string)\":{\"notice\":\"Allows to submit evidence for a particular question.\"},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"notice\":\"Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\"},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"notice\":\"Allows to withdraw any rewards or reimbursable fees for all rounds at once.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/0.8/RealitioForeignProxyOptimism.sol\":\"RealitioForeignProxyOptimism\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/dispute-resolver-interface-contract-0.8/contracts/IDisputeResolver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@ferittuncer]\\n *  @reviewers: [@mtsalenc*, @hbarcelos*, @unknownunknown1, @MerlinEgalite, @fnanni-0*, @shalzz]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\n\\n/**\\n *  @title This serves as a standard interface for crowdfunded appeals and evidence submission, which aren't a part of the arbitration (erc-792 and erc-1497) standard yet.\\n    This interface is used in Dispute Resolver (resolve.kleros.io).\\n */\\nabstract contract IDisputeResolver is IArbitrable, IEvidence {\\n    string public constant VERSION = \\\"2.0.0\\\"; // Can be used to distinguish between multiple deployed versions, if necessary.\\n\\n    /** @dev Raised when a contribution is made, inside fundAppeal function.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the contribution was made to.\\n     *  @param ruling Indicates the ruling option which got the contribution.\\n     *  @param _contributor Caller of fundAppeal function.\\n     *  @param _amount Contribution amount.\\n     */\\n    event Contribution(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 ruling, address indexed _contributor, uint256 _amount);\\n\\n    /** @dev Raised when a contributor withdraws non-zero value.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the withdrawal was made from.\\n     *  @param _ruling Indicates the ruling option which contributor gets rewards from.\\n     *  @param _contributor The beneficiary of withdrawal.\\n     *  @param _reward Total amount of withdrawal, consists of reimbursed deposits plus rewards.\\n     */\\n    event Withdrawal(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 _ruling, address indexed _contributor, uint256 _reward);\\n\\n    /** @dev To be raised when a ruling option is fully funded for appeal.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round Number of the round this ruling option was fully funded in.\\n     *  @param _ruling The ruling option which just got fully funded.\\n     */\\n    event RulingFunded(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 indexed _ruling);\\n\\n    /** @dev Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     *  @param _externalDisputeID Dispute id as in arbitrator contract.\\n     *  @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external virtual returns (uint256 localDisputeID);\\n\\n    /** @dev Returns number of possible ruling options. Valid rulings are [0, return value].\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 _localDisputeID) external view virtual returns (uint256 count);\\n\\n    /** @dev Allows to submit evidence for a given dispute.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _evidenceURI IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    function submitEvidence(uint256 _localDisputeID, string calldata _evidenceURI) external virtual;\\n\\n    /** @dev Manages contributions and calls appeal function of the specified arbitrator to appeal a dispute. This function lets appeals be crowdfunded.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _ruling The ruling option to which the caller wants to contribute.\\n     *  @return fullyFunded True if the ruling option got fully funded as a result of this contribution.\\n     */\\n    function fundAppeal(uint256 _localDisputeID, uint256 _ruling) external payable virtual returns (bool fullyFunded);\\n\\n    /** @dev Returns appeal multipliers.\\n     *  @return winnerStakeMultiplier Winners stake multiplier.\\n     *  @return loserStakeMultiplier Losers stake multiplier.\\n     *  @return loserAppealPeriodMultiplier Losers appeal period multiplier. The loser is given less time to fund its appeal to defend against last minute appeal funding attacks.\\n     *  @return denominator Multiplier denominator in basis points.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        virtual\\n        returns (\\n            uint256 winnerStakeMultiplier,\\n            uint256 loserStakeMultiplier,\\n            uint256 loserAppealPeriodMultiplier,\\n            uint256 denominator\\n        );\\n\\n    /** @dev Allows to withdraw any reimbursable fees or rewards after the dispute gets resolved.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _round Number of the round that caller wants to execute withdraw on.\\n     *  @param _ruling A ruling option that caller wants to execute withdraw on.\\n     *  @return sum The amount that is going to be transferred to contributor as a result of this function call.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _round,\\n        uint256 _ruling\\n    ) external virtual returns (uint256 sum);\\n\\n    /** @dev Allows to withdraw any rewards or reimbursable fees after the dispute gets resolved for all rounds at once.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to execute withdraw on.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external virtual;\\n\\n    /** @dev Returns the sum of withdrawable amount.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to get withdrawable amount from.\\n     *  @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external view virtual returns (uint256 sum);\\n}\\n\",\"keccak256\":\"0x9174a37ba69e682381a3ae6e14582a17d69f29be879ff27433fce2b971f871ae\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0x1803a3433a78c509b20bd9477a2c60a71b2ce1ee7e17eb0ef0601618a8a72526\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {Waiting, Appealable, Solved}\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0x240a4142f9ec379da0333dfc82409b7b058cff9ea118368eb5e8f15447996c1e\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence A link to the meta-evidence JSON.\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0x1ccedf5213730632540c748486637d7b1977ee73375818bf498a8276ca49dd13\",\"license\":\"MIT\"},\"src/0.8/RealitioForeignProxyOptimism.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@anmol-dhiman]\\n *  @reviewers: []\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity 0.8.25;\\n\\nimport {IDisputeResolver, IArbitrator} from \\\"@kleros/dispute-resolver-interface-contract-0.8/contracts/IDisputeResolver.sol\\\";\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./interfaces/IArbitrationProxies.sol\\\";\\nimport {ICrossDomainMessenger} from \\\"./interfaces/optimism/ICrossDomainMessenger.sol\\\";\\n\\n/**\\n * @title Arbitration proxy for Realitio on foreign chain (eg. mainnet).\\n * @dev https://docs.optimism.io/builders/app-developers/bridging/messaging\\n */\\ncontract RealitioForeignProxyOptimism is IForeignArbitrationProxy, IDisputeResolver {\\n    /* Constants */\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max; // The number of choices for the arbitrator.\\n    uint256 public constant REFUSE_TO_ARBITRATE_REALITIO = type(uint256).max; // Constant that represents \\\"Refuse to rule\\\" in realitio format.\\n    uint256 public constant MULTIPLIER_DIVISOR = 10000; // Divisor parameter for multipliers.\\n    uint256 public constant META_EVIDENCE_ID = 0; // The ID of the MetaEvidence for disputes.\\n\\n    /* Storage */\\n\\n    enum Status {\\n        None,\\n        Requested,\\n        Created,\\n        Ruled,\\n        Failed\\n    }\\n\\n    struct ArbitrationRequest {\\n        Status status; // Status of the arbitration.\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\n        uint256 disputeID; // The ID of the dispute in arbitrator contract.\\n        uint256 answer; // The answer given by the arbitrator.\\n        Round[] rounds; // Tracks each appeal round of a dispute.\\n    }\\n\\n    struct DisputeDetails {\\n        uint256 arbitrationID; // The ID of the arbitration.\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\n    }\\n\\n    // Round struct stores the contributions made to particular answers.\\n    struct Round {\\n        mapping(uint256 => uint256) paidFees; // Tracks the fees paid in this round in the form paidFees[answer].\\n        mapping(uint256 => bool) hasPaid; // True if the fees for this particular answer have been fully paid in the form hasPaid[answer].\\n        mapping(address => mapping(uint256 => uint256)) contributions; // Maps contributors to their contributions for each answer in the form contributions[address][answer].\\n        uint256 feeRewards; // Sum of reimbursable appeal fees available to the parties that made contributions to the answer that ultimately wins a dispute.\\n        uint256[] fundedAnswers; // Stores the answer choices that are fully funded.\\n    }\\n\\n    // contract for L1 -> L2 communication\\n    ICrossDomainMessenger public immutable messenger;\\n    uint32 public immutable minGasLimit = 200000; // Gas limit of the transaction call on L2. Note that setting value too high results in high gas estimation fee (tested on Sepolia).\\n    address public immutable homeProxy; // Proxy on L2.\\n\\n    IArbitrator public immutable arbitrator; // The address of the arbitrator. TRUSTED.\\n    bytes public arbitratorExtraData; // The extra data used to raise a dispute in the arbitrator.\\n\\n    // Multipliers are in basis points.\\n    uint256 public immutable winnerMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that was chosen by the arbitrator in the previous round.\\n    uint256 public immutable loserMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that the arbitrator didn't rule for in the previous round.\\n    uint256 public immutable loserAppealPeriodMultiplier; // Multiplier for calculating the duration of the appeal period for the loser, in basis points.\\n\\n    mapping(uint256 => mapping(address => ArbitrationRequest)) public arbitrationRequests; // Maps arbitration ID to its data. arbitrationRequests[uint(questionID)][requester].\\n    mapping(uint256 => DisputeDetails) public disputeIDToDisputeDetails; // Maps external dispute ids to local arbitration ID and requester who was able to complete the arbitration request.\\n    mapping(uint256 => bool) public arbitrationIDToDisputeExists; // Whether a dispute has already been created for the given arbitration ID or not.\\n\\n    mapping(uint256 => address) public arbitrationIDToRequester; // Maps arbitration ID to the requester who was able to complete the arbitration request.\\n    mapping(uint256 => uint256) public arbitrationCreatedBlock; // Block of dispute creation.\\n\\n    modifier onlyHomeProxy() {\\n        require(msg.sender == address(messenger), \\\"NOT_MESSENGER\\\");\\n        require(messenger.xDomainMessageSender() == homeProxy, \\\"Can only be called by Home proxy\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @notice Creates an arbitration proxy on the foreign chain (L1).\\n     * @param _arbitrator Arbitrator contract address.\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\n     * @param _metaEvidence The URI of the meta evidence file.\\n     * @param _winnerMultiplier Multiplier for calculating the appeal cost of the winning answer.\\n     * @param _loserMultiplier Multiplier for calculating the appeal cost of the losing answer.\\n     * @param _loserAppealPeriodMultiplier Multiplier for calculating the appeal period for the losing answer.\\n     * @param _homeProxy Proxy on L2.\\n     * @param _messenger contract for L1 -> L2 tx\\n     */\\n    constructor(\\n        IArbitrator _arbitrator,\\n        bytes memory _arbitratorExtraData,\\n        string memory _metaEvidence,\\n        uint256 _winnerMultiplier,\\n        uint256 _loserMultiplier,\\n        uint256 _loserAppealPeriodMultiplier,\\n        address _homeProxy,\\n        address _messenger\\n    ) {\\n        arbitrator = _arbitrator;\\n        arbitratorExtraData = _arbitratorExtraData;\\n        winnerMultiplier = _winnerMultiplier;\\n        loserMultiplier = _loserMultiplier;\\n        loserAppealPeriodMultiplier = _loserAppealPeriodMultiplier;\\n        homeProxy = _homeProxy;\\n        messenger = ICrossDomainMessenger(_messenger);\\n\\n        emit MetaEvidence(META_EVIDENCE_ID, _metaEvidence);\\n    }\\n\\n    /*//////////////////////////////////////////////////////////////\\n                             REALITIO LOGIC\\n    //////////////////////////////////////////////////////////////*/\\n\\n    /**\\n     * @notice Requests arbitration for the given question and contested answer.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\n        require(!arbitrationIDToDisputeExists[uint256(_questionID)], \\\"Dispute already created\\\");\\n\\n        ArbitrationRequest storage arbitration = arbitrationRequests[uint256(_questionID)][msg.sender];\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationRequest.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\n\\n        require(msg.value >= arbitrationCost, \\\"Deposit value too low\\\");\\n\\n        arbitration.status = Status.Requested;\\n        arbitration.deposit = uint248(msg.value);\\n\\n        messenger.sendMessage(homeProxy, data, minGasLimit);\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\n    }\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The requester.\\n     */\\n    function receiveArbitrationAcknowledgement(\\n        bytes32 _questionID,\\n        address _requester\\n    ) external override onlyHomeProxy {\\n        uint256 arbitrationID = uint256(_questionID);\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\n\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\n        if (arbitration.deposit >= arbitrationCost) {\\n            try\\n                arbitrator.createDispute{value: arbitrationCost}(NUMBER_OF_CHOICES_FOR_ARBITRATOR, arbitratorExtraData)\\n            returns (uint256 disputeID) {\\n                DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[disputeID];\\n                disputeDetails.arbitrationID = arbitrationID;\\n                disputeDetails.requester = _requester;\\n\\n                arbitrationIDToDisputeExists[arbitrationID] = true;\\n                arbitrationIDToRequester[arbitrationID] = _requester;\\n                arbitrationCreatedBlock[disputeID] = block.number;\\n\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\n\\n                arbitration.status = Status.Created;\\n                arbitration.deposit = 0;\\n                arbitration.disputeID = disputeID;\\n                arbitration.rounds.push();\\n\\n                if (remainder > 0) {\\n                    payable(_requester).send(remainder);\\n                }\\n\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\n                emit Dispute(arbitrator, disputeID, META_EVIDENCE_ID, arbitrationID);\\n            } catch {\\n                arbitration.status = Status.Failed;\\n                emit ArbitrationFailed(_questionID, _requester);\\n            }\\n        } else {\\n            arbitration.status = Status.Failed;\\n            emit ArbitrationFailed(_questionID, _requester);\\n        }\\n    }\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external override onlyHomeProxy {\\n        uint256 arbitrationID = uint256(_questionID);\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\n        uint256 deposit = arbitration.deposit;\\n\\n        delete arbitrationRequests[arbitrationID][_requester];\\n        payable(_requester).send(deposit);\\n\\n        emit ArbitrationCanceled(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover Optimism fees.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable override {\\n        uint256 arbitrationID = uint256(_questionID);\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationFailure.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\n\\n        uint256 deposit = arbitration.deposit;\\n\\n        delete arbitrationRequests[arbitrationID][_requester];\\n\\n        payable(_requester).send(deposit);\\n\\n        messenger.sendMessage(homeProxy, data, minGasLimit);\\n        emit ArbitrationCanceled(_questionID, _requester);\\n    }\\n\\n    // ********************************* //\\n    // *    Appeals and arbitration    * //\\n    // ********************************* //\\n\\n    /**\\n     * @notice Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\\n     * @param _arbitrationID The ID of the arbitration, which is questionID cast into uint256.\\n     * @param _answer One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question.\\n     * Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format.\\n     * Also note that '0' answer can be funded.\\n     * @return Whether the answer was fully funded or not.\\n     */\\n    function fundAppeal(uint256 _arbitrationID, uint256 _answer) external payable override returns (bool) {\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][\\n            arbitrationIDToRequester[_arbitrationID]\\n        ];\\n        require(arbitration.status == Status.Created, \\\"No dispute to appeal.\\\");\\n\\n        uint256 disputeID = arbitration.disputeID;\\n        (uint256 appealPeriodStart, uint256 appealPeriodEnd) = arbitrator.appealPeriod(disputeID);\\n        require(block.timestamp >= appealPeriodStart && block.timestamp < appealPeriodEnd, \\\"Appeal period is over.\\\");\\n\\n        uint256 multiplier;\\n        {\\n            uint256 winner = arbitrator.currentRuling(disputeID);\\n            if (winner == _answer) {\\n                multiplier = winnerMultiplier;\\n            } else {\\n                require(\\n                    block.timestamp - appealPeriodStart <\\n                        ((appealPeriodEnd - appealPeriodStart) * (loserAppealPeriodMultiplier)) / MULTIPLIER_DIVISOR,\\n                    \\\"Appeal period is over for loser\\\"\\n                );\\n                multiplier = loserMultiplier;\\n            }\\n        }\\n\\n        uint256 lastRoundID = arbitration.rounds.length - 1;\\n        Round storage round = arbitration.rounds[lastRoundID];\\n        require(!round.hasPaid[_answer], \\\"Appeal fee is already paid.\\\");\\n        uint256 appealCost = arbitrator.appealCost(disputeID, arbitratorExtraData);\\n        uint256 totalCost = appealCost + ((appealCost * multiplier) / MULTIPLIER_DIVISOR);\\n\\n        // Take up to the amount necessary to fund the current round at the current costs.\\n        uint256 contribution = totalCost - (round.paidFees[_answer]) > msg.value\\n            ? msg.value\\n            : totalCost - (round.paidFees[_answer]);\\n        emit Contribution(_arbitrationID, lastRoundID, _answer, msg.sender, contribution);\\n\\n        round.contributions[msg.sender][_answer] += contribution;\\n        round.paidFees[_answer] += contribution;\\n        if (round.paidFees[_answer] >= totalCost) {\\n            round.feeRewards += round.paidFees[_answer];\\n            round.fundedAnswers.push(_answer);\\n            round.hasPaid[_answer] = true;\\n            emit RulingFunded(_arbitrationID, lastRoundID, _answer);\\n        }\\n\\n        if (round.fundedAnswers.length > 1) {\\n            // At least two sides are fully funded.\\n            arbitration.rounds.push();\\n\\n            round.feeRewards = round.feeRewards - appealCost;\\n            arbitrator.appeal{value: appealCost}(disputeID, arbitratorExtraData);\\n        }\\n\\n        if (msg.value - contribution > 0) payable(msg.sender).send(msg.value - contribution); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\n        return round.hasPaid[_answer];\\n    }\\n\\n    /**\\n     * @notice Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _beneficiary The address to send reward to.\\n     * @param _round The round from which to withdraw.\\n     * @param _answer The answer to query the reward from.\\n     * @return reward The withdrawn amount.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _arbitrationID,\\n        address payable _beneficiary,\\n        uint256 _round,\\n        uint256 _answer\\n    ) public override returns (uint256 reward) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\n        // Allow to reimburse if funding of the round was unsuccessful.\\n        if (!round.hasPaid[_answer]) {\\n            reward = round.contributions[_beneficiary][_answer];\\n        } else if (!round.hasPaid[arbitration.answer]) {\\n            // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\n            // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\n            reward = round.fundedAnswers.length > 1\\n                ? (round.contributions[_beneficiary][_answer] * round.feeRewards) /\\n                    (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\n                : 0;\\n        } else if (arbitration.answer == _answer) {\\n            uint256 paidFees = round.paidFees[_answer];\\n            // Reward the winner.\\n            reward = paidFees > 0 ? (round.contributions[_beneficiary][_answer] * round.feeRewards) / paidFees : 0;\\n        }\\n\\n        if (reward != 0) {\\n            round.contributions[_beneficiary][_answer] = 0;\\n            _beneficiary.send(reward); // It is the user's responsibility to accept ETH.\\n            emit Withdrawal(_arbitrationID, _round, _answer, _beneficiary, reward);\\n        }\\n    }\\n\\n    /**\\n     * @notice Allows to withdraw any rewards or reimbursable fees for all rounds at once.\\n     * @dev This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.\\n     *      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _beneficiary The address that made contributions.\\n     * @param _contributedTo Answer that received contributions from contributor.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _arbitrationID,\\n        address payable _beneficiary,\\n        uint256 _contributedTo\\n    ) external override {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n\\n        uint256 numberOfRounds = arbitration.rounds.length;\\n        for (uint256 roundNumber = 0; roundNumber < numberOfRounds; roundNumber++) {\\n            withdrawFeesAndRewards(_arbitrationID, _beneficiary, roundNumber, _contributedTo);\\n        }\\n    }\\n\\n    /**\\n     * @notice Allows to submit evidence for a particular question.\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\n     * @param _evidenceURI Link to evidence.\\n     */\\n    function submitEvidence(uint256 _arbitrationID, string calldata _evidenceURI) external override {\\n        emit Evidence(arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\n    }\\n\\n    /**\\n     * @notice Rules a specified dispute. Can only be called by the arbitrator.\\n     * @dev Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\n     * @param _ruling The ruling given by the arbitrator.\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external override {\\n        DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[_disputeID];\\n        uint256 arbitrationID = disputeDetails.arbitrationID;\\n        address requester = disputeDetails.requester;\\n\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][requester];\\n        require(msg.sender == address(arbitrator), \\\"Only arbitrator allowed\\\");\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\n        uint256 finalRuling = _ruling;\\n\\n        // If one side paid its fees, the ruling is in its favor. Note that if the other side had also paid, an appeal would have been created.\\n        Round storage round = arbitration.rounds[arbitration.rounds.length - 1];\\n        if (round.fundedAnswers.length == 1) finalRuling = round.fundedAnswers[0];\\n\\n        arbitration.answer = finalRuling;\\n        arbitration.status = Status.Ruled;\\n\\n        // Realitio ruling is shifted by 1 compared to Kleros.\\n        uint256 realitioRuling = finalRuling != 0 ? finalRuling - 1 : REFUSE_TO_ARBITRATE_REALITIO;\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationAnswer.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, bytes32(arbitrationID), bytes32(realitioRuling));\\n        messenger.sendMessage(homeProxy, data, minGasLimit);\\n\\n        emit Ruling(arbitrator, _disputeID, finalRuling);\\n    }\\n\\n    // ********************************* //\\n    // *    External View Functions    * //\\n    // ********************************* //\\n\\n    /**\\n     * @notice Returns stake multipliers.\\n     * @return winner Winners stake multiplier.\\n     * @return loser Losers stake multiplier.\\n     * @return loserAppealPeriod Multiplier for calculating an appeal period duration for the losing side.\\n     * @return divisor Multiplier divisor.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        override\\n        returns (uint256 winner, uint256 loser, uint256 loserAppealPeriod, uint256 divisor)\\n    {\\n        return (winnerMultiplier, loserMultiplier, loserAppealPeriodMultiplier, MULTIPLIER_DIVISOR);\\n    }\\n\\n    /**\\n     * @notice Returns number of possible ruling options. Valid rulings are [0, return value].\\n     * @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 /* _arbitrationID */) external pure override returns (uint256) {\\n        return NUMBER_OF_CHOICES_FOR_ARBITRATOR;\\n    }\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 /* _questionID */) external view override returns (uint256) {\\n        return arbitrator.arbitrationCost(arbitratorExtraData);\\n    }\\n\\n    /**\\n     * @notice Gets the number of rounds of the specific question.\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\n     * @return The number of rounds.\\n     */\\n    function getNumberOfRounds(uint256 _arbitrationID) external view returns (uint256) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        return arbitration.rounds.length;\\n    }\\n\\n    /**\\n     * @notice Gets the information of a round of a question.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _round The round to query.\\n     * @return paidFees The amount of fees paid for each fully funded answer.\\n     * @return feeRewards The amount of fees that will be used as rewards.\\n     * @return fundedAnswers IDs of fully funded answers.\\n     */\\n    function getRoundInfo(\\n        uint256 _arbitrationID,\\n        uint256 _round\\n    ) external view returns (uint256[] memory paidFees, uint256 feeRewards, uint256[] memory fundedAnswers) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n        fundedAnswers = round.fundedAnswers;\\n\\n        paidFees = new uint256[](round.fundedAnswers.length);\\n\\n        for (uint256 i = 0; i < round.fundedAnswers.length; i++) {\\n            paidFees[i] = round.paidFees[round.fundedAnswers[i]];\\n        }\\n\\n        feeRewards = round.feeRewards;\\n    }\\n\\n    /**\\n     * @notice Gets the information of a round of a question for a specific answer choice.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _round The round to query.\\n     * @param _answer The answer choice to get funding status for.\\n     * @return raised The amount paid for this answer.\\n     * @return fullyFunded Whether the answer is fully funded or not.\\n     */\\n    function getFundingStatus(\\n        uint256 _arbitrationID,\\n        uint256 _round,\\n        uint256 _answer\\n    ) external view returns (uint256 raised, bool fullyFunded) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n\\n        raised = round.paidFees[_answer];\\n        fullyFunded = round.hasPaid[_answer];\\n    }\\n\\n    /**\\n     * @notice Gets contributions to the answers that are fully funded.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _round The round to query.\\n     * @param _contributor The address whose contributions to query.\\n     * @return fundedAnswers IDs of the answers that are fully funded.\\n     * @return contributions The amount contributed to each funded answer by the contributor.\\n     */\\n    function getContributionsToSuccessfulFundings(\\n        uint256 _arbitrationID,\\n        uint256 _round,\\n        address _contributor\\n    ) external view returns (uint256[] memory fundedAnswers, uint256[] memory contributions) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n\\n        fundedAnswers = round.fundedAnswers;\\n        contributions = new uint256[](round.fundedAnswers.length);\\n\\n        for (uint256 i = 0; i < contributions.length; i++) {\\n            contributions[i] = round.contributions[_contributor][fundedAnswers[i]];\\n        }\\n    }\\n\\n    /**\\n     * @notice Returns the sum of withdrawable amount.\\n     * @dev This function is O(n) where n is the total number of rounds.\\n     * @dev This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _beneficiary The contributor for which to query.\\n     * @param _contributedTo Answer that received contributions from contributor.\\n     * @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _arbitrationID,\\n        address payable _beneficiary,\\n        uint256 _contributedTo\\n    ) external view override returns (uint256 sum) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        if (arbitration.status < Status.Ruled) return sum;\\n\\n        uint256 finalAnswer = arbitration.answer;\\n        uint256 noOfRounds = arbitration.rounds.length;\\n        for (uint256 roundNumber = 0; roundNumber < noOfRounds; roundNumber++) {\\n            Round storage round = arbitration.rounds[roundNumber];\\n\\n            if (!round.hasPaid[_contributedTo]) {\\n                // Allow to reimburse if funding was unsuccessful for this answer option.\\n                sum += round.contributions[_beneficiary][_contributedTo];\\n            } else if (!round.hasPaid[finalAnswer]) {\\n                // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\n                // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\n                sum += round.fundedAnswers.length > 1\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) /\\n                        (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\n                    : 0;\\n            } else if (finalAnswer == _contributedTo) {\\n                uint256 paidFees = round.paidFees[_contributedTo];\\n                // Reward the winner.\\n                sum += paidFees > 0\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) / paidFees\\n                    : 0;\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @notice Casts question ID into uint256 thus returning the related arbitration ID.\\n     * @param _questionID The ID of the question.\\n     * @return The ID of the arbitration.\\n     */\\n    function questionIDToArbitrationID(bytes32 _questionID) external pure returns (uint256) {\\n        return uint256(_questionID);\\n    }\\n\\n    /**\\n     * @notice Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     * @param _externalDisputeID Dispute id as in arbitrator side.\\n     * @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external view override returns (uint256) {\\n        return disputeIDToDisputeDetails[_externalDisputeID].arbitrationID;\\n    }\\n}\\n\",\"keccak256\":\"0xfa0f29895a959ca4b6712c65a918c1b89fa32f48601faefe2885d7017f36dbf4\",\"license\":\"MIT\"},\"src/0.8/interfaces/IArbitrationProxies.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity 0.8.25;\\n\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\n\\ninterface IHomeArbitrationProxy {\\n    /**\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\n     */\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice To be emitted when arbitration request is rejected.\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\n     * or if the question is already finalized.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\n     * @param _reason The reason why the request was rejected.\\n     */\\n    event RequestRejected(\\n        bytes32 indexed _questionID,\\n        address indexed _requester,\\n        uint256 _maxPrevious,\\n        string _reason\\n    );\\n\\n    /**\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the arbitration request is canceled.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\n\\n    /**\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\n     * @param _questionID The ID of the question.\\n     */\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\n\\n    /**\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function receiveArbitrationRequest(bytes32 _questionID, address _requester, uint256 _maxPrevious) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\n     * the bridge and cannot send messages back to it.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been rejected.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\n     * the bridge and cannot send messages back to it.\\n     * Reasons why the request might be rejected:\\n     *  - The question does not exist\\n     *  - The question was not answered yet\\n     *  - The quesiton bond value changed while the arbitration was being requested\\n     *  - Another request was already accepted\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\n     * @dev Currently this can happen only if the arbitration cost increased.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the answer to a specified question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\n\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\n     *  @dev Template_hashes won't be used by this home proxy. \\n     */\\n    function metadata() external view returns (string calldata);\\n}\\n\\ninterface IForeignArbitrationProxy {\\n    /**\\n     * @notice Should be emitted when the arbitration is requested.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice Should be emitted when the dispute is created.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _disputeID The ID of the dispute.\\n     */\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\n\\n    /**\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the dispute could not be created.\\n     * @dev This will happen if there is an increase in the arbitration fees\\n     * between the time the arbitration is made and the time it is acknowledged.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event.\\n     * @param _questionID The ID of the question with the ruling to relay.\\n     * @param _ruling Ruling converted into Realitio format.\\n     */\\n    event RulingRelayed(bytes32 _questionID, bytes32 _ruling);\\n\\n    /**\\n     * @notice Requests arbitration for the given question and contested answer.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable;\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @param _questionID the ID of the question.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\n}\\n\\n\",\"keccak256\":\"0x4f2dd0c7372274a2382fad70cc8165c7c970e4d859f604b8441ebfe091fa62e3\",\"license\":\"MIT\"},\"src/0.8/interfaces/optimism/ICrossDomainMessenger.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\npragma solidity 0.8.25;\\n// @dev https://github.com/ethereum-optimism/optimism/blob/v1.7.7/packages/contracts-bedrock/src/universal/CrossDomainMessenger.sol\\ninterface ICrossDomainMessenger {\\n    function sendMessage(\\n        address _target,\\n        bytes calldata _message,\\n        uint32 _gasLimit\\n    ) external;\\n\\n    function xDomainMessageSender() external view returns (address);\\n}\\n\",\"keccak256\":\"0xd43d55bf1e86eecfde1319dc0b684d133cf958d3467b14642de26e8d63cc79b6\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "https://docs.optimism.io/builders/app-developers/bridging/messaging", "events": {"ArbitrationCanceled(bytes32,address)": {"params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationCreated(bytes32,address,uint256)": {"params": {"_disputeID": "The ID of the dispute.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationFailed(bytes32,address)": {"details": "This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.", "params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationRequested(bytes32,address,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "Contribution(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contribution is made, inside fundAppeal function.", "params": {"_amount": "Contribution amount.", "_contributor": "Caller of fundAppeal function.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "The round number the contribution was made to.", "ruling": "Indicates the ruling option which got the contribution."}}, "Dispute(address,uint256,uint256,uint256)": {"details": "To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.", "params": {"_arbitrator": "The arbitrator of the contract.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_evidenceGroupID": "Unique identifier of the evidence group that is linked to this dispute.", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Evidence(address,uint256,address,string)": {"details": "To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).", "params": {"_arbitrator": "The arbitrator of the contract.", "_evidence": "A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.", "_evidenceGroupID": "Unique identifier of the evidence group the evidence belongs to.", "_party": "The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party."}}, "MetaEvidence(uint256,string)": {"details": "To be emitted when meta-evidence is submitted.", "params": {"_evidence": "A link to the meta-evidence JSON.", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Ruling(address,uint256,uint256)": {"details": "To be raised when a ruling is given.", "params": {"_arbitrator": "The arbitrator giving the ruling.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_ruling": "The ruling which was given."}}, "RulingFunded(uint256,uint256,uint256)": {"details": "To be raised when a ruling option is fully funded for appeal.", "params": {"_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "Number of the round this ruling option was fully funded in.", "_ruling": "The ruling option which just got fully funded."}}, "RulingRelayed(bytes32,bytes32)": {"params": {"_questionID": "The ID of the question with the ruling to relay.", "_ruling": "Ruling converted into Realitio format."}}, "Withdrawal(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contributor withdraws non-zero value.", "params": {"_contributor": "The beneficiary of withdrawal.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_reward": "Total amount of withdrawal, consists of reimbursed deposits plus rewards.", "_round": "The round number the withdrawal was made from.", "_ruling": "Indicates the ruling option which contributor gets rewards from."}}}, "kind": "dev", "methods": {"constructor": {"params": {"_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_homeProxy": "Proxy on L2.", "_loserAppealPeriodMultiplier": "Multiplier for calculating the appeal period for the losing answer.", "_loserMultiplier": "Multiplier for calculating the appeal cost of the losing answer.", "_messenger": "contract for L1 -> L2 tx", "_metaEvidence": "The URI of the meta evidence file.", "_winnerMultiplier": "Multiplier for calculating the appeal cost of the winning answer."}}, "externalIDtoLocalID(uint256)": {"params": {"_externalDisputeID": "Dispute id as in arbitrator side."}, "returns": {"_0": "localDisputeID Dispute id as in arbitrable contract."}}, "fundAppeal(uint256,uint256)": {"params": {"_answer": "One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.", "_arbitrationID": "The ID of the arbitration, which is questionID cast into uint256."}, "returns": {"_0": "Whether the answer was fully funded or not."}}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_contributor": "The address whose contributions to query.", "_round": "The round to query."}, "returns": {"contributions": "The amount contributed to each funded answer by the contributor.", "fundedAnswers": "IDs of the answers that are fully funded."}}, "getDisputeFee(bytes32)": {"returns": {"_0": "The fee to create a dispute."}}, "getFundingStatus(uint256,uint256,uint256)": {"params": {"_answer": "The answer choice to get funding status for.", "_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"fullyFunded": "Whether the answer is fully funded or not.", "raised": "The amount paid for this answer."}}, "getMultipliers()": {"returns": {"divisor": "Multiplier divisor.", "loser": "Losers stake multiplier.", "loserAppealPeriod": "Multiplier for calculating an appeal period duration for the losing side.", "winner": "Winners stake multiplier."}}, "getNumberOfRounds(uint256)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question."}, "returns": {"_0": "The number of rounds."}}, "getRoundInfo(uint256,uint256)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"feeRewards": "The amount of fees that will be used as rewards.", "fundedAnswers": "IDs of fully funded answers.", "paidFees": "The amount of fees paid for each fully funded answer."}}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The contributor for which to query.", "_contributedTo": "Answer that received contributions from contributor."}, "returns": {"sum": "The total amount available to withdraw."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "numberOfRulingOptions(uint256)": {"returns": {"_0": "count The number of ruling options."}}, "questionIDToArbitrationID(bytes32)": {"params": {"_questionID": "The ID of the question."}, "returns": {"_0": "The ID of the arbitration."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}, "submitEvidence(uint256,string)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question.", "_evidenceURI": "Link to evidence."}}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"params": {"_answer": "The answer to query the reward from.", "_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address to send reward to.", "_round": "The round from which to withdraw."}, "returns": {"reward": "The withdrawn amount."}}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address that made contributions.", "_contributedTo": "Answer that received contributions from contributor."}}}, "title": "Arbitration proxy for Realitio on foreign chain (eg. mainnet).", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}, "RulingRelayed(bytes32,bytes32)": {"notice": "Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the foreign chain (L1)."}, "externalIDtoLocalID(uint256)": {"notice": "Maps external (arbitrator side) dispute id to local (arbitrable) dispute id."}, "fundAppeal(uint256,uint256)": {"notice": "Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded."}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"notice": "Gets contributions to the answers that are fully funded."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "getFundingStatus(uint256,uint256,uint256)": {"notice": "Gets the information of a round of a question for a specific answer choice."}, "getMultipliers()": {"notice": "Returns stake multipliers."}, "getNumberOfRounds(uint256)": {"notice": "Gets the number of rounds of the specific question."}, "getRoundInfo(uint256,uint256)": {"notice": "Gets the information of a round of a question."}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"notice": "Returns the sum of withdrawable amount."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover Optimism fees."}, "numberOfRulingOptions(uint256)": {"notice": "Returns number of possible ruling options. Valid rulings are [0, return value]."}, "questionIDToArbitrationID(bytes32)": {"notice": "Casts question ID into uint256 thus returning the related arbitration ID."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute. Can only be called by the arbitrator."}, "submitEvidence(uint256,string)": {"notice": "Allows to submit evidence for a particular question."}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"notice": "Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner."}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"notice": "Allows to withdraw any rewards or reimbursable fees for all rounds at once."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 14023, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "arbitratorExtraData", "offset": 0, "slot": "0", "type": "t_bytes_storage"}, {"astId": 14036, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "arbitrationRequests", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)13985_storage))"}, {"astId": 14041, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "disputeIDToDisputeDetails", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_struct(DisputeDetails)13990_storage)"}, {"astId": 14045, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "arbitrationIDToDisputeExists", "offset": 0, "slot": "3", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 14049, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "arbitrationIDToRequester", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 14053, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "arbitrationCreatedBlock", "offset": 0, "slot": "5", "type": "t_mapping(t_uint256,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_struct(Round)14010_storage)dyn_storage": {"base": "t_struct(Round)14010_storage", "encoding": "dynamic_array", "label": "struct RealitioForeignProxyOptimism.Round[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_enum(Status)13971": {"encoding": "inplace", "label": "enum RealitioForeignProxyOptimism.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_uint256)"}, "t_mapping(t_address,t_struct(ArbitrationRequest)13985_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignProxyOptimism.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)13985_storage"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_uint256,t_bool)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)13985_storage))": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => mapping(address => struct RealitioForeignProxyOptimism.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)13985_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)13990_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignProxyOptimism.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)13990_storage"}, "t_mapping(t_uint256,t_uint256)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(ArbitrationRequest)13985_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyOptimism.ArbitrationRequest", "members": [{"astId": 13974, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)13971"}, {"astId": 13976, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}, {"astId": 13978, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "disputeID", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 13980, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "answer", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 13984, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "rounds", "offset": 0, "slot": "3", "type": "t_array(t_struct(Round)14010_storage)dyn_storage"}], "numberOfBytes": "128"}, "t_struct(DisputeDetails)13990_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyOptimism.DisputeDetails", "members": [{"astId": 13987, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "arbitrationID", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 13989, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_struct(Round)14010_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyOptimism.Round", "members": [{"astId": 13994, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "paidFees", "offset": 0, "slot": "0", "type": "t_mapping(t_uint256,t_uint256)"}, {"astId": 13998, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "hasPaid", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 14004, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "contributions", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))"}, {"astId": 14006, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "feeRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 14009, "contract": "src/0.8/RealitioForeignProxyOptimism.sol:RealitioForeignProxyOptimism", "label": "fundedAnswers", "offset": 0, "slot": "4", "type": "t_array(t_uint256)dyn_storage"}], "numberOfBytes": "160"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}
{"address": "0x32bcDC9776692679CfBBf8350BAd67Da13FaaA3F", "abi": [{"inputs": [{"internalType": "contract IAMB", "name": "_amb", "type": "address"}, {"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "bytes32", "name": "_homeChainId", "type": "bytes32"}, {"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "string", "name": "_termsOfService", "type": "string"}, {"internalType": "uint256", "name": "_winnerMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserAppealPeriodMultiplier", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "Contribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "RulingFunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "META_EVIDENCE_ID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTIPLIER_DIVISOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "amb", "outputs": [{"internalType": "contract IAMB", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationCreatedBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignArbitrationProxyWithAppeals.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}, {"internalType": "uint256", "name": "disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "answer", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "disputeIDToDisputeDetails", "outputs": [{"internalType": "uint256", "name": "arbitrationID", "type": "uint256"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_externalDisputeID", "type": "uint256"}], "name": "externalIDtoLocalID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "fundAppeal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "address", "name": "_contributor", "type": "address"}], "name": "getContributionsToSuccessfulFundings", "outputs": [{"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "contributions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "getFundingStatus", "outputs": [{"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "bool", "name": "fullyFunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMultipliers", "outputs": [{"internalType": "uint256", "name": "winner", "type": "uint256"}, {"internalType": "uint256", "name": "loser", "type": "uint256"}, {"internalType": "uint256", "name": "loserAppealPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "divisor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}], "name": "getNumberOfRounds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256[]", "name": "paidFees", "type": "uint256[]"}, {"internalType": "uint256", "name": "feeRewards", "type": "uint256"}, {"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "getTotalWithdrawableAmount", "outputs": [{"internalType": "uint256", "name": "sum", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "homeChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserAppealPeriodMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "numberOfRulingOptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "questionIDToArbitrationID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "string", "name": "_evidenceURI", "type": "string"}], "name": "submitEvidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "termsOfService", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "winner<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "withdrawFeesAndRewards", "outputs": [{"internalType": "uint256", "name": "reward", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "withdrawFeesAndRewardsForAllRounds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0x74149ac706021c0a7efa1fb951324bd1ee78a7cbd8f5af75f5fe00114de5ac45", "receipt": {"to": null, "from": "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "contractAddress": "0x32bcDC9776692679CfBBf8350BAd67Da13FaaA3F", "transactionIndex": 87, "gasUsed": "3255686", "logsBloom": "0x00000000000000000000000000000000000000000000000000000004000010000000000000000000000000000000000000000000000000000000000002000000000008000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x1137d17d276c5f9ca9f0f57b07a4d91e5210eea8b5a8bc3fcadbca02915248b5", "transactionHash": "0x74149ac706021c0a7efa1fb951324bd1ee78a7cbd8f5af75f5fe00114de5ac45", "logs": [{"transactionIndex": 87, "blockNumber": 21358927, "transactionHash": "0x74149ac706021c0a7efa1fb951324bd1ee78a7cbd8f5af75f5fe00114de5ac45", "address": "0x32bcDC9776692679CfBBf8350BAd67Da13FaaA3F", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000342f697066732f516d54594570754e3469716259505863366261325143635947447752564e7653466679574770446b78376b433342000000000000000000000000", "logIndex": 206, "blockHash": "0x1137d17d276c5f9ca9f0f57b07a4d91e5210eea8b5a8bc3fcadbca02915248b5"}], "blockNumber": 21358927, "cumulativeGasUsed": "10576733", "status": 1, "byzantium": true}, "args": ["0x4C36d2919e407f0Cc2Ee3c993ccF8ac26d9CE64e", "0x88Fb25D399310c07d35cB9091b8346d8b1893aa5", "0x0000000000000000000000000000000000000000000000000000000000000064", "0x988b3a538b618c7a603e1c11ab82cd16dbe28069", "0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f", "/ipfs/QmTYEpuN4iqbYPXc6ba2QCcYGDwRVNvSFfyWGpDkx7kC3B", "/ipfs/QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf", 3000, 7000, 5000], "numDeployments": 5, "solcInputHash": "93073abbd994a43dc54757b2e290b9e4", "metadata": "{\"compiler\":{\"version\":\"0.7.6+commit.7338295f\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"_amb\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_homeProxy\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_homeChainId\",\"type\":\"bytes32\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_termsOfService\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_winnerMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserAppealPeriodMultiplier\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Contribution\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"RulingFunded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_reward\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"META_EVIDENCE_ID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MULTIPLIER_DIVISOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"amb\",\"outputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationCreatedBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignArbitrationProxyWithAppeals.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"},{\"internalType\":\"uint256\",\"name\":\"disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"answer\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"disputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_externalDisputeID\",\"type\":\"uint256\"}],\"name\":\"externalIDtoLocalID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"fundAppeal\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"}],\"name\":\"getContributionsToSuccessfulFundings\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"contributions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"getFundingStatus\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"fullyFunded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMultipliers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"winner\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loser\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loserAppealPeriod\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"divisor\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"}],\"name\":\"getNumberOfRounds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"paidFees\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"feeRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"getTotalWithdrawableAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sum\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeChainId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserAppealPeriodMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"numberOfRulingOptions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"questionIDToArbitrationID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_evidenceURI\",\"type\":\"string\"}],\"name\":\"submitEvidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"termsOfService\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"winnerMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewardsForAllRounds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\",\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_amb\":\"ArbitraryMessageBridge contract address.\",\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_homeChainId\":\"The chain ID where the home proxy is deployed.\",\"_homeProxy\":\"The address of the proxy.\",\"_loserAppealPeriodMultiplier\":\"Multiplier for calculating the appeal period for the losing answer.\",\"_loserMultiplier\":\"Multiplier for calculation the appeal cost of the losing answer.\",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_termsOfService\":\"The path for the Terms of Service for Kleros as an arbitrator for Realitio.\",\"_winnerMultiplier\":\"Multiplier for calculating the appeal cost of the winning answer.\"}},\"externalIDtoLocalID(uint256)\":{\"params\":{\"_externalDisputeID\":\"Dispute id as in arbitrator side.\"},\"returns\":{\"_0\":\"localDisputeID Dispute id as in arbitrable contract.\"}},\"fundAppeal(uint256,uint256)\":{\"params\":{\"_answer\":\"One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.\",\"_arbitrationID\":\"The ID of the arbitration, which is questionID cast into uint256.\"},\"returns\":{\"_0\":\"Whether the answer was fully funded or not.\"}},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_contributor\":\"The address whose contributions to query.\",\"_round\":\"The round to query.\"},\"returns\":{\"contributions\":\"The amount contributed to each funded answer by the contributor.\",\"fundedAnswers\":\"IDs of the answers that are fully funded.\"}},\"getDisputeFee(bytes32)\":{\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"getFundingStatus(uint256,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer choice to get funding status for.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"fullyFunded\":\"Whether the answer is fully funded or not.\",\"raised\":\"The amount paid for this answer.\"}},\"getMultipliers()\":{\"returns\":{\"divisor\":\"Multiplier divisor.\",\"loser\":\"Losers stake multiplier.\",\"loserAppealPeriod\":\"Multiplier for calculating an appeal period duration for the losing side.\",\"winner\":\"Winners stake multiplier.\"}},\"getNumberOfRounds(uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\"},\"returns\":{\"_0\":\"The number of rounds.\"}},\"getRoundInfo(uint256,uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"feeRewards\":\"The amount of fees that will be used as rewards.\",\"fundedAnswers\":\"IDs of fully funded answers.\",\"paidFees\":\"The amount of fees paid for each fully funded answer.\"}},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The contributor for which to query.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"},\"returns\":{\"sum\":\"The total amount available to withdraw.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"numberOfRulingOptions(uint256)\":{\"returns\":{\"_0\":\"count The number of ruling options.\"}},\"questionIDToArbitrationID(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"},\"returns\":{\"_0\":\"The ID of the arbitration.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}},\"submitEvidence(uint256,string)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\",\"_evidenceURI\":\"Link to evidence.\"}},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer to query the reward from.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address to send reward to.\",\"_round\":\"The round from which to withdraw.\"},\"returns\":{\"reward\":\"The withdrawn amount.\"}},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address that made contributions.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"}}},\"title\":\"Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain). This version of the contract has an appeal support.\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain.\"},\"externalIDtoLocalID(uint256)\":{\"notice\":\"Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\"},\"fundAppeal(uint256,uint256)\":{\"notice\":\"Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\"},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"notice\":\"Gets contributions to the answers that are fully funded.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"getFundingStatus(uint256,uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question for a specific answer choice.\"},\"getMultipliers()\":{\"notice\":\"Returns stake multipliers.\"},\"getNumberOfRounds(uint256)\":{\"notice\":\"Gets the number of rounds of the specific question.\"},\"getRoundInfo(uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question.\"},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"notice\":\"Returns the sum of withdrawable amount.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created.\"},\"numberOfRulingOptions(uint256)\":{\"notice\":\"Returns number of possible ruling options. Valid rulings are [0, return value].\"},\"questionIDToArbitrationID(bytes32)\":{\"notice\":\"Casts question ID into uint256 thus returning the related arbitration ID.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute. Can only be called by the arbitrator.\"},\"submitEvidence(uint256,string)\":{\"notice\":\"Allows to submit evidence for a particular question.\"},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"notice\":\"Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\"},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"notice\":\"Allows to withdraw any rewards or reimbursable fees for all rounds at once.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioForeignArbitrationProxyWithAppeals.sol\":\"RealitioForeignArbitrationProxyWithAppeals\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/dispute-resolver-interface-contract/contracts/solc-0.7.x/IDisputeResolver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@ferittuncer]\\n *  @reviewers: [@mtsalenc*, @hbarcelos*, @unknownunknown1*, @MerlinEgalite*]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.7.0;\\n\\nimport \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\n\\n/**\\n *  @title This serves as a standard interface for crowdfunded appeals and evidence submission, which aren't a part of the arbitration (erc-792 and erc-1497) standard yet.\\n    This interface is used in Dispute Resolver (resolve.kleros.io).\\n */\\nabstract contract IDisputeResolver is IArbitrable, IEvidence {\\n    string public constant VERSION = \\\"2.0.0\\\"; // Can be used to distinguish between multiple deployed versions, if necessary.\\n\\n    /** @dev Raised when a contribution is made, inside fundAppeal function.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the contribution was made to.\\n     *  @param ruling Indicates the ruling option which got the contribution.\\n     *  @param _contributor Caller of fundAppeal function.\\n     *  @param _amount Contribution amount.\\n     */\\n    event Contribution(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 ruling, address indexed _contributor, uint256 _amount);\\n\\n    /** @dev Raised when a contributor withdraws non-zero value.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the withdrawal was made from.\\n     *  @param _ruling Indicates the ruling option which contributor gets rewards from.\\n     *  @param _contributor The beneficiary of withdrawal.\\n     *  @param _reward Total amount of withdrawal, consists of reimbursed deposits plus rewards.\\n     */\\n    event Withdrawal(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 _ruling, address indexed _contributor, uint256 _reward);\\n\\n    /** @dev To be raised when a ruling option is fully funded for appeal.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round Number of the round this ruling option was fully funded in.\\n     *  @param _ruling The ruling option which just got fully funded.\\n     */\\n    event RulingFunded(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 indexed _ruling);\\n\\n    /** @dev Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     *  @param _externalDisputeID Dispute id as in arbitrator contract.\\n     *  @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external virtual returns (uint256 localDisputeID);\\n\\n    /** @dev Returns number of possible ruling options. Valid rulings are [0, return value].\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 _localDisputeID) external view virtual returns (uint256 count);\\n\\n    /** @dev Allows to submit evidence for a given dispute.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _evidenceURI IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    function submitEvidence(uint256 _localDisputeID, string calldata _evidenceURI) external virtual;\\n\\n    /** @dev Manages contributions and calls appeal function of the specified arbitrator to appeal a dispute. This function lets appeals be crowdfunded.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _ruling The ruling option to which the caller wants to contribute.\\n     *  @return fullyFunded True if the ruling option got fully funded as a result of this contribution.\\n     */\\n    function fundAppeal(uint256 _localDisputeID, uint256 _ruling) external payable virtual returns (bool fullyFunded);\\n\\n    /** @dev Returns appeal multipliers.\\n     *  @return winnerStakeMultiplier Winners stake multiplier.\\n     *  @return loserStakeMultiplier Losers stake multiplier.\\n     *  @return loserAppealPeriodMultiplier Losers appeal period multiplier. The loser is given less time to fund its appeal to defend against last minute appeal funding attacks.\\n     *  @return denominator Multiplier denominator in basis points.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        virtual\\n        returns (\\n            uint256 winnerStakeMultiplier,\\n            uint256 loserStakeMultiplier,\\n            uint256 loserAppealPeriodMultiplier,\\n            uint256 denominator\\n        );\\n\\n    /** @dev Allows to withdraw any reimbursable fees or rewards after the dispute gets resolved.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _round Number of the round that caller wants to execute withdraw on.\\n     *  @param _ruling A ruling option that caller wants to execute withdraw on.\\n     *  @return sum The amount that is going to be transferred to contributor as a result of this function call.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _round,\\n        uint256 _ruling\\n    ) external virtual returns (uint256 sum);\\n\\n    /** @dev Allows to withdraw any rewards or reimbursable fees after the dispute gets resolved. For multiple rulings options and for all rounds at once.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to execute withdraw on.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external virtual;\\n\\n    /** @dev Returns the sum of withdrawable amount.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to get withdrawable amount from.\\n     *  @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external view virtual returns (uint256 sum);\\n}\\n\",\"keccak256\":\"0x4775f15699805c40aefdb7b4cc4781553c7c5c0861dee3a5a7d98c8a640858f0\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0x1803a3433a78c509b20bd9477a2c60a71b2ce1ee7e17eb0ef0601618a8a72526\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {Waiting, Appealable, Solved}\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0x240a4142f9ec379da0333dfc82409b7b058cff9ea118368eb5e8f15447996c1e\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence A link to the meta-evidence JSON.\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0x1ccedf5213730632540c748486637d7b1977ee73375818bf498a8276ca49dd13\",\"license\":\"MIT\"},\"@kleros/ethereum-libraries/contracts/CappedMath.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n * @authors: [@mtsalenc, @hbarcelos]\\n * @reviewers: [@clesaege*, @ferittuncer]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity ^0.7.6;\\n\\n\\n/**\\n * @title CappedMath\\n * @dev Math operations with caps for under and overflow.\\n */\\nlibrary CappedMath {\\n    uint constant private UINT_MAX = 2**256 - 1;\\n\\n    /**\\n     * @dev Adds two unsigned integers, returns 2^256 - 1 on overflow.\\n     */\\n    function addCap(uint _a, uint _b) internal pure returns (uint) {\\n        uint c = _a + _b;\\n        return c >= _a ? c : UINT_MAX;\\n    }\\n\\n    /**\\n     * @dev Subtracts two integers, returns 0 on underflow.\\n     */\\n    function subCap(uint _a, uint _b) internal pure returns (uint) {\\n        if (_b > _a)\\n            return 0;\\n        else\\n            return _a - _b;\\n    }\\n\\n    /**\\n     * @dev Multiplies two unsigned integers, returns 2^256 - 1 on overflow.\\n     */\\n    function mulCap(uint _a, uint _b) internal pure returns (uint) {\\n        // Gas optimization: this is cheaper than requiring '_a' not being zero, but the\\n        // benefit is lost if '_b' is also tested.\\n        // See: https://github.com/OpenZeppelin/openzeppelin-solidity/pull/522\\n        if (_a == 0)\\n            return 0;\\n\\n        uint c = _a * _b;\\n        return c / _a == _b ? c : UINT_MAX;\\n    }\\n}\\n\",\"keccak256\":\"0x17dc8ae95582317df81532d06545576f0c509e462299ce71ddddd65ea506a5aa\",\"license\":\"MIT\"},\"src/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\r\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\r\\n\\r\\ninterface IHomeArbitrationProxy {\\r\\n    /**\\r\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when arbitration request is rejected.\\r\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\r\\n     * or if the question is already finalized.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\r\\n     * @param _reason The reason why the request was rejected.\\r\\n     */\\r\\n    event RequestRejected(\\r\\n        bytes32 indexed _questionID,\\r\\n        address indexed _requester,\\r\\n        uint256 _maxPrevious,\\r\\n        string _reason\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request is canceled.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\r\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\r\\n     * @param _questionID The ID of the question.\\r\\n     */\\r\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function receiveArbitrationRequest(\\r\\n        bytes32 _questionID,\\r\\n        address _requester,\\r\\n        uint256 _maxPrevious\\r\\n    ) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\r\\n\\r\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\r\\n      - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\r\\n      - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\r\\n     *  @dev Template_hashes won't be used by this home proxy. \\r\\n     */\\r\\n    function metadata() external view returns (string calldata);\\r\\n}\\r\\n\\r\\ninterface IForeignArbitrationProxy is IArbitrable, IEvidence {\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is requested.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute is created.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _disputeID The ID of the dispute.\\r\\n     */\\r\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute could not be created.\\r\\n     * @dev This will happen if there is an increase in the arbitration fees\\r\\n     * between the time the arbitration is made and the time it is acknowledged.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @param _questionID the ID of the question.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\r\\n}\\r\\n\",\"keccak256\":\"0x30f642a1cbcfde4442a8f50531d8aa52cb84a7ebd81616d5c576e9e54b44ebd4\",\"license\":\"MIT\"},\"src/RealitioForeignArbitrationProxyWithAppeals.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n *  @authors: [@hbarcelos*, @unknownunknown1]\\r\\n *  @reviewers: [@MerlinEgalite*, @shalzz, @jaybuidl, @ferittuncer, @fnanni-0]\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\nimport {IDisputeResolver, IArbitrator} from \\\"@kleros/dispute-resolver-interface-contract/contracts/solc-0.7.x/IDisputeResolver.sol\\\";\\r\\nimport {CappedMath} from \\\"@kleros/ethereum-libraries/contracts/CappedMath.sol\\\";\\r\\nimport {IAMB} from \\\"./dependencies/IAMB.sol\\\";\\r\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./ArbitrationProxyInterfaces.sol\\\";\\r\\n\\r\\n/**\\r\\n * @title Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\\r\\n * This version of the contract has an appeal support.\\r\\n * @dev This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\\r\\n */\\r\\ncontract RealitioForeignArbitrationProxyWithAppeals is IForeignArbitrationProxy, IDisputeResolver {\\r\\n    using CappedMath for uint256;\\r\\n\\r\\n    /* Constants */\\r\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max; // The number of choices for the arbitrator.\\r\\n    uint256 public constant MULTIPLIER_DIVISOR = 10000; // Divisor parameter for multipliers.\\r\\n    uint256 public constant META_EVIDENCE_ID = 0; // The ID of the MetaEvidence for disputes.\\r\\n\\r\\n    /* Storage */\\r\\n\\r\\n    enum Status {\\r\\n        None,\\r\\n        Requested,\\r\\n        Created,\\r\\n        Ruled,\\r\\n        Failed\\r\\n    }\\r\\n\\r\\n    struct ArbitrationRequest {\\r\\n        Status status; // Status of the arbitration.\\r\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\r\\n        uint256 disputeID; // The ID of the dispute in arbitrator contract.\\r\\n        uint256 answer; // The answer given by the arbitrator shifted by -1 to match Realitio format.\\r\\n        Round[] rounds; // Tracks each appeal round of a dispute.\\r\\n    }\\r\\n\\r\\n    struct DisputeDetails {\\r\\n        uint256 arbitrationID; // The ID of the arbitration.\\r\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\r\\n    }\\r\\n\\r\\n    // Round struct stores the contributions made to particular answers.\\r\\n    struct Round {\\r\\n        mapping(uint256 => uint256) paidFees; // Tracks the fees paid in this round in the form paidFees[answer].\\r\\n        mapping(uint256 => bool) hasPaid; // True if the fees for this particular answer have been fully paid in the form hasPaid[answer].\\r\\n        mapping(address => mapping(uint256 => uint256)) contributions; // Maps contributors to their contributions for each answer in the form contributions[address][answer].\\r\\n        uint256 feeRewards; // Sum of reimbursable appeal fees available to the parties that made contributions to the answer that ultimately wins a dispute.\\r\\n        uint256[] fundedAnswers; // Stores the answer choices that are fully funded.\\r\\n    }\\r\\n\\r\\n    IArbitrator public immutable arbitrator; // The address of the arbitrator. TRUSTED.\\r\\n    bytes public arbitratorExtraData; // The extra data used to raise a dispute in the arbitrator.\\r\\n\\r\\n    IAMB public immutable amb; // ArbitraryMessageBridge contract address. TRUSTED.\\r\\n    address public immutable homeProxy; // Address of the counter-party proxy on the Home Chain. TRUSTED.\\r\\n    bytes32 public immutable homeChainId; // The chain ID where the home proxy is deployed.\\r\\n\\r\\n    string public termsOfService; // The path for the Terms of Service for Kleros as an arbitrator for Realitio.\\r\\n\\r\\n    // Multipliers are in basis points.\\r\\n    uint256 public immutable winnerMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that was chosen by the arbitrator in the previous round.\\r\\n    uint256 public immutable loserMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that the arbitrator didn't rule for in the previous round.\\r\\n    uint256 public immutable loserAppealPeriodMultiplier; // Multiplier for calculating the duration of the appeal period for the loser, in basis points.\\r\\n\\r\\n    mapping(uint256 => mapping(address => ArbitrationRequest)) public arbitrationRequests; // Maps arbitration ID to its data. arbitrationRequests[uint(questionID)][requester].\\r\\n    mapping(uint256 => DisputeDetails) public disputeIDToDisputeDetails; // Maps external dispute ids to local arbitration ID and requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => bool) public arbitrationIDToDisputeExists; // Whether a dispute has already been created for the given arbitration ID or not.\\r\\n    mapping(uint256 => address) public arbitrationIDToRequester; // Maps arbitration ID to the requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => uint256) public arbitrationCreatedBlock; // Block of dispute creation. arbitrationCreatedBlock[disputeID]\\r\\n\\r\\n    /* Modifiers */\\r\\n\\r\\n    modifier onlyHomeProxy() {\\r\\n        require(msg.sender == address(amb), \\\"Only AMB allowed\\\");\\r\\n        require(amb.messageSourceChainId() == homeChainId, \\\"Only home chain allowed\\\");\\r\\n        require(amb.messageSender() == homeProxy, \\\"Only home proxy allowed\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Creates an arbitration proxy on the foreign chain.\\r\\n     * @param _amb ArbitraryMessageBridge contract address.\\r\\n     * @param _homeProxy The address of the proxy.\\r\\n     * @param _homeChainId The chain ID where the home proxy is deployed.\\r\\n     * @param _arbitrator Arbitrator contract address.\\r\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\r\\n     * @param _metaEvidence The URI of the meta evidence file.\\r\\n     * @param _termsOfService The path for the Terms of Service for Kleros as an arbitrator for Realitio.\\r\\n     * @param _winnerMultiplier Multiplier for calculating the appeal cost of the winning answer.\\r\\n     * @param _loserMultiplier Multiplier for calculation the appeal cost of the losing answer.\\r\\n     * @param _loserAppealPeriodMultiplier Multiplier for calculating the appeal period for the losing answer.\\r\\n     */\\r\\n    constructor(\\r\\n        IAMB _amb,\\r\\n        address _homeProxy,\\r\\n        bytes32 _homeChainId,\\r\\n        IArbitrator _arbitrator,\\r\\n        bytes memory _arbitratorExtraData,\\r\\n        string memory _metaEvidence,\\r\\n        string memory _termsOfService,\\r\\n        uint256 _winnerMultiplier,\\r\\n        uint256 _loserMultiplier,\\r\\n        uint256 _loserAppealPeriodMultiplier\\r\\n    ) {\\r\\n        amb = _amb;\\r\\n        homeProxy = _homeProxy;\\r\\n        homeChainId = _homeChainId;\\r\\n        arbitrator = _arbitrator;\\r\\n        arbitratorExtraData = _arbitratorExtraData;\\r\\n        termsOfService = _termsOfService;\\r\\n        winnerMultiplier = _winnerMultiplier;\\r\\n        loserMultiplier = _loserMultiplier;\\r\\n        loserAppealPeriodMultiplier = _loserAppealPeriodMultiplier;\\r\\n\\r\\n        emit MetaEvidence(META_EVIDENCE_ID, _metaEvidence);\\r\\n    }\\r\\n\\r\\n    /* External and public */\\r\\n\\r\\n    // ************************ //\\r\\n    // *    Realitio logic    * //\\r\\n    // ************************ //\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\r\\n        require(!arbitrationIDToDisputeExists[uint256(_questionID)], \\\"Dispute already created\\\");\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[uint256(_questionID)][msg.sender];\\r\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\r\\n\\r\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n        require(msg.value >= arbitrationCost, \\\"Deposit value too low\\\");\\r\\n\\r\\n        arbitration.status = Status.Requested;\\r\\n        arbitration.deposit = uint248(msg.value);\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy(0).receiveArbitrationRequest.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\r\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\r\\n\\r\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester)\\r\\n        external\\r\\n        override\\r\\n        onlyHomeProxy\\r\\n    {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n\\r\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n\\r\\n        if (arbitration.deposit >= arbitrationCost) {\\r\\n            try\\r\\n                arbitrator.createDispute{value: arbitrationCost}(NUMBER_OF_CHOICES_FOR_ARBITRATOR, arbitratorExtraData)\\r\\n            returns (uint256 disputeID) {\\r\\n                DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[disputeID];\\r\\n                disputeDetails.arbitrationID = arbitrationID;\\r\\n                disputeDetails.requester = _requester;\\r\\n\\r\\n                arbitrationIDToDisputeExists[arbitrationID] = true;\\r\\n                arbitrationIDToRequester[arbitrationID] = _requester;\\r\\n                arbitrationCreatedBlock[disputeID] = block.number;\\r\\n\\r\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\r\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\r\\n\\r\\n                arbitration.status = Status.Created;\\r\\n                arbitration.deposit = 0;\\r\\n                arbitration.disputeID = disputeID;\\r\\n                arbitration.rounds.push();\\r\\n\\r\\n                if (remainder > 0) {\\r\\n                    payable(_requester).send(remainder);\\r\\n                }\\r\\n\\r\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\r\\n                emit Dispute(arbitrator, disputeID, META_EVIDENCE_ID, arbitrationID);\\r\\n            } catch {\\r\\n                arbitration.status = Status.Failed;\\r\\n                emit ArbitrationFailed(_questionID, _requester);\\r\\n            }\\r\\n        } else {\\r\\n            arbitration.status = Status.Failed;\\r\\n            emit ArbitrationFailed(_questionID, _requester);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external override onlyHomeProxy {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external override {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy(0).receiveArbitrationFailure.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\r\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    // ********************************* //\\r\\n    // *    Appeals and arbitration    * //\\r\\n    // ********************************* //\\r\\n\\r\\n    /**\\r\\n     * @notice Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\\r\\n     * @param _arbitrationID The ID of the arbitration, which is questionID cast into uint256.\\r\\n     * @param _answer One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question.\\r\\n     * Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format.\\r\\n     * Also note that '0' answer can be funded.\\r\\n     * @return Whether the answer was fully funded or not.\\r\\n     */\\r\\n    function fundAppeal(uint256 _arbitrationID, uint256 _answer) external payable override returns (bool) {\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][\\r\\n            arbitrationIDToRequester[_arbitrationID]\\r\\n        ];\\r\\n        require(arbitration.status == Status.Created, \\\"No dispute to appeal.\\\");\\r\\n\\r\\n        uint256 disputeID = arbitration.disputeID;\\r\\n        (uint256 appealPeriodStart, uint256 appealPeriodEnd) = arbitrator.appealPeriod(disputeID);\\r\\n        require(block.timestamp >= appealPeriodStart && block.timestamp < appealPeriodEnd, \\\"Appeal period is over.\\\");\\r\\n\\r\\n        uint256 multiplier;\\r\\n        {\\r\\n            uint256 winner = arbitrator.currentRuling(disputeID);\\r\\n            if (winner == _answer) {\\r\\n                multiplier = winnerMultiplier;\\r\\n            } else {\\r\\n                require(\\r\\n                    block.timestamp - appealPeriodStart <\\r\\n                        (appealPeriodEnd - appealPeriodStart).mulCap(loserAppealPeriodMultiplier) / MULTIPLIER_DIVISOR,\\r\\n                    \\\"Appeal period is over for loser\\\"\\r\\n                );\\r\\n                multiplier = loserMultiplier;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        uint256 lastRoundID = arbitration.rounds.length - 1;\\r\\n        Round storage round = arbitration.rounds[lastRoundID];\\r\\n        require(!round.hasPaid[_answer], \\\"Appeal fee is already paid.\\\");\\r\\n        uint256 appealCost = arbitrator.appealCost(disputeID, arbitratorExtraData);\\r\\n        uint256 totalCost = appealCost.addCap((appealCost.mulCap(multiplier)) / MULTIPLIER_DIVISOR);\\r\\n\\r\\n        // Take up to the amount necessary to fund the current round at the current costs.\\r\\n        uint256 contribution = totalCost.subCap(round.paidFees[_answer]) > msg.value\\r\\n            ? msg.value\\r\\n            : totalCost.subCap(round.paidFees[_answer]);\\r\\n        emit Contribution(_arbitrationID, lastRoundID, _answer, msg.sender, contribution);\\r\\n\\r\\n        round.contributions[msg.sender][_answer] += contribution;\\r\\n        round.paidFees[_answer] += contribution;\\r\\n        if (round.paidFees[_answer] >= totalCost) {\\r\\n            round.feeRewards += round.paidFees[_answer];\\r\\n            round.fundedAnswers.push(_answer);\\r\\n            round.hasPaid[_answer] = true;\\r\\n            emit RulingFunded(_arbitrationID, lastRoundID, _answer);\\r\\n        }\\r\\n\\r\\n        if (round.fundedAnswers.length > 1) {\\r\\n            // At least two sides are fully funded.\\r\\n            arbitration.rounds.push();\\r\\n\\r\\n            round.feeRewards = round.feeRewards.subCap(appealCost);\\r\\n            arbitrator.appeal{value: appealCost}(disputeID, arbitratorExtraData);\\r\\n        }\\r\\n\\r\\n        if (msg.value.subCap(contribution) > 0) msg.sender.send(msg.value.subCap(contribution)); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\r\\n        return round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address to send reward to.\\r\\n     * @param _round The round from which to withdraw.\\r\\n     * @param _answer The answer to query the reward from.\\r\\n     * @return reward The withdrawn amount.\\r\\n     */\\r\\n    function withdrawFeesAndRewards(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) public override returns (uint256 reward) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\r\\n        // Allow to reimburse if funding of the round was unsuccessful.\\r\\n        if (!round.hasPaid[_answer]) {\\r\\n            reward = round.contributions[_beneficiary][_answer];\\r\\n        } else if (!round.hasPaid[arbitration.answer]) {\\r\\n            // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n            // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n            reward = round.fundedAnswers.length > 1\\r\\n                ? (round.contributions[_beneficiary][_answer] * round.feeRewards) /\\r\\n                    (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                : 0;\\r\\n        } else if (arbitration.answer == _answer) {\\r\\n            uint256 paidFees = round.paidFees[_answer];\\r\\n            // Reward the winner.\\r\\n            reward = paidFees > 0 ? (round.contributions[_beneficiary][_answer] * round.feeRewards) / paidFees : 0;\\r\\n        }\\r\\n\\r\\n        if (reward != 0) {\\r\\n            round.contributions[_beneficiary][_answer] = 0;\\r\\n            _beneficiary.send(reward); // It is the user's responsibility to accept ETH.\\r\\n            emit Withdrawal(_arbitrationID, _round, _answer, _beneficiary, reward);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to withdraw any rewards or reimbursable fees for all rounds at once.\\r\\n     * @dev This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.\\r\\n     *      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address that made contributions.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     */\\r\\n    function withdrawFeesAndRewardsForAllRounds(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external override {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n\\r\\n        uint256 numberOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < numberOfRounds; roundNumber++) {\\r\\n            withdrawFeesAndRewards(_arbitrationID, _beneficiary, roundNumber, _contributedTo);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to submit evidence for a particular question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @param _evidenceURI Link to evidence.\\r\\n     */\\r\\n    function submitEvidence(uint256 _arbitrationID, string calldata _evidenceURI) external override {\\r\\n        emit Evidence(arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Rules a specified dispute. Can only be called by the arbitrator.\\r\\n     * @dev Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\\r\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\r\\n     * @param _ruling The ruling given by the arbitrator.\\r\\n     */\\r\\n    function rule(uint256 _disputeID, uint256 _ruling) external override {\\r\\n        DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[_disputeID];\\r\\n        uint256 arbitrationID = disputeDetails.arbitrationID;\\r\\n        address requester = disputeDetails.requester;\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][requester];\\r\\n        require(msg.sender == address(arbitrator), \\\"Only arbitrator allowed\\\");\\r\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\r\\n        uint256 finalRuling = _ruling;\\r\\n\\r\\n        // If one side paid its fees, the ruling is in its favor. Note that if the other side had also paid, an appeal would have been created.\\r\\n        Round storage round = arbitration.rounds[arbitration.rounds.length - 1];\\r\\n        if (round.fundedAnswers.length == 1) finalRuling = round.fundedAnswers[0];\\r\\n\\r\\n        arbitration.answer = finalRuling;\\r\\n        arbitration.status = Status.Ruled;\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy(0).receiveArbitrationAnswer.selector;\\r\\n        // Realitio ruling is shifted by 1 compared to Kleros.\\r\\n        // Note that this shifting won't work in the latest compiler versions (0.8.0 and further), because of the innate underflow checks.\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, bytes32(arbitrationID), bytes32(finalRuling - 1));\\r\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\r\\n\\r\\n        emit Ruling(arbitrator, _disputeID, finalRuling);\\r\\n    }\\r\\n\\r\\n    /* External Views */\\r\\n\\r\\n    /**\\r\\n     * @notice Returns stake multipliers.\\r\\n     * @return winner Winners stake multiplier.\\r\\n     * @return loser Losers stake multiplier.\\r\\n     * @return loserAppealPeriod Multiplier for calculating an appeal period duration for the losing side.\\r\\n     * @return divisor Multiplier divisor.\\r\\n     */\\r\\n    function getMultipliers()\\r\\n        external\\r\\n        view\\r\\n        override\\r\\n        returns (\\r\\n            uint256 winner,\\r\\n            uint256 loser,\\r\\n            uint256 loserAppealPeriod,\\r\\n            uint256 divisor\\r\\n        )\\r\\n    {\\r\\n        return (winnerMultiplier, loserMultiplier, loserAppealPeriodMultiplier, MULTIPLIER_DIVISOR);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns number of possible ruling options. Valid rulings are [0, return value].\\r\\n     * @return count The number of ruling options.\\r\\n     */\\r\\n    function numberOfRulingOptions(\\r\\n        uint256 /* _arbitrationID */\\r\\n    ) external pure override returns (uint256) {\\r\\n        return NUMBER_OF_CHOICES_FOR_ARBITRATOR;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(\\r\\n        bytes32 /* _questionID */\\r\\n    ) external view override returns (uint256) {\\r\\n        return arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the number of rounds of the specific question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @return The number of rounds.\\r\\n     */\\r\\n    function getNumberOfRounds(uint256 _arbitrationID) external view returns (uint256) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        return arbitration.rounds.length;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @return paidFees The amount of fees paid for each fully funded answer.\\r\\n     * @return feeRewards The amount of fees that will be used as rewards.\\r\\n     * @return fundedAnswers IDs of fully funded answers.\\r\\n     */\\r\\n    function getRoundInfo(uint256 _arbitrationID, uint256 _round)\\r\\n        external\\r\\n        view\\r\\n        returns (\\r\\n            uint256[] memory paidFees,\\r\\n            uint256 feeRewards,\\r\\n            uint256[] memory fundedAnswers\\r\\n        )\\r\\n    {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n\\r\\n        paidFees = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < round.fundedAnswers.length; i++) {\\r\\n            paidFees[i] = round.paidFees[round.fundedAnswers[i]];\\r\\n        }\\r\\n\\r\\n        feeRewards = round.feeRewards;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question for a specific answer choice.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _answer The answer choice to get funding status for.\\r\\n     * @return raised The amount paid for this answer.\\r\\n     * @return fullyFunded Whether the answer is fully funded or not.\\r\\n     */\\r\\n    function getFundingStatus(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) external view returns (uint256 raised, bool fullyFunded) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        raised = round.paidFees[_answer];\\r\\n        fullyFunded = round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets contributions to the answers that are fully funded.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _contributor The address whose contributions to query.\\r\\n     * @return fundedAnswers IDs of the answers that are fully funded.\\r\\n     * @return contributions The amount contributed to each funded answer by the contributor.\\r\\n     */\\r\\n    function getContributionsToSuccessfulFundings(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        address _contributor\\r\\n    ) external view returns (uint256[] memory fundedAnswers, uint256[] memory contributions) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n        contributions = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < contributions.length; i++) {\\r\\n            contributions[i] = round.contributions[_contributor][fundedAnswers[i]];\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns the sum of withdrawable amount.\\r\\n     * @dev This function is O(n) where n is the total number of rounds.\\r\\n     * @dev This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The contributor for which to query.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     * @return sum The total amount available to withdraw.\\r\\n     */\\r\\n    function getTotalWithdrawableAmount(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external view override returns (uint256 sum) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        if (arbitration.status < Status.Ruled) return sum;\\r\\n\\r\\n        uint256 finalAnswer = arbitration.answer;\\r\\n        uint256 noOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < noOfRounds; roundNumber++) {\\r\\n            Round storage round = arbitration.rounds[roundNumber];\\r\\n\\r\\n            if (!round.hasPaid[_contributedTo]) {\\r\\n                // Allow to reimburse if funding was unsuccessful for this answer option.\\r\\n                sum += round.contributions[_beneficiary][_contributedTo];\\r\\n            } else if (!round.hasPaid[finalAnswer]) {\\r\\n                // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n                // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n                sum += round.fundedAnswers.length > 1\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) /\\r\\n                        (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                    : 0;\\r\\n            } else if (finalAnswer == _contributedTo) {\\r\\n                uint256 paidFees = round.paidFees[_contributedTo];\\r\\n                // Reward the winner.\\r\\n                sum += paidFees > 0\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) / paidFees\\r\\n                    : 0;\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Casts question ID into uint256 thus returning the related arbitration ID.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @return The ID of the arbitration.\\r\\n     */\\r\\n    function questionIDToArbitrationID(bytes32 _questionID) external pure returns (uint256) {\\r\\n        return uint256(_questionID);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\r\\n     * @param _externalDisputeID Dispute id as in arbitrator side.\\r\\n     * @return localDisputeID Dispute id as in arbitrable contract.\\r\\n     */\\r\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external view override returns (uint256) {\\r\\n        return disputeIDToDisputeDetails[_externalDisputeID].arbitrationID;\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0xeff1c230b3a96f7513863d93ebe5cef8b62bea1160cd7206c2985d9f760b27d5\",\"license\":\"MIT\"},\"src/dependencies/IAMB.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\ninterface IAMB {\\r\\n    function requireToPassMessage(\\r\\n        address _contract,\\r\\n        bytes memory _data,\\r\\n        uint256 _gas\\r\\n    ) external returns (bytes32);\\r\\n\\r\\n    function maxGasPerTx() external view returns (uint256);\\r\\n\\r\\n    function messageSender() external view returns (address);\\r\\n\\r\\n    function messageSourceChainId() external view returns (bytes32);\\r\\n\\r\\n    function messageId() external view returns (bytes32);\\r\\n}\\r\\n\",\"keccak256\":\"0x2e79d0690426c2daa93907cebce6ec6f91c03a25cf04b1fd6aefda033b3134bb\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "0x6101606040523480156200001257600080fd5b5060405162003c7938038062003c7983398181016040526101408110156200003957600080fd5b81516020830151604080850151606086015160808701805193519597949692959194919392820192846401000000008211156200007557600080fd5b9083019060208201858111156200008b57600080fd5b8251640100000000811182820188101715620000a657600080fd5b82525081516020918201929091019080838360005b83811015620000d5578181015183820152602001620000bb565b50505050905090810190601f168015620001035780820380516001836020036101000a031916815260200191505b50604052602001805160405193929190846401000000008211156200012757600080fd5b9083019060208201858111156200013d57600080fd5b82516401000000008111828201881017156200015857600080fd5b82525081516020918201929091019080838360005b83811015620001875781810151838201526020016200016d565b50505050905090810190601f168015620001b55780820380516001836020036101000a031916815260200191505b5060405260200180516040519392919084640100000000821115620001d957600080fd5b908301906020820185811115620001ef57600080fd5b82516401000000008111828201881017156200020a57600080fd5b82525081516020918201929091019080838360005b83811015620002395781810151838201526020016200021f565b50505050905090810190601f168015620002675780820380516001836020036101000a031916815260200191505b506040908152602082810151918301516060938401516001600160601b03198e861b811660a0528d861b811660c05260e08d9052948b901b90941660805288519295509350620002bd9160009189019062000392565b508351620002d390600190602087019062000392565b5061010083905261012082905261014081905260408051602080825287518183015287516000937f61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d938a93909283928301918501908083838a5b83811015620003475781810151838201526020016200032d565b50505050905090810190601f168015620003755780820380516001836020036101000a031916815260200191505b509250505060405180910390a2505050505050505050506200043e565b828054600181600116156101000203166002900490600052602060002090601f016020900481019282620003ca576000855562000415565b82601f10620003e557805160ff191683800117855562000415565b8280016001018555821562000415579182015b8281111562000415578251825591602001919060010190620003f8565b506200042392915062000427565b5090565b5b8082111562000423576000815560010162000428565b60805160601c60a05160601c60c05160601c60e0516101005161012051610140516137346200054560003980610a9652806117e75280611e0452508061186a5280611de35280611f9a5250806117b65280611dc25280611fbe52508061272b5280612c4f528061354c525080610dcf528061125152806113fe528061246e528061281d5280612d41525080610b4e5280610d9e52806113d2528061244552806126c4528061274c52806128475280612be85280612c705280612d6b525080610c065280610f3b5280611625528061171c52806119645280611bff5280611d83528061202052806120fd52806122aa5280612f085280613030528061324952506137346000f3fe60806040526004361061021a5760003560e01c80639b3ac99811610123578063c21ae061116100ab578063edabc4741161006f578063edabc4741461098b578063ef12696714610a01578063fc6f8f1614610a16578063fe2dddeb14610a40578063ffa1ad7414610a7f5761021a565b8063c21ae061146107fc578063c599c66f14610826578063d661dd31146108fe578063e742e2ed14610913578063ec85635b1461094c5761021a565b8063aba980eb116100f2578063aba980eb146106d5578063bb163782146106ff578063bb2fe65f14610729578063bde80ca714610778578063be26fc88146107c35761021a565b80639b3ac998146105ef578063a22352e214610604578063a6a7f0eb1461062e578063a829c3d1146106b25761021a565b80634b2f0ea0116101a6578063756804c011610175578063756804c01461047b57806379873f8a146104a55780638a9bb02a146104e05780638d8b2d7e146105b0578063965394ab146105da5761021a565b80634b2f0ea0146103f057806354dba90f1461042757806368cb30f51461043c5780636cc6cde1146104665761021a565b80631e117f49116101ed5780631e117f4914610316578063311a6c561461032b578063362c34791461035d57806348f37857146103a25780634aa6a1e4146103b75761021a565b80630c139eb41461021f5780630c7ac7b6146102465780630fa8c7ce146102d05780631062b39a146102e5575b600080fd5b34801561022b57600080fd5b50610234610a94565b60408051918252519081900360200190f35b34801561025257600080fd5b5061025b610ab8565b6040805160208082528351818301528351919283929083019185019080838360005b8381101561029557818101518382015260200161027d565b50505050905090810190601f1680156102c25780820380516001836020036101000a031916815260200191505b509250505060405180910390f35b3480156102dc57600080fd5b50610234610b46565b3480156102f157600080fd5b506102fa610b4c565b604080516001600160a01b039092168252519081900360200190f35b34801561032257600080fd5b5061025b610b70565b34801561033757600080fd5b5061035b6004803603604081101561034e57600080fd5b5080359060200135610bca565b005b34801561036957600080fd5b506102346004803603608081101561038057600080fd5b508035906001600160a01b036020820135169060408101359060600135610f92565b3480156103ae57600080fd5b506102fa61124f565b3480156103c357600080fd5b5061035b600480360360408110156103da57600080fd5b50803590602001356001600160a01b0316611273565b6104136004803603604081101561040657600080fd5b5080359060200135611593565b604080519115158252519081900360200190f35b34801561043357600080fd5b50610234611d67565b34801561044857600080fd5b506104136004803603602081101561045f57600080fd5b5035611d6c565b34801561047257600080fd5b506102fa611d81565b34801561048757600080fd5b506102fa6004803603602081101561049e57600080fd5b5035611da5565b3480156104b157600080fd5b506104ba611dc0565b604080519485526020850193909352838301919091526060830152519081900360800190f35b3480156104ec57600080fd5b506105106004803603604081101561050357600080fd5b5080359060200135611e2c565b604051808060200184815260200180602001838103835286818151815260200191508051906020019060200280838360005b8381101561055a578181015183820152602001610542565b50505050905001838103825284818151815260200191508051906020019060200280838360005b83811015610599578181015183820152602001610581565b505050509050019550505050505060405180910390f35b3480156105bc57600080fd5b50610234600480360360208110156105d357600080fd5b5035611f91565b3480156105e657600080fd5b50610234611f98565b3480156105fb57600080fd5b50610234611fbc565b34801561061057600080fd5b506102346004803603602081101561062757600080fd5b5035611fe0565b34801561063a57600080fd5b5061035b6004803603604081101561065157600080fd5b8135919081019060408101602082013564010000000081111561067357600080fd5b82018360208201111561068557600080fd5b803590602001918460018302840111640100000000831117156106a757600080fd5b5090925090506120f0565b61035b600480360360408110156106c857600080fd5b508035906020013561218a565b3480156106e157600080fd5b50610234600480360360208110156106f857600080fd5b5035612604565b34801561070b57600080fd5b506102346004803603602081101561072257600080fd5b5035612616565b34801561073557600080fd5b5061075f6004803603606081101561074c57600080fd5b5080359060208101359060400135612619565b6040805192835290151560208301528051918290030190f35b34801561078457600080fd5b506107a26004803603602081101561079b57600080fd5b5035612697565b604080519283526001600160a01b0390911660208301528051918290030190f35b3480156107cf57600080fd5b5061035b600480360360408110156107e657600080fd5b50803590602001356001600160a01b03166126b9565b34801561080857600080fd5b506102346004803603602081101561081f57600080fd5b5035612a5a565b34801561083257600080fd5b506108656004803603606081101561084957600080fd5b50803590602081013590604001356001600160a01b0316612a6c565b604051808060200180602001838103835285818151815260200191508051906020019060200280838360005b838110156108a9578181015183820152602001610891565b50505050905001838103825284818151815260200191508051906020019060200280838360005b838110156108e85781810151838201526020016108d0565b5050505090500194505050505060405180910390f35b34801561090a57600080fd5b50610234612bd7565b34801561091f57600080fd5b5061035b6004803603604081101561093657600080fd5b50803590602001356001600160a01b0316612bdd565b34801561095857600080fd5b506102346004803603606081101561096f57600080fd5b508035906001600160a01b036020820135169060400135613303565b34801561099757600080fd5b506109c4600480360360408110156109ae57600080fd5b50803590602001356001600160a01b031661350d565b604051808560048111156109d457fe5b81526001600160f81b03909416602085015250604080840192909252606083015251908190036080019150f35b348015610a0d57600080fd5b5061023461354a565b348015610a2257600080fd5b5061023460048036036020811015610a3957600080fd5b503561356e565b348015610a4c57600080fd5b5061035b60048036036060811015610a6357600080fd5b508035906001600160a01b0360208201351690604001356135a0565b348015610a8b57600080fd5b5061025b6135fc565b7f000000000000000000000000000000000000000000000000000000000000000081565b6000805460408051602060026001851615610100026000190190941693909304601f81018490048402820184019092528181529291830182828015610b3e5780601f10610b1357610100808354040283529160200191610b3e565b820191906000526020600020905b815481529060010190602001808311610b2157829003601f168201915b505050505081565b60001981565b7f000000000000000000000000000000000000000000000000000000000000000081565b60018054604080516020600284861615610100026000190190941693909304601f81018490048402820184019092528181529291830182828015610b3e5780601f10610b1357610100808354040283529160200191610b3e565b600082815260036020908152604080832080546001820154818652600285528386206001600160a01b03918216808852955292909420909392917f0000000000000000000000000000000000000000000000000000000000000000163314610c79576040805162461bcd60e51b815260206004820152601760248201527f4f6e6c792061726269747261746f7220616c6c6f776564000000000000000000604482015290519081900360640190fd5b6002815460ff166004811115610c8b57fe5b14610cda576040805162461bcd60e51b815260206004820152601a602482015279496e76616c6964206172626974726174696f6e2073746174757360301b604482015290519081900360640190fd5b60038101805486916000916000198101908110610cf357fe5b90600052602060002090600502019050806004018054905060011415610d315780600401600081548110610d2357fe5b906000526020600020015491505b60028301829055825460ff19166003178355604080516024810187905260001984016044808301919091528251808303909101815260649091018252602080820180516306bad43760e51b6001600160e01b039091168117909152835163e5789d0360e01b8152935190937f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03169263dc8601b3927f0000000000000000000000000000000000000000000000000000000000000000928692869263e5789d03926004808201939291829003018186803b158015610e1557600080fd5b505afa158015610e29573d6000803e3d6000fd5b505050506040513d6020811015610e3f57600080fd5b50516040516001600160e01b031960e086901b1681526001600160a01b038416600482019081526044820183905260606024830190815284516064840152845191929091608490910190602086019080838360005b83811015610eac578181015183820152602001610e94565b50505050905090810190601f168015610ed95780820380516001836020036101000a031916815260200191505b50945050505050602060405180830381600087803b158015610efa57600080fd5b505af1158015610f0e573d6000803e3d6000fd5b505050506040513d6020811015610f2457600080fd5b50506040805185815290518b916001600160a01b037f000000000000000000000000000000000000000000000000000000000000000016917f394027a5fa6e098a1191094d1719d6929b9abc535fcc0c8f448d6a4e756222769181900360200190a350505050505050505050565b600084815260056020908152604080832054600283528184206001600160a01b039091168085529252822060038101805484919087908110610fd057fe5b6000918252602090912060059091020190506003825460ff166004811115610ff457fe5b1461103d576040805162461bcd60e51b8152602060048201526014602482015273111a5cdc1d5d19481b9bdd081c995cdbdb1d995960621b604482015290519081900360640190fd5b600085815260018201602052604090205460ff16611082576001600160a01b0387166000908152600282016020908152604080832088845290915290205493506111b1565b6002820154600090815260018201602052604090205460ff1661114f5760048101546001106110b2576000611148565b806000016000826004016001815481106110c857fe5b9060005260206000200154815260200190815260200160002054816000016000836004016000815481106110f857fe5b6000918252602080832090910154835282810193909352604091820181205460038601546001600160a01b038d1683526002870185528383208b845290945291902054920191028161114657fe5b045b93506111b1565b84826002015414156111b157600085815260208290526040902054806111765760006111ad565b60038201546001600160a01b038916600090815260028401602090815260408083208a8452909152902054829102816111ab57fe5b045b9450505b8315611244576001600160a01b038716600081815260028301602090815260408083208984529091528082208290555186156108fc0291879190818181858888f1935050505050866001600160a01b031686897f54b3cab3cb5c4aca3209db1151caff092e878011202e43a36782d4ebe0b963ae8888604051808381526020018281526020019250505060405180910390a45b505050949350505050565b7f000000000000000000000000000000000000000000000000000000000000000081565b60008281526002602090815260408083206001600160a01b0385168452909152902082906004815460ff1660048111156112a957fe5b146112f8576040805162461bcd60e51b815260206004820152601a602482015279496e76616c6964206172626974726174696f6e2073746174757360301b604482015290519081900360640190fd5b805460008381526002602081815260408084206001600160a01b03891685529091528220828155600181018390559081018290556101009092046001600160f81b0316919061134a6003830182613681565b50506040516001600160a01b0385169082156108fc029083906000818181858888f1505060408051602481018a90526001600160a01b03808a166044808401919091528351808403909101815260649092018352602082810180516001600160e01b03166305fb2b4f60e51b908117909152845163e5789d0360e01b815294519097509295507f0000000000000000000000000000000000000000000000000000000000000000909116935063dc8601b3927f0000000000000000000000000000000000000000000000000000000000000000928692869263e5789d039260048083019392829003018186803b15801561144357600080fd5b505afa158015611457573d6000803e3d6000fd5b505050506040513d602081101561146d57600080fd5b50516040516001600160e01b031960e086901b1681526001600160a01b038416600482019081526044820183905260606024830190815284516064840152845191929091608490910190602086019080838360005b838110156114da5781810151838201526020016114c2565b50505050905090810190601f1680156115075780820380516001836020036101000a031916815260200191505b50945050505050602060405180830381600087803b15801561152857600080fd5b505af115801561153c573d6000803e3d6000fd5b505050506040513d602081101561155257600080fd5b50506040516001600160a01b0387169088907fe7700735be0b02f71ef1d623678daf36cd936af4c349a22ad9d5a8b217df0dd990600090a350505050505050565b600082815260026020818152604080842060058352818520546001600160a01b03168552909152822090815460ff1660048111156115cd57fe5b14611617576040805162461bcd60e51b81526020600482015260156024820152742737903234b9b83aba32903a379030b83832b0b61760591b604482015290519081900360640190fd5b6000816001015490506000807f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031663afe15cfb846040518263ffffffff1660e01b815260040180828152602001915050604080518083038186803b15801561168657600080fd5b505afa15801561169a573d6000803e3d6000fd5b505050506040513d60408110156116b057600080fd5b50805160209091015190925090504282118015906116cd57508042105b611717576040805162461bcd60e51b815260206004820152601660248201527520b83832b0b6103832b934b7b21034b99037bb32b91760511b604482015290519081900360640190fd5b6000807f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316631c3db16d866040518263ffffffff1660e01b81526004018082815260200191505060206040518083038186803b15801561177e57600080fd5b505afa158015611792573d6000803e3d6000fd5b505050506040513d60208110156117a857600080fd5b50519050878114156117dc577f0000000000000000000000000000000000000000000000000000000000000000915061188c565b61271061180b8585037f000000000000000000000000000000000000000000000000000000000000000061361d565b8161181257fe5b0484420310611868576040805162461bcd60e51b815260206004820152601f60248201527f41707065616c20706572696f64206973206f76657220666f72206c6f73657200604482015290519081900360640190fd5b7f000000000000000000000000000000000000000000000000000000000000000091505b50600385018054600019810191600091839081106118a657fe5b600091825260208083208c84526001600590930201918201905260409091205490915060ff161561191e576040805162461bcd60e51b815260206004820152601b60248201527f41707065616c2066656520697320616c726561647920706169642e0000000000604482015290519081900360640190fd5b6040805163791f8b7360e11b8152600481018881526024820192835260008054600260001961010060018416150201909116046044840181905290936001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169363f23f16e6938c938793919260640190849080156119e45780601f106119b9576101008083540402835291602001916119e4565b820191906000526020600020905b8154815290600101906020018083116119c757829003601f168201915b5050935050505060206040518083038186803b158015611a0357600080fd5b505afa158015611a17573d6000803e3d6000fd5b505050506040513d6020811015611a2d57600080fd5b505190506000611a53612710611a43848861361d565b81611a4a57fe5b84919004613651565b60008c815260208590526040812054919250903490611a73908490613666565b11611a975760008c815260208590526040902054611a92908390613666565b611a99565b345b9050336001600160a01b0316858e7fcae597f39a3ad75c2e10d46b031f023c5c2babcd58ca0491b122acda3968d4c08f85604051808381526020018281526020019250505060405180910390a433600090815260028501602090815260408083208f8452825280832080548501905590869052902080548201908190558211611bcc578360000160008d8152602001908152602001600020548460030160008282540192505081905550836004018c908060018154018082558091505060019003906000526020600020016000909190919091505560018460010160008e815260200190815260200160002060006101000a81548160ff0219169083151502179055508b858e7f39493c1b78d9a13bcc9e1d532fc7faed3889248d93affa811416ce3c6bcb1a6860405160405180910390a45b600484015460011015611d08576003808b0180546001018155600052840154611bf59084613666565b84600301819055507f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03166349912f88848b60006040518463ffffffff1660e01b81526004018083815260200180602001828103825283818154600181600116156101000203166002900481526020019150805460018160011615610100020316600290048015611cce5780601f10611ca357610100808354040283529160200191611cce565b820191906000526020600020905b815481529060010190602001808311611cb157829003601f168201915b505093505050506000604051808303818588803b158015611cee57600080fd5b505af1158015611d02573d6000803e3d6000fd5b50505050505b6000611d143483613666565b1115611d4057336108fc611d283484613666565b6040518115909202916000818181858888f150505050505b50505060008981526001909101602052604090205460ff1696505050505050505b92915050565b600081565b60046020526000908152604090205460ff1681565b7f000000000000000000000000000000000000000000000000000000000000000081565b6005602052600090815260409020546001600160a01b031681565b7f00000000000000000000000000000000000000000000000000000000000000007f00000000000000000000000000000000000000000000000000000000000000007f000000000000000000000000000000000000000000000000000000000000000061271090919293565b600082815260056020908152604080832054600283528184206001600160a01b03909116808552925282206003810180546060949385939092909185919088908110611e7457fe5b9060005260206000209060050201905080600401805480602002602001604051908101604052809291908181526020018280548015611ed257602002820191906000526020600020905b815481526020019060010190808311611ebe575b5050505060048301549195505067ffffffffffffffff81118015611ef557600080fd5b50604051908082528060200260200182016040528015611f1f578160200160208202803683370190505b50955060005b6004820154811015611f7f57816000016000836004018381548110611f4657fe5b9060005260206000200154815260200190815260200160002054878281518110611f6c57fe5b6020908102919091010152600101611f25565b50806003015494505050509250925092565b5060001990565b7f000000000000000000000000000000000000000000000000000000000000000081565b7f000000000000000000000000000000000000000000000000000000000000000081565b60405163f7434ea960e01b815260206004820190815260008054600260001961010060018416150201909116046024840181905290926001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169263f7434ea99285928291604490910190849080156120a05780601f10612075576101008083540402835291602001916120a0565b820191906000526020600020905b81548152906001019060200180831161208357829003601f168201915b50509250505060206040518083038186803b1580156120be57600080fd5b505afa1580156120d2573d6000803e3d6000fd5b505050506040513d60208110156120e857600080fd5b505192915050565b336001600160a01b0316837f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03167fdccf2f8b2cc26eafcd61905cba744cff4b81d14740725f6376390dc6298a6a3c858560405180806020018281038252848482818152602001925080828437600083820152604051601f909101601f19169092018290039550909350505050a4505050565b60008281526004602052604090205460ff16156121ee576040805162461bcd60e51b815260206004820152601760248201527f4469737075746520616c72656164792063726561746564000000000000000000604482015290519081900360640190fd5b6000828152600260209081526040808320338452909152812090815460ff16600481111561221857fe5b1461226a576040805162461bcd60e51b815260206004820152601d60248201527f4172626974726174696f6e20616c726561647920726571756573746564000000604482015290519081900360640190fd5b60405163f7434ea960e01b815260206004820190815260008054600260001961010060018416150201909116046024840181905290926001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169263f7434ea992859282916044909101908490801561232a5780601f106122ff5761010080835404028352916020019161232a565b820191906000526020600020905b81548152906001019060200180831161230d57829003601f168201915b50509250505060206040518083038186803b15801561234857600080fd5b505afa15801561235c573d6000803e3d6000fd5b505050506040513d602081101561237257600080fd5b50519050348111156123c3576040805162461bcd60e51b81526020600482015260156024820152744465706f7369742076616c756520746f6f206c6f7760581b604482015290519081900360640190fd5b8154600160ff199091161760ff16610100346001600160f81b0316021782556040805160248101869052336044820152606480820186905282518083039091018152608490910182526020808201805163666b746960e11b6001600160e01b039091168117909152835163e5789d0360e01b8152935190936001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169263dc8601b3927f0000000000000000000000000000000000000000000000000000000000000000928692869263e5789d03926004808201939291829003018186803b1580156124b457600080fd5b505afa1580156124c8573d6000803e3d6000fd5b505050506040513d60208110156124de57600080fd5b50516040516001600160e01b031960e086901b1681526001600160a01b038416600482019081526044820183905260606024830190815284516064840152845191929091608490910190602086019080838360005b8381101561254b578181015183820152602001612533565b50505050905090810190601f1680156125785780820380516001836020036101000a031916815260200191505b50945050505050602060405180830381600087803b15801561259957600080fd5b505af11580156125ad573d6000803e3d6000fd5b505050506040513d60208110156125c357600080fd5b5050604080518681529051339188917f2067d1b3170fe803dd001b95b04f9d372eb9c3e5a48ed131a62962a7300bdb209181900360200190a3505050505050565b60066020526000908152604090205481565b90565b600083815260056020908152604080832054600283528184206001600160a01b03909116808552925282206003810180548493929184918890811061265a57fe5b600091825260208083209883526005909102909701808852604080832054600190920190985296902054959860ff90961697509495505050505050565b600360205260009081526040902080546001909101546001600160a01b031682565b336001600160a01b037f00000000000000000000000000000000000000000000000000000000000000001614612729576040805162461bcd60e51b815260206004820152601060248201526f13db9b1e4810535088185b1b1bddd95960821b604482015290519081900360640190fd5b7f00000000000000000000000000000000000000000000000000000000000000007f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316639e307dff6040518163ffffffff1660e01b815260040160206040518083038186803b1580156127a357600080fd5b505afa1580156127b7573d6000803e3d6000fd5b505050506040513d60208110156127cd57600080fd5b50511461281b576040805162461bcd60e51b815260206004820152601760248201527613db9b1e481a1bdb594818da185a5b88185b1b1bddd959604a1b604482015290519081900360640190fd5b7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03167f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031663d67bdd256040518163ffffffff1660e01b815260040160206040518083038186803b15801561289e57600080fd5b505afa1580156128b2573d6000803e3d6000fd5b505050506040513d60208110156128c857600080fd5b50516001600160a01b03161461291f576040805162461bcd60e51b815260206004820152601760248201527613db9b1e481a1bdb59481c1c9bde1e48185b1b1bddd959604a1b604482015290519081900360640190fd5b60008281526002602090815260408083206001600160a01b0385168452909152902082906001815460ff16600481111561295557fe5b146129a4576040805162461bcd60e51b815260206004820152601a602482015279496e76616c6964206172626974726174696f6e2073746174757360301b604482015290519081900360640190fd5b805460008381526002602081815260408084206001600160a01b03891685529091528220828155600181018390559081018290556101009092046001600160f81b031691906129f66003830182613681565b50506040516001600160a01b0385169082156108fc029083906000818181858888f150506040516001600160a01b03881693508892507fe7700735be0b02f71ef1d623678daf36cd936af4c349a22ad9d5a8b217df0dd99150600090a35050505050565b60009081526003602052604090205490565b600083815260056020908152604080832054600283528184206001600160a01b039091168085529252822060038101805460609485949392909188908110612ab057fe5b9060005260206000209060050201905080600401805480602002602001604051908101604052809291908181526020018280548015612b0e57602002820191906000526020600020905b815481526020019060010190808311612afa575b5050505060048301549196505067ffffffffffffffff81118015612b3157600080fd5b50604051908082528060200260200182016040528015612b5b578160200160208202803683370190505b50935060005b8451811015612bcb576001600160a01b038716600090815260028301602052604081208751909190889084908110612b9557fe5b6020026020010151815260200190815260200160002054858281518110612bb857fe5b6020908102919091010152600101612b61565b50505050935093915050565b61271081565b336001600160a01b037f00000000000000000000000000000000000000000000000000000000000000001614612c4d576040805162461bcd60e51b815260206004820152601060248201526f13db9b1e4810535088185b1b1bddd95960821b604482015290519081900360640190fd5b7f00000000000000000000000000000000000000000000000000000000000000007f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316639e307dff6040518163ffffffff1660e01b815260040160206040518083038186803b158015612cc757600080fd5b505afa158015612cdb573d6000803e3d6000fd5b505050506040513d6020811015612cf157600080fd5b505114612d3f576040805162461bcd60e51b815260206004820152601760248201527613db9b1e481a1bdb594818da185a5b88185b1b1bddd959604a1b604482015290519081900360640190fd5b7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03167f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031663d67bdd256040518163ffffffff1660e01b815260040160206040518083038186803b158015612dc257600080fd5b505afa158015612dd6573d6000803e3d6000fd5b505050506040513d6020811015612dec57600080fd5b50516001600160a01b031614612e43576040805162461bcd60e51b815260206004820152601760248201527613db9b1e481a1bdb59481c1c9bde1e48185b1b1bddd959604a1b604482015290519081900360640190fd5b60008281526002602090815260408083206001600160a01b0385168452909152902082906001815460ff166004811115612e7957fe5b14612ec8576040805162461bcd60e51b815260206004820152601a602482015279496e76616c6964206172626974726174696f6e2073746174757360301b604482015290519081900360640190fd5b60405163f7434ea960e01b815260206004820190815260008054600260001961010060018416150201909116046024840181905290926001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169263f7434ea9928592829160449091019084908015612f885780601f10612f5d57610100808354040283529160200191612f88565b820191906000526020600020905b815481529060010190602001808311612f6b57829003601f168201915b50509250505060206040518083038186803b158015612fa657600080fd5b505afa158015612fba573d6000803e3d6000fd5b505050506040513d6020811015612fd057600080fd5b5051825490915061010090046001600160f81b031681116132ba576040805163c13517e160e01b81526000196004820181815260248301938452600080546002610100600183161502850190911604604485018190526001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169563c13517e19588959492606490910190849080156130b05780601f10613085576101008083540402835291602001916130b0565b820191906000526020600020905b81548152906001019060200180831161309357829003601f168201915b505093505050506020604051808303818588803b1580156130d057600080fd5b505af1935050505080156130f657506040513d60208110156130f157600080fd5b505160015b61314057815460ff191660041782556040516001600160a01b0385169086907f9beda0c81abc1c65da7685f113195974dfddb781dfde6263e5a1b13a5356ffad90600090a36132b5565b6000818152600360208181526040808420888155600180820180546001600160a01b03199081166001600160a01b038e169081179092558b885260048652848820805460ff19908116851790915560058752858920805490921690921790558787526006909452918520439055875460ff938116600217939093168855878201869055928701805490910181559092529061010090046001600160f81b0316839003801561320e576040516001600160a01b0388169082156108fc029083906000818181858888f150505050505b82876001600160a01b0316897fcf9bcbc2efae51060af73ef64119281007f63eada739f19c41c181d8350fa81460405160405180910390a4827f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03167f74baab670a4015ab2f1b467c5252a96141a2573f2908e58a92081e80d3cfde3d600089604051808381526020018281526020019250505060405180910390a35050505b6132fc565b815460ff191660041782556040516001600160a01b0385169086907f9beda0c81abc1c65da7685f113195974dfddb781dfde6263e5a1b13a5356ffad90600090a35b5050505050565b600083815260056020908152604080832054600283528184206001600160a01b03909116808552925282206003815460ff16600481111561334057fe5b101561334d575050613506565b6002810154600382015460005b8181101561350057600084600301828154811061337357fe5b600091825260208083208b84526001600590930201918201905260409091205490915060ff166133cd576001600160a01b038916600090815260028201602090815260408083208b845290915290205496909601956134f7565b600084815260018201602052604090205460ff166134975760048101546001106133f857600061348e565b8060000160008260040160018154811061340e57fe5b90600052602060002001548152602001908152602001600020548160000160008360040160008154811061343e57fe5b6000918252602080832090910154835282810193909352604091820181205460038601546001600160a01b038f1683526002870185528383208e845290945291902054920191028161348c57fe5b045b870196506134f7565b878414156134f757600088815260208290526040902054806134ba5760006134f1565b60038201546001600160a01b038b16600090815260028401602090815260408083208d8452909152902054829102816134ef57fe5b045b88019750505b5060010161335a565b50505050505b9392505050565b6002602081815260009384526040808520909152918352912080546001820154919092015460ff83169261010090046001600160f81b0316919084565b7f000000000000000000000000000000000000000000000000000000000000000081565b600090815260056020908152604080832054600283528184206001600160a01b03909116845290915290206003015490565b600083815260056020908152604080832054600283528184206001600160a01b0390911680855292528220600381015491929091905b818110156135f3576135ea87878388610f92565b506001016135d6565b50505050505050565b604051806040016040528060058152602001640322e302e360dc1b81525081565b60008261362c57506000611d61565b8282028284828161363957fe5b041461364757600019613649565b805b949350505050565b60008282018381101561364757600019613649565b60008282111561367857506000611d61565b50808203611d61565b50805460008255600502906000526020600020908101906136a291906136a5565b50565b808211156136cc576000600382018190556136c360048301826136d0565b506005016136a5565b5090565b50805460008255906000526020600020908101906136a291905b808211156136cc57600081556001016136ea56fea2646970667358221220bcc609c66659909ccfb8e3ebb9f2da3c07b56b4bdf9f4409ec49c0803a341ccb64736f6c63430007060033", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.", "kind": "dev", "methods": {"constructor": {"params": {"_amb": "ArbitraryMessageBridge contract address.", "_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_homeChainId": "The chain ID where the home proxy is deployed.", "_homeProxy": "The address of the proxy.", "_loserAppealPeriodMultiplier": "Multiplier for calculating the appeal period for the losing answer.", "_loserMultiplier": "Multiplier for calculation the appeal cost of the losing answer.", "_metaEvidence": "The URI of the meta evidence file.", "_termsOfService": "The path for the Terms of Service for Kleros as an arbitrator for Realitio.", "_winnerMultiplier": "Multiplier for calculating the appeal cost of the winning answer."}}, "externalIDtoLocalID(uint256)": {"params": {"_externalDisputeID": "Dispute id as in arbitrator side."}, "returns": {"_0": "localDisputeID Dispute id as in arbitrable contract."}}, "fundAppeal(uint256,uint256)": {"params": {"_answer": "One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.", "_arbitrationID": "The ID of the arbitration, which is questionID cast into uint256."}, "returns": {"_0": "Whether the answer was fully funded or not."}}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_contributor": "The address whose contributions to query.", "_round": "The round to query."}, "returns": {"contributions": "The amount contributed to each funded answer by the contributor.", "fundedAnswers": "IDs of the answers that are fully funded."}}, "getDisputeFee(bytes32)": {"returns": {"_0": "The fee to create a dispute."}}, "getFundingStatus(uint256,uint256,uint256)": {"params": {"_answer": "The answer choice to get funding status for.", "_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"fullyFunded": "Whether the answer is fully funded or not.", "raised": "The amount paid for this answer."}}, "getMultipliers()": {"returns": {"divisor": "Multiplier divisor.", "loser": "Losers stake multiplier.", "loserAppealPeriod": "Multiplier for calculating an appeal period duration for the losing side.", "winner": "Winners stake multiplier."}}, "getNumberOfRounds(uint256)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question."}, "returns": {"_0": "The number of rounds."}}, "getRoundInfo(uint256,uint256)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"feeRewards": "The amount of fees that will be used as rewards.", "fundedAnswers": "IDs of fully funded answers.", "paidFees": "The amount of fees paid for each fully funded answer."}}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The contributor for which to query.", "_contributedTo": "Answer that received contributions from contributor."}, "returns": {"sum": "The total amount available to withdraw."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "numberOfRulingOptions(uint256)": {"returns": {"_0": "count The number of ruling options."}}, "questionIDToArbitrationID(bytes32)": {"params": {"_questionID": "The ID of the question."}, "returns": {"_0": "The ID of the arbitration."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}, "submitEvidence(uint256,string)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question.", "_evidenceURI": "Link to evidence."}}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"params": {"_answer": "The answer to query the reward from.", "_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address to send reward to.", "_round": "The round from which to withdraw."}, "returns": {"reward": "The withdrawn amount."}}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address that made contributions.", "_contributedTo": "Answer that received contributions from contributor."}}}, "title": "Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain). This version of the contract has an appeal support.", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the foreign chain."}, "externalIDtoLocalID(uint256)": {"notice": "Maps external (arbitrator side) dispute id to local (arbitrable) dispute id."}, "fundAppeal(uint256,uint256)": {"notice": "Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded."}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"notice": "Gets contributions to the answers that are fully funded."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "getFundingStatus(uint256,uint256,uint256)": {"notice": "Gets the information of a round of a question for a specific answer choice."}, "getMultipliers()": {"notice": "Returns stake multipliers."}, "getNumberOfRounds(uint256)": {"notice": "Gets the number of rounds of the specific question."}, "getRoundInfo(uint256,uint256)": {"notice": "Gets the information of a round of a question."}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"notice": "Returns the sum of withdrawable amount."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created."}, "numberOfRulingOptions(uint256)": {"notice": "Returns number of possible ruling options. Valid rulings are [0, return value]."}, "questionIDToArbitrationID(bytes32)": {"notice": "Casts question ID into uint256 thus returning the related arbitration ID."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute. Can only be called by the arbitrator."}, "submitEvidence(uint256,string)": {"notice": "Allows to submit evidence for a particular question."}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"notice": "Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner."}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"notice": "Allows to withdraw any rewards or reimbursable fees for all rounds at once."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 1317, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitratorExtraData", "offset": 0, "slot": "0", "type": "t_bytes_storage"}, {"astId": 1325, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "termsOfService", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 1337, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationRequests", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)1288_storage))"}, {"astId": 1341, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "disputeIDToDisputeDetails", "offset": 0, "slot": "3", "type": "t_mapping(t_uint256,t_struct(DisputeDetails)1293_storage)"}, {"astId": 1345, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationIDToDisputeExists", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 1349, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationIDToRequester", "offset": 0, "slot": "5", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 1353, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationCreatedBlock", "offset": 0, "slot": "6", "type": "t_mapping(t_uint256,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_struct(Round)1313_storage)dyn_storage": {"base": "t_struct(Round)1313_storage", "encoding": "dynamic_array", "label": "struct RealitioForeignArbitrationProxyWithAppeals.Round[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_enum(Status)1276": {"encoding": "inplace", "label": "enum RealitioForeignArbitrationProxyWithAppeals.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_uint256)"}, "t_mapping(t_address,t_struct(ArbitrationRequest)1288_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignArbitrationProxyWithAppeals.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)1288_storage"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_uint256,t_bool)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)1288_storage))": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => mapping(address => struct RealitioForeignArbitrationProxyWithAppeals.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)1288_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)1293_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignArbitrationProxyWithAppeals.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)1293_storage"}, "t_mapping(t_uint256,t_uint256)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(ArbitrationRequest)1288_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxyWithAppeals.ArbitrationRequest", "members": [{"astId": 1278, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)1276"}, {"astId": 1280, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}, {"astId": 1282, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "disputeID", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 1284, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "answer", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 1287, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "rounds", "offset": 0, "slot": "3", "type": "t_array(t_struct(Round)1313_storage)dyn_storage"}], "numberOfBytes": "128"}, "t_struct(DisputeDetails)1293_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxyWithAppeals.DisputeDetails", "members": [{"astId": 1290, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationID", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 1292, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_struct(Round)1313_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxyWithAppeals.Round", "members": [{"astId": 1297, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "paidFees", "offset": 0, "slot": "0", "type": "t_mapping(t_uint256,t_uint256)"}, {"astId": 1301, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "hasPaid", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 1307, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "contributions", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))"}, {"astId": 1309, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "feeRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 1312, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "fundedAnswers", "offset": 0, "slot": "4", "type": "t_array(t_uint256)dyn_storage"}], "numberOfBytes": "160"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}
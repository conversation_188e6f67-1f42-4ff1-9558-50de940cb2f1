{"address": "0x776e5853e3d61B2dFB22Bcf872a43bF9A1231e52", "abi": [{"inputs": [{"internalType": "address", "name": "_checkpointManager", "type": "address"}, {"internalType": "address", "name": "_fxRoot", "type": "address"}, {"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "uint256", "name": "_winnerMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserAppealPeriodMultiplier", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "Contribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "RulingFunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "META_EVIDENCE_ID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTIPLIER_DIVISOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFUSE_TO_ARBITRATE_REALITIO", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "SEND_MESSAGE_EVENT_SIG", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignArbitrationProxyWithAppeals.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}, {"internalType": "uint256", "name": "disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "answer", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkpointManager", "outputs": [{"internalType": "contract ICheckpointManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "disputeIDToDisputeDetails", "outputs": [{"internalType": "uint256", "name": "arbitrationID", "type": "uint256"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_externalDisputeID", "type": "uint256"}], "name": "externalIDtoLocalID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "fundAppeal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "fxChildTunnel", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "fxRoot", "outputs": [{"internalType": "contract IFxStateSender", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "address", "name": "_contributor", "type": "address"}], "name": "getContributionsToSuccessfulFundings", "outputs": [{"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "contributions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "getFundingStatus", "outputs": [{"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "bool", "name": "fullyFunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMultipliers", "outputs": [{"internalType": "uint256", "name": "winner", "type": "uint256"}, {"internalType": "uint256", "name": "loser", "type": "uint256"}, {"internalType": "uint256", "name": "loserAppealPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "divisor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}], "name": "getNumberOfRounds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256[]", "name": "paidFees", "type": "uint256[]"}, {"internalType": "uint256", "name": "feeRewards", "type": "uint256"}, {"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "getTotalWithdrawableAmount", "outputs": [{"internalType": "uint256", "name": "sum", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "loserAppealPeriodMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "numberOfRulingOptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "processedExits", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "questionIDToArbitrationID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "inputData", "type": "bytes"}], "name": "receiveMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_fxChildTunnel", "type": "address"}], "name": "setFxChildTunnel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "string", "name": "_evidenceURI", "type": "string"}], "name": "submitEvidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "winner<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "withdrawFeesAndRewards", "outputs": [{"internalType": "uint256", "name": "reward", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "withdrawFeesAndRewardsForAllRounds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0x5764e238fbf9b9a6845353aa172a513eb20be6c441cf3c46aae43f49c63cb39a", "receipt": {"to": null, "from": "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "contractAddress": "0x776e5853e3d61B2dFB22Bcf872a43bF9A1231e52", "transactionIndex": 175, "gasUsed": "4391815", "logsBloom": "0x00000000000000000000000000000000000000000000000000000004008000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000000002000000000000000000000000000008000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x644468f2357182d8a2518b7b31425a657c51d48f6585aba6de73d1badebc1595", "transactionHash": "0x5764e238fbf9b9a6845353aa172a513eb20be6c441cf3c46aae43f49c63cb39a", "logs": [{"transactionIndex": 175, "blockNumber": 15867680, "transactionHash": "0x5764e238fbf9b9a6845353aa172a513eb20be6c441cf3c46aae43f49c63cb39a", "address": "0x776e5853e3d61B2dFB22Bcf872a43bF9A1231e52", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000422f697066732f516d585772345a57437042597441484e487a54624b5739536b56314d7751696370576674686844486b4e59784b6b2f7265616c6974696f2e6a736f6e000000000000000000000000000000000000000000000000000000000000", "logIndex": 78, "blockHash": "0x644468f2357182d8a2518b7b31425a657c51d48f6585aba6de73d1badebc1595"}], "blockNumber": 15867680, "cumulativeGasUsed": "13188227", "status": 1, "byzantium": true}, "args": ["0x86e4dc95c7fbdbf52e33d563bbdb00823894c287", "0xfe5e5D361b2ad62c541bAb87C45a0B9B018389a2", "0x988b3a538b618c7a603e1c11ab82cd16dbe28069", "0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f", "/ipfs/QmXWr4ZWCpBYtAHNHzTbKW9SkV1MwQicpWfthhDHkNYxKk/realitio.json", 3000, 7000, 5000], "numDeployments": 1, "solcInputHash": "7b805e84a75e070f31564c6690d6b6c4", "metadata": "{\"compiler\":{\"version\":\"0.8.0+commit.c7dfd78e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_checkpointManager\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_fxRoot\",\"type\":\"address\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_winnerMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserAppealPeriodMultiplier\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Contribution\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"RulingFunded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_reward\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"META_EVIDENCE_ID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MULTIPLIER_DIVISOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REFUSE_TO_ARBITRATE_REALITIO\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SEND_MESSAGE_EVENT_SIG\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignArbitrationProxyWithAppeals.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"},{\"internalType\":\"uint256\",\"name\":\"disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"answer\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"checkpointManager\",\"outputs\":[{\"internalType\":\"contract ICheckpointManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"disputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_externalDisputeID\",\"type\":\"uint256\"}],\"name\":\"externalIDtoLocalID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"fundAppeal\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"fxChildTunnel\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"fxRoot\",\"outputs\":[{\"internalType\":\"contract IFxStateSender\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"}],\"name\":\"getContributionsToSuccessfulFundings\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"contributions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"getFundingStatus\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"fullyFunded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMultipliers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"winner\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loser\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loserAppealPeriod\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"divisor\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"}],\"name\":\"getNumberOfRounds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"paidFees\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"feeRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"getTotalWithdrawableAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sum\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserAppealPeriodMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"numberOfRulingOptions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"processedExits\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"questionIDToArbitrationID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"inputData\",\"type\":\"bytes\"}],\"name\":\"receiveMessage\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_fxChildTunnel\",\"type\":\"address\"}],\"name\":\"setFxChildTunnel\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_evidenceURI\",\"type\":\"string\"}],\"name\":\"submitEvidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"winnerMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewardsForAllRounds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\",\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_checkpointManager\":\"For Polygon FX-portal bridge.\",\"_fxRoot\":\"Address of the FxRoot contract of the Polygon bridge.\",\"_loserAppealPeriodMultiplier\":\"Multiplier for calculating the appeal period for the losing answer.\",\"_loserMultiplier\":\"Multiplier for calculation the appeal cost of the losing answer.\",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_winnerMultiplier\":\"Multiplier for calculating the appeal cost of the winning answer.\"}},\"externalIDtoLocalID(uint256)\":{\"params\":{\"_externalDisputeID\":\"Dispute id as in arbitrator side.\"},\"returns\":{\"_0\":\"localDisputeID Dispute id as in arbitrable contract.\"}},\"fundAppeal(uint256,uint256)\":{\"params\":{\"_answer\":\"One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.\",\"_arbitrationID\":\"The ID of the arbitration, which is questionID cast into uint256.\"},\"returns\":{\"_0\":\"Whether the answer was fully funded or not.\"}},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_contributor\":\"The address whose contributions to query.\",\"_round\":\"The round to query.\"},\"returns\":{\"contributions\":\"The amount contributed to each funded answer by the contributor.\",\"fundedAnswers\":\"IDs of the answers that are fully funded.\"}},\"getDisputeFee(bytes32)\":{\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"getFundingStatus(uint256,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer choice to get funding status for.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"fullyFunded\":\"Whether the answer is fully funded or not.\",\"raised\":\"The amount paid for this answer.\"}},\"getMultipliers()\":{\"returns\":{\"divisor\":\"Multiplier divisor.\",\"loser\":\"Losers stake multiplier.\",\"loserAppealPeriod\":\"Multiplier for calculating an appeal period duration for the losing side.\",\"winner\":\"Winners stake multiplier.\"}},\"getNumberOfRounds(uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\"},\"returns\":{\"_0\":\"The number of rounds.\"}},\"getRoundInfo(uint256,uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"feeRewards\":\"The amount of fees that will be used as rewards.\",\"fundedAnswers\":\"IDs of fully funded answers.\",\"paidFees\":\"The amount of fees paid for each fully funded answer.\"}},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The contributor for which to query.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"},\"returns\":{\"sum\":\"The total amount available to withdraw.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"numberOfRulingOptions(uint256)\":{\"returns\":{\"_0\":\"count The number of ruling options.\"}},\"questionIDToArbitrationID(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"},\"returns\":{\"_0\":\"The ID of the arbitration.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveMessage(bytes)\":{\"details\":\"This function verifies if the transaction actually happened on child chain\",\"params\":{\"inputData\":\"RLP encoded data of the reference tx containing following list of fields  0 - headerNumber - Checkpoint header block number containing the reference tx  1 - blockProof - Proof that the block header (in the child chain) is a leaf in the submitted merkle root  2 - blockNumber - Block number containing the reference tx on child chain  3 - blockTime - Reference tx block time  4 - txRoot - Transactions root of block  5 - receiptRoot - Receipts root of block  6 - receipt - Receipt of the reference transaction  7 - receiptProof - Merkle proof of the reference receipt  8 - branchMask - 32 bits denoting the path of receipt in merkle tree  9 - receiptLogIndex - Log Index to read from the receipt\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}},\"submitEvidence(uint256,string)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\",\"_evidenceURI\":\"Link to evidence.\"}},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer to query the reward from.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address to send reward to.\",\"_round\":\"The round from which to withdraw.\"},\"returns\":{\"reward\":\"The withdrawn amount.\"}},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address that made contributions.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"}}},\"title\":\"Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain.\"},\"externalIDtoLocalID(uint256)\":{\"notice\":\"Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\"},\"fundAppeal(uint256,uint256)\":{\"notice\":\"Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\"},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"notice\":\"Gets contributions to the answers that are fully funded.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"getFundingStatus(uint256,uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question for a specific answer choice.\"},\"getMultipliers()\":{\"notice\":\"Returns stake multipliers.\"},\"getNumberOfRounds(uint256)\":{\"notice\":\"Gets the number of rounds of the specific question.\"},\"getRoundInfo(uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question.\"},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"notice\":\"Returns the sum of withdrawable amount.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created.\"},\"numberOfRulingOptions(uint256)\":{\"notice\":\"Returns number of possible ruling options. Valid rulings are [0, return value].\"},\"questionIDToArbitrationID(bytes32)\":{\"notice\":\"Casts question ID into uint256 thus returning the related arbitration ID.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveMessage(bytes)\":{\"notice\":\"receive message from  L2 to L1, validated by proof\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute. Can only be called by the arbitrator.\"},\"submitEvidence(uint256,string)\":{\"notice\":\"Allows to submit evidence for a particular question.\"},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"notice\":\"Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\"},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"notice\":\"Allows to withdraw any rewards or reimbursable fees for all rounds at once.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioForeignArbitrationProxyWithAppeals.sol\":\"RealitioForeignArbitrationProxyWithAppeals\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/dispute-resolver-interface-contract/contracts/IDisputeResolver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@ferittuncer]\\n *  @reviewers: [@mtsalenc*, @hbarcelos*, @unknownunknown1, @MerlinEgalite, @fnanni-0*, @shalzz]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\n\\n/**\\n *  @title This serves as a standard interface for crowdfunded appeals and evidence submission, which aren't a part of the arbitration (erc-792 and erc-1497) standard yet.\\n    This interface is used in Dispute Resolver (resolve.kleros.io).\\n */\\nabstract contract IDisputeResolver is IArbitrable, IEvidence {\\n    string public constant VERSION = \\\"2.0.0\\\"; // Can be used to distinguish between multiple deployed versions, if necessary.\\n\\n    /** @dev Raised when a contribution is made, inside fundAppeal function.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the contribution was made to.\\n     *  @param ruling Indicates the ruling option which got the contribution.\\n     *  @param _contributor Caller of fundAppeal function.\\n     *  @param _amount Contribution amount.\\n     */\\n    event Contribution(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 ruling, address indexed _contributor, uint256 _amount);\\n\\n    /** @dev Raised when a contributor withdraws non-zero value.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the withdrawal was made from.\\n     *  @param _ruling Indicates the ruling option which contributor gets rewards from.\\n     *  @param _contributor The beneficiary of withdrawal.\\n     *  @param _reward Total amount of withdrawal, consists of reimbursed deposits plus rewards.\\n     */\\n    event Withdrawal(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 _ruling, address indexed _contributor, uint256 _reward);\\n\\n    /** @dev To be raised when a ruling option is fully funded for appeal.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round Number of the round this ruling option was fully funded in.\\n     *  @param _ruling The ruling option which just got fully funded.\\n     */\\n    event RulingFunded(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 indexed _ruling);\\n\\n    /** @dev Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     *  @param _externalDisputeID Dispute id as in arbitrator contract.\\n     *  @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external virtual returns (uint256 localDisputeID);\\n\\n    /** @dev Returns number of possible ruling options. Valid rulings are [0, return value].\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 _localDisputeID) external view virtual returns (uint256 count);\\n\\n    /** @dev Allows to submit evidence for a given dispute.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _evidenceURI IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    function submitEvidence(uint256 _localDisputeID, string calldata _evidenceURI) external virtual;\\n\\n    /** @dev Manages contributions and calls appeal function of the specified arbitrator to appeal a dispute. This function lets appeals be crowdfunded.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _ruling The ruling option to which the caller wants to contribute.\\n     *  @return fullyFunded True if the ruling option got fully funded as a result of this contribution.\\n     */\\n    function fundAppeal(uint256 _localDisputeID, uint256 _ruling) external payable virtual returns (bool fullyFunded);\\n\\n    /** @dev Returns appeal multipliers.\\n     *  @return winnerStakeMultiplier Winners stake multiplier.\\n     *  @return loserStakeMultiplier Losers stake multiplier.\\n     *  @return loserAppealPeriodMultiplier Losers appeal period multiplier. The loser is given less time to fund its appeal to defend against last minute appeal funding attacks.\\n     *  @return denominator Multiplier denominator in basis points.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        virtual\\n        returns (\\n            uint256 winnerStakeMultiplier,\\n            uint256 loserStakeMultiplier,\\n            uint256 loserAppealPeriodMultiplier,\\n            uint256 denominator\\n        );\\n\\n    /** @dev Allows to withdraw any reimbursable fees or rewards after the dispute gets resolved.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _round Number of the round that caller wants to execute withdraw on.\\n     *  @param _ruling A ruling option that caller wants to execute withdraw on.\\n     *  @return sum The amount that is going to be transferred to contributor as a result of this function call.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _round,\\n        uint256 _ruling\\n    ) external virtual returns (uint256 sum);\\n\\n    /** @dev Allows to withdraw any rewards or reimbursable fees after the dispute gets resolved for all rounds at once.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to execute withdraw on.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external virtual;\\n\\n    /** @dev Returns the sum of withdrawable amount.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to get withdrawable amount from.\\n     *  @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external view virtual returns (uint256 sum);\\n}\\n\",\"keccak256\":\"0x9174a37ba69e682381a3ae6e14582a17d69f29be879ff27433fce2b971f871ae\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0xf1a2c2d7ec1237ef8d3c5f580ac73f56ed58fe4d023817a188363885b3eeb9f2\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {\\n        Waiting,\\n        Appealable,\\n        Solved\\n    }\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0xfd19582446ef635cfb02a035a18efae3bc242ccf1472bb9949cad3d291306333\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0xf9f105a2cbf5e34cdc5ce71d877cded1b502437f1cd6d28173898f88542418af\",\"license\":\"MIT\"},\"src/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\r\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\r\\n\\r\\ninterface IHomeArbitrationProxy {\\r\\n    /**\\r\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when arbitration request is rejected.\\r\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\r\\n     * or if the question is already finalized.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\r\\n     * @param _reason The reason why the request was rejected.\\r\\n     */\\r\\n    event RequestRejected(\\r\\n        bytes32 indexed _questionID,\\r\\n        address indexed _requester,\\r\\n        uint256 _maxPrevious,\\r\\n        string _reason\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request is canceled.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\r\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\r\\n     * @param _questionID The ID of the question.\\r\\n     */\\r\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function receiveArbitrationRequest(\\r\\n        bytes32 _questionID,\\r\\n        address _requester,\\r\\n        uint256 _maxPrevious\\r\\n    ) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\r\\n\\r\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\r\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\r\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\r\\n     *  @dev Template_hashes won't be used by this home proxy. \\r\\n     */\\r\\n    function metadata() external view returns (string calldata);\\r\\n}\\r\\n\\r\\ninterface IForeignArbitrationProxy is IArbitrable, IEvidence {\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is requested.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute is created.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _disputeID The ID of the dispute.\\r\\n     */\\r\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute could not be created.\\r\\n     * @dev This will happen if there is an increase in the arbitration fees\\r\\n     * between the time the arbitration is made and the time it is acknowledged.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @param _questionID the ID of the question.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\r\\n}\\r\\n\",\"keccak256\":\"0x9b9c5fe1c88b7f87e6e4bfc39a02c3d933afba1ea58b322e5ed5358d298b7a00\",\"license\":\"MIT\"},\"src/RealitioForeignArbitrationProxyWithAppeals.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n *  @authors: [@hbarcelos, @unknownunknown1, @shalzz]\\r\\n *  @reviewers: [@MerlinEgalite*, @jaybuidl, @unknownunknown1, @fnanni-0*]\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\nimport {IDisputeResolver, IArbitrator} from \\\"@kleros/dispute-resolver-interface-contract/contracts/IDisputeResolver.sol\\\";\\r\\nimport {CappedMath} from \\\"./dependencies/lib/CappedMath.sol\\\";\\r\\nimport {FxBaseRootTunnel} from \\\"./dependencies/FxBaseRootTunnel.sol\\\";\\r\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./ArbitrationProxyInterfaces.sol\\\";\\r\\n\\r\\n/**\\r\\n * @title Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\\r\\n * @dev This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\\r\\n */\\r\\ncontract RealitioForeignArbitrationProxyWithAppeals is IForeignArbitrationProxy, IDisputeResolver, FxBaseRootTunnel {\\r\\n    using CappedMath for uint256;\\r\\n\\r\\n    /* Constants */\\r\\n    // The number of choices for the arbitrator.\\r\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max;\\r\\n    uint256 public constant REFUSE_TO_ARBITRATE_REALITIO = type(uint256).max; // Constant that represents \\\"Refuse to rule\\\" in realitio format.\\r\\n    uint256 public constant MULTIPLIER_DIVISOR = 10000; // Divisor parameter for multipliers.\\r\\n    uint256 public constant META_EVIDENCE_ID = 0; // The ID of the MetaEvidence for disputes.\\r\\n\\r\\n    /* Storage */\\r\\n\\r\\n    enum Status {\\r\\n        None,\\r\\n        Requested,\\r\\n        Created,\\r\\n        Ruled,\\r\\n        Failed\\r\\n    }\\r\\n\\r\\n    struct ArbitrationRequest {\\r\\n        Status status; // Status of the arbitration.\\r\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\r\\n        uint256 disputeID; // The ID of the dispute in arbitrator contract.\\r\\n        uint256 answer; // The answer given by the arbitrator.\\r\\n        Round[] rounds; // Tracks each appeal round of a dispute.\\r\\n    }\\r\\n\\r\\n    struct DisputeDetails {\\r\\n        uint256 arbitrationID; // The ID of the arbitration.\\r\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\r\\n    }\\r\\n\\r\\n    // Round struct stores the contributions made to particular answers.\\r\\n    struct Round {\\r\\n        mapping(uint256 => uint256) paidFees; // Tracks the fees paid in this round in the form paidFees[answer].\\r\\n        mapping(uint256 => bool) hasPaid; // True if the fees for this particular answer have been fully paid in the form hasPaid[answer].\\r\\n        mapping(address => mapping(uint256 => uint256)) contributions; // Maps contributors to their contributions for each answer in the form contributions[address][answer].\\r\\n        uint256 feeRewards; // Sum of reimbursable appeal fees available to the parties that made contributions to the answer that ultimately wins a dispute.\\r\\n        uint256[] fundedAnswers; // Stores the answer choices that are fully funded.\\r\\n    }\\r\\n\\r\\n    IArbitrator public immutable arbitrator; // The address of the arbitrator. TRUSTED.\\r\\n    bytes public arbitratorExtraData; // The extra data used to raise a dispute in the arbitrator.\\r\\n\\r\\n    // Multipliers are in basis points.\\r\\n    uint256 public immutable winnerMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that was chosen by the arbitrator in the previous round.\\r\\n    uint256 public immutable loserMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that the arbitrator didn't rule for in the previous round.\\r\\n    uint256 public immutable loserAppealPeriodMultiplier; // Multiplier for calculating the duration of the appeal period for the loser, in basis points.\\r\\n\\r\\n    mapping(uint256 => mapping(address => ArbitrationRequest)) public arbitrationRequests; // Maps arbitration ID to its data. arbitrationRequests[uint(questionID)][requester].\\r\\n    mapping(uint256 => DisputeDetails) public disputeIDToDisputeDetails; // Maps external dispute ids to local arbitration ID and requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => bool) public arbitrationIDToDisputeExists; // Whether a dispute has already been created for the given arbitration ID or not.\\r\\n    mapping(uint256 => address) public arbitrationIDToRequester; // Maps arbitration ID to the requester who was able to complete the arbitration request.\\r\\n\\r\\n    /**\\r\\n     * @dev This is applied to functions called via the internal function\\r\\n     * `_processMessageFromChild` which is invoked via the Polygon bridge (see FxBaseRootTunnel)\\r\\n     *\\r\\n     * The functions requiring this modifier cannot simply be declared internal as\\r\\n     * we still need the ABI generated of these functions to be able to call them\\r\\n     * across contracts and have the compiler type check the function signatures.\\r\\n     */\\r\\n    modifier onlyBridge() {\\r\\n        require(msg.sender == address(this), \\\"Can only be called via bridge\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Creates an arbitration proxy on the foreign chain.\\r\\n     * @param _checkpointManager For Polygon FX-portal bridge.\\r\\n     * @param _fxRoot Address of the FxRoot contract of the Polygon bridge.\\r\\n     * @param _arbitrator Arbitrator contract address.\\r\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\r\\n     * @param _metaEvidence The URI of the meta evidence file.\\r\\n     * @param _winnerMultiplier Multiplier for calculating the appeal cost of the winning answer.\\r\\n     * @param _loserMultiplier Multiplier for calculation the appeal cost of the losing answer.\\r\\n     * @param _loserAppealPeriodMultiplier Multiplier for calculating the appeal period for the losing answer.\\r\\n     */\\r\\n    constructor(\\r\\n        address _checkpointManager,\\r\\n        address _fxRoot,\\r\\n        IArbitrator _arbitrator,\\r\\n        bytes memory _arbitratorExtraData,\\r\\n        string memory _metaEvidence,\\r\\n        uint256 _winnerMultiplier,\\r\\n        uint256 _loserMultiplier,\\r\\n        uint256 _loserAppealPeriodMultiplier\\r\\n    ) FxBaseRootTunnel(_checkpointManager, _fxRoot) {\\r\\n        arbitrator = _arbitrator;\\r\\n        arbitratorExtraData = _arbitratorExtraData;\\r\\n        winnerMultiplier = _winnerMultiplier;\\r\\n        loserMultiplier = _loserMultiplier;\\r\\n        loserAppealPeriodMultiplier = _loserAppealPeriodMultiplier;\\r\\n\\r\\n        emit MetaEvidence(META_EVIDENCE_ID, _metaEvidence);\\r\\n    }\\r\\n\\r\\n    /* External and public */\\r\\n\\r\\n    // ************************ //\\r\\n    // *    Realitio logic    * //\\r\\n    // ************************ //\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\r\\n        require(!arbitrationIDToDisputeExists[uint256(_questionID)], \\\"Dispute already created\\\");\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[uint256(_questionID)][msg.sender];\\r\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\r\\n\\r\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n        require(msg.value >= arbitrationCost, \\\"Deposit value too low\\\");\\r\\n\\r\\n        arbitration.status = Status.Requested;\\r\\n        arbitration.deposit = uint248(msg.value);\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationRequest.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\r\\n        _sendMessageToChild(data);\\r\\n\\r\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external override onlyBridge {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n\\r\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n\\r\\n        if (arbitration.deposit >= arbitrationCost) {\\r\\n            try\\r\\n                arbitrator.createDispute{value: arbitrationCost}(NUMBER_OF_CHOICES_FOR_ARBITRATOR, arbitratorExtraData)\\r\\n            returns (uint256 disputeID) {\\r\\n                DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[disputeID];\\r\\n                disputeDetails.arbitrationID = arbitrationID;\\r\\n                disputeDetails.requester = _requester;\\r\\n\\r\\n                arbitrationIDToDisputeExists[arbitrationID] = true;\\r\\n                arbitrationIDToRequester[arbitrationID] = _requester;\\r\\n\\r\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\r\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\r\\n\\r\\n                arbitration.status = Status.Created;\\r\\n                arbitration.deposit = 0;\\r\\n                arbitration.disputeID = disputeID;\\r\\n                arbitration.rounds.push();\\r\\n\\r\\n                if (remainder > 0) {\\r\\n                    payable(_requester).send(remainder);\\r\\n                }\\r\\n\\r\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\r\\n                emit Dispute(arbitrator, disputeID, META_EVIDENCE_ID, arbitrationID);\\r\\n            } catch {\\r\\n                arbitration.status = Status.Failed;\\r\\n                emit ArbitrationFailed(_questionID, _requester);\\r\\n            }\\r\\n        } else {\\r\\n            arbitration.status = Status.Failed;\\r\\n            emit ArbitrationFailed(_questionID, _requester);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external override onlyBridge {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external override {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationFailure.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\r\\n        _sendMessageToChild(data);\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    // ********************************* //\\r\\n    // *    Appeals and arbitration    * //\\r\\n    // ********************************* //\\r\\n\\r\\n    /**\\r\\n     * @notice Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\\r\\n     * @param _arbitrationID The ID of the arbitration, which is questionID cast into uint256.\\r\\n     * @param _answer One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question.\\r\\n     * Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format.\\r\\n     * Also note that '0' answer can be funded.\\r\\n     * @return Whether the answer was fully funded or not.\\r\\n     */\\r\\n    function fundAppeal(uint256 _arbitrationID, uint256 _answer) external payable override returns (bool) {\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][\\r\\n            arbitrationIDToRequester[_arbitrationID]\\r\\n        ];\\r\\n        require(arbitration.status == Status.Created, \\\"No dispute to appeal.\\\");\\r\\n\\r\\n        uint256 disputeID = arbitration.disputeID;\\r\\n        (uint256 appealPeriodStart, uint256 appealPeriodEnd) = arbitrator.appealPeriod(disputeID);\\r\\n        require(block.timestamp >= appealPeriodStart && block.timestamp < appealPeriodEnd, \\\"Appeal period is over.\\\");\\r\\n\\r\\n        uint256 multiplier;\\r\\n        {\\r\\n            uint256 winner = arbitrator.currentRuling(disputeID);\\r\\n            if (winner == _answer) {\\r\\n                multiplier = winnerMultiplier;\\r\\n            } else {\\r\\n                require(\\r\\n                    block.timestamp - appealPeriodStart <\\r\\n                        (appealPeriodEnd - appealPeriodStart).mulCap(loserAppealPeriodMultiplier) / MULTIPLIER_DIVISOR,\\r\\n                    \\\"Appeal period is over for loser\\\"\\r\\n                );\\r\\n                multiplier = loserMultiplier;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        uint256 lastRoundID = arbitration.rounds.length - 1;\\r\\n        Round storage round = arbitration.rounds[lastRoundID];\\r\\n        require(!round.hasPaid[_answer], \\\"Appeal fee is already paid.\\\");\\r\\n        uint256 appealCost = arbitrator.appealCost(disputeID, arbitratorExtraData);\\r\\n        uint256 totalCost = appealCost.addCap((appealCost.mulCap(multiplier)) / MULTIPLIER_DIVISOR);\\r\\n\\r\\n        // Take up to the amount necessary to fund the current round at the current costs.\\r\\n        uint256 contribution = totalCost.subCap(round.paidFees[_answer]) > msg.value\\r\\n            ? msg.value\\r\\n            : totalCost.subCap(round.paidFees[_answer]);\\r\\n        emit Contribution(_arbitrationID, lastRoundID, _answer, msg.sender, contribution);\\r\\n\\r\\n        round.contributions[msg.sender][_answer] += contribution;\\r\\n        round.paidFees[_answer] += contribution;\\r\\n        if (round.paidFees[_answer] >= totalCost) {\\r\\n            round.feeRewards += round.paidFees[_answer];\\r\\n            round.fundedAnswers.push(_answer);\\r\\n            round.hasPaid[_answer] = true;\\r\\n            emit RulingFunded(_arbitrationID, lastRoundID, _answer);\\r\\n        }\\r\\n\\r\\n        if (round.fundedAnswers.length > 1) {\\r\\n            // At least two sides are fully funded.\\r\\n            arbitration.rounds.push();\\r\\n\\r\\n            round.feeRewards = round.feeRewards.subCap(appealCost);\\r\\n            arbitrator.appeal{value: appealCost}(disputeID, arbitratorExtraData);\\r\\n        }\\r\\n\\r\\n        if (msg.value.subCap(contribution) > 0) payable(msg.sender).send(msg.value.subCap(contribution)); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\r\\n        return round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address to send reward to.\\r\\n     * @param _round The round from which to withdraw.\\r\\n     * @param _answer The answer to query the reward from.\\r\\n     * @return reward The withdrawn amount.\\r\\n     */\\r\\n    function withdrawFeesAndRewards(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) public override returns (uint256 reward) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\r\\n        // Allow to reimburse if funding of the round was unsuccessful.\\r\\n        if (!round.hasPaid[_answer]) {\\r\\n            reward = round.contributions[_beneficiary][_answer];\\r\\n        } else if (!round.hasPaid[arbitration.answer]) {\\r\\n            // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n            // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n            reward = round.fundedAnswers.length > 1\\r\\n                ? (round.contributions[_beneficiary][_answer] * round.feeRewards) /\\r\\n                    (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                : 0;\\r\\n        } else if (arbitration.answer == _answer) {\\r\\n            uint256 paidFees = round.paidFees[_answer];\\r\\n            // Reward the winner.\\r\\n            reward = paidFees > 0 ? (round.contributions[_beneficiary][_answer] * round.feeRewards) / paidFees : 0;\\r\\n        }\\r\\n\\r\\n        if (reward != 0) {\\r\\n            round.contributions[_beneficiary][_answer] = 0;\\r\\n            _beneficiary.send(reward); // It is the user's responsibility to accept ETH.\\r\\n            emit Withdrawal(_arbitrationID, _round, _answer, _beneficiary, reward);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to withdraw any rewards or reimbursable fees for all rounds at once.\\r\\n     * @dev This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.\\r\\n     *      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address that made contributions.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     */\\r\\n    function withdrawFeesAndRewardsForAllRounds(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external override {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n\\r\\n        uint256 numberOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < numberOfRounds; roundNumber++) {\\r\\n            withdrawFeesAndRewards(_arbitrationID, _beneficiary, roundNumber, _contributedTo);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to submit evidence for a particular question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @param _evidenceURI Link to evidence.\\r\\n     */\\r\\n    function submitEvidence(uint256 _arbitrationID, string calldata _evidenceURI) external override {\\r\\n        emit Evidence(arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Rules a specified dispute. Can only be called by the arbitrator.\\r\\n     * @dev Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\\r\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\r\\n     * @param _ruling The ruling given by the arbitrator.\\r\\n     */\\r\\n    function rule(uint256 _disputeID, uint256 _ruling) external override {\\r\\n        DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[_disputeID];\\r\\n        uint256 arbitrationID = disputeDetails.arbitrationID;\\r\\n        address requester = disputeDetails.requester;\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][requester];\\r\\n        require(msg.sender == address(arbitrator), \\\"Only arbitrator allowed\\\");\\r\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\r\\n        uint256 finalRuling = _ruling;\\r\\n        uint256 realitioRuling; // Realitio ruling is shifted by 1 compared to Kleros.\\r\\n\\r\\n        // If one side paid its fees, the ruling is in its favor. Note that if the other side had also paid, an appeal would have been created.\\r\\n        Round storage round = arbitration.rounds[arbitration.rounds.length - 1];\\r\\n        if (round.fundedAnswers.length == 1) finalRuling = round.fundedAnswers[0];\\r\\n\\r\\n        arbitration.answer = finalRuling;\\r\\n        arbitration.status = Status.Ruled;\\r\\n\\r\\n        realitioRuling = finalRuling != 0 ? finalRuling - 1 : REFUSE_TO_ARBITRATE_REALITIO;\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationAnswer.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, bytes32(arbitrationID), bytes32(realitioRuling));\\r\\n        _sendMessageToChild(data);\\r\\n\\r\\n        emit Ruling(arbitrator, _disputeID, finalRuling);\\r\\n    }\\r\\n\\r\\n    /* External Views */\\r\\n\\r\\n    /**\\r\\n     * @notice Returns stake multipliers.\\r\\n     * @return winner Winners stake multiplier.\\r\\n     * @return loser Losers stake multiplier.\\r\\n     * @return loserAppealPeriod Multiplier for calculating an appeal period duration for the losing side.\\r\\n     * @return divisor Multiplier divisor.\\r\\n     */\\r\\n    function getMultipliers()\\r\\n        external\\r\\n        view\\r\\n        override\\r\\n        returns (\\r\\n            uint256 winner,\\r\\n            uint256 loser,\\r\\n            uint256 loserAppealPeriod,\\r\\n            uint256 divisor\\r\\n        )\\r\\n    {\\r\\n        return (winnerMultiplier, loserMultiplier, loserAppealPeriodMultiplier, MULTIPLIER_DIVISOR);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns number of possible ruling options. Valid rulings are [0, return value].\\r\\n     * @return count The number of ruling options.\\r\\n     */\\r\\n    function numberOfRulingOptions(\\r\\n        uint256 /* _arbitrationID */\\r\\n    ) external pure override returns (uint256) {\\r\\n        return NUMBER_OF_CHOICES_FOR_ARBITRATOR;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(\\r\\n        bytes32 /* _questionID */\\r\\n    ) external view override returns (uint256) {\\r\\n        return arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the number of rounds of the specific question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @return The number of rounds.\\r\\n     */\\r\\n    function getNumberOfRounds(uint256 _arbitrationID) external view returns (uint256) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        return arbitration.rounds.length;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @return paidFees The amount of fees paid for each fully funded answer.\\r\\n     * @return feeRewards The amount of fees that will be used as rewards.\\r\\n     * @return fundedAnswers IDs of fully funded answers.\\r\\n     */\\r\\n    function getRoundInfo(uint256 _arbitrationID, uint256 _round)\\r\\n        external\\r\\n        view\\r\\n        returns (\\r\\n            uint256[] memory paidFees,\\r\\n            uint256 feeRewards,\\r\\n            uint256[] memory fundedAnswers\\r\\n        )\\r\\n    {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n\\r\\n        paidFees = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < round.fundedAnswers.length; i++) {\\r\\n            paidFees[i] = round.paidFees[round.fundedAnswers[i]];\\r\\n        }\\r\\n\\r\\n        feeRewards = round.feeRewards;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question for a specific answer choice.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _answer The answer choice to get funding status for.\\r\\n     * @return raised The amount paid for this answer.\\r\\n     * @return fullyFunded Whether the answer is fully funded or not.\\r\\n     */\\r\\n    function getFundingStatus(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) external view returns (uint256 raised, bool fullyFunded) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        raised = round.paidFees[_answer];\\r\\n        fullyFunded = round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets contributions to the answers that are fully funded.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _contributor The address whose contributions to query.\\r\\n     * @return fundedAnswers IDs of the answers that are fully funded.\\r\\n     * @return contributions The amount contributed to each funded answer by the contributor.\\r\\n     */\\r\\n    function getContributionsToSuccessfulFundings(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        address _contributor\\r\\n    ) external view returns (uint256[] memory fundedAnswers, uint256[] memory contributions) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n        contributions = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < contributions.length; i++) {\\r\\n            contributions[i] = round.contributions[_contributor][fundedAnswers[i]];\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns the sum of withdrawable amount.\\r\\n     * @dev This function is O(n) where n is the total number of rounds.\\r\\n     * @dev This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The contributor for which to query.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     * @return sum The total amount available to withdraw.\\r\\n     */\\r\\n    function getTotalWithdrawableAmount(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external view override returns (uint256 sum) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        if (arbitration.status < Status.Ruled) return sum;\\r\\n\\r\\n        uint256 finalAnswer = arbitration.answer;\\r\\n        uint256 noOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < noOfRounds; roundNumber++) {\\r\\n            Round storage round = arbitration.rounds[roundNumber];\\r\\n\\r\\n            if (!round.hasPaid[_contributedTo]) {\\r\\n                // Allow to reimburse if funding was unsuccessful for this answer option.\\r\\n                sum += round.contributions[_beneficiary][_contributedTo];\\r\\n            } else if (!round.hasPaid[finalAnswer]) {\\r\\n                // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n                // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n                sum += round.fundedAnswers.length > 1\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) /\\r\\n                        (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                    : 0;\\r\\n            } else if (finalAnswer == _contributedTo) {\\r\\n                uint256 paidFees = round.paidFees[_contributedTo];\\r\\n                // Reward the winner.\\r\\n                sum += paidFees > 0\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) / paidFees\\r\\n                    : 0;\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Casts question ID into uint256 thus returning the related arbitration ID.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @return The ID of the arbitration.\\r\\n     */\\r\\n    function questionIDToArbitrationID(bytes32 _questionID) external pure returns (uint256) {\\r\\n        return uint256(_questionID);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\r\\n     * @param _externalDisputeID Dispute id as in arbitrator side.\\r\\n     * @return localDisputeID Dispute id as in arbitrable contract.\\r\\n     */\\r\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external view override returns (uint256) {\\r\\n        return disputeIDToDisputeDetails[_externalDisputeID].arbitrationID;\\r\\n    }\\r\\n\\r\\n    function _processMessageFromChild(bytes memory _data) internal override {\\r\\n        // solhint-disable-next-line avoid-low-level-calls\\r\\n        (bool success, ) = address(this).call(_data);\\r\\n        require(success, \\\"Failed to call contract\\\");\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x065349533e79ef36dd6bec2db10ee306aae918f675982fdde9c42c8c08f3d817\",\"license\":\"MIT\"},\"src/dependencies/FxBaseRootTunnel.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n// https://github.com/fx-portal/contracts/blob/v1.0.5/contracts/tunnel/FxBaseRootTunnel.sol\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\nimport {RLPReader} from \\\"./lib/RLPReader.sol\\\";\\r\\nimport {MerklePatriciaProof} from \\\"./lib/MerklePatriciaProof.sol\\\";\\r\\nimport {Merkle} from \\\"./lib/Merkle.sol\\\";\\r\\nimport \\\"./lib/ExitPayloadReader.sol\\\";\\r\\n\\r\\ninterface IFxStateSender {\\r\\n    function sendMessageToChild(address _receiver, bytes calldata _data) external;\\r\\n}\\r\\n\\r\\ncontract ICheckpointManager {\\r\\n    struct HeaderBlock {\\r\\n        bytes32 root;\\r\\n        uint256 start;\\r\\n        uint256 end;\\r\\n        uint256 createdAt;\\r\\n        address proposer;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice mapping of checkpoint header numbers to block details\\r\\n     * @dev These checkpoints are submited by plasma contracts\\r\\n     */\\r\\n    mapping(uint256 => HeaderBlock) public headerBlocks;\\r\\n}\\r\\n\\r\\nabstract contract FxBaseRootTunnel {\\r\\n    using RLPReader for RLPReader.RLPItem;\\r\\n    using Merkle for bytes32;\\r\\n    using ExitPayloadReader for bytes;\\r\\n    using ExitPayloadReader for ExitPayloadReader.ExitPayload;\\r\\n    using ExitPayloadReader for ExitPayloadReader.Log;\\r\\n    using ExitPayloadReader for ExitPayloadReader.LogTopics;\\r\\n    using ExitPayloadReader for ExitPayloadReader.Receipt;\\r\\n\\r\\n    // keccak256(MessageSent(bytes))\\r\\n    bytes32 public constant SEND_MESSAGE_EVENT_SIG = 0x8c5261668696ce22758910d05bab8f186d6eb247ceac2af2e82c7dc17669b036;\\r\\n\\r\\n    // state sender contract\\r\\n    IFxStateSender public fxRoot;\\r\\n    // root chain manager\\r\\n    ICheckpointManager public checkpointManager;\\r\\n    // child tunnel contract which receives and sends messages\\r\\n    address public fxChildTunnel;\\r\\n\\r\\n    // storage to avoid duplicate exits\\r\\n    mapping(bytes32 => bool) public processedExits;\\r\\n\\r\\n    constructor(address _checkpointManager, address _fxRoot) {\\r\\n        checkpointManager = ICheckpointManager(_checkpointManager);\\r\\n        fxRoot = IFxStateSender(_fxRoot);\\r\\n    }\\r\\n\\r\\n    // set fxChildTunnel if not set already\\r\\n    function setFxChildTunnel(address _fxChildTunnel) public {\\r\\n        require(fxChildTunnel == address(0x0), \\\"FxBaseRootTunnel: CHILD_TUNNEL_ALREADY_SET\\\");\\r\\n        fxChildTunnel = _fxChildTunnel;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Send bytes message to Child Tunnel\\r\\n     * @param message bytes message that will be sent to Child Tunnel\\r\\n     * some message examples -\\r\\n     *   abi.encode(tokenId);\\r\\n     *   abi.encode(tokenId, tokenMetadata);\\r\\n     *   abi.encode(messageType, messageData);\\r\\n     */\\r\\n    function _sendMessageToChild(bytes memory message) internal {\\r\\n        fxRoot.sendMessageToChild(fxChildTunnel, message);\\r\\n    }\\r\\n\\r\\n    function _validateAndExtractMessage(bytes memory inputData) internal returns (bytes memory) {\\r\\n        ExitPayloadReader.ExitPayload memory payload = inputData.toExitPayload();\\r\\n\\r\\n        bytes memory branchMaskBytes = payload.getBranchMaskAsBytes();\\r\\n        uint256 blockNumber = payload.getBlockNumber();\\r\\n        // checking if exit has already been processed\\r\\n        // unique exit is identified using hash of (blockNumber, branchMask, receiptLogIndex)\\r\\n        bytes32 exitHash = keccak256(\\r\\n            abi.encodePacked(\\r\\n                blockNumber,\\r\\n                // first 2 nibbles are dropped while generating nibble array\\r\\n                // this allows branch masks that are valid but bypass exitHash check (changing first 2 nibbles only)\\r\\n                // so converting to nibble array and then hashing it\\r\\n                MerklePatriciaProof._getNibbleArray(branchMaskBytes),\\r\\n                payload.getReceiptLogIndex()\\r\\n            )\\r\\n        );\\r\\n        require(processedExits[exitHash] == false, \\\"FxRootTunnel: EXIT_ALREADY_PROCESSED\\\");\\r\\n        processedExits[exitHash] = true;\\r\\n\\r\\n        ExitPayloadReader.Receipt memory receipt = payload.getReceipt();\\r\\n        ExitPayloadReader.Log memory log = receipt.getLog();\\r\\n\\r\\n        // check child tunnel\\r\\n        require(fxChildTunnel == log.getEmitter(), \\\"FxRootTunnel: INVALID_FX_CHILD_TUNNEL\\\");\\r\\n\\r\\n        bytes32 receiptRoot = payload.getReceiptRoot();\\r\\n        // verify receipt inclusion\\r\\n        require(\\r\\n            MerklePatriciaProof.verify(receipt.toBytes(), branchMaskBytes, payload.getReceiptProof(), receiptRoot),\\r\\n            \\\"FxRootTunnel: INVALID_RECEIPT_PROOF\\\"\\r\\n        );\\r\\n\\r\\n        // verify checkpoint inclusion\\r\\n        _checkBlockMembershipInCheckpoint(\\r\\n            blockNumber,\\r\\n            payload.getBlockTime(),\\r\\n            payload.getTxRoot(),\\r\\n            receiptRoot,\\r\\n            payload.getHeaderNumber(),\\r\\n            payload.getBlockProof()\\r\\n        );\\r\\n\\r\\n        ExitPayloadReader.LogTopics memory topics = log.getTopics();\\r\\n\\r\\n        require(\\r\\n            bytes32(topics.getField(0).toUint()) == SEND_MESSAGE_EVENT_SIG, // topic0 is event sig\\r\\n            \\\"FxRootTunnel: INVALID_SIGNATURE\\\"\\r\\n        );\\r\\n\\r\\n        // received message data\\r\\n        bytes memory message = abi.decode(log.getData(), (bytes)); // event decodes params again, so decoding bytes to get message\\r\\n        return message;\\r\\n    }\\r\\n\\r\\n    function _checkBlockMembershipInCheckpoint(\\r\\n        uint256 blockNumber,\\r\\n        uint256 blockTime,\\r\\n        bytes32 txRoot,\\r\\n        bytes32 receiptRoot,\\r\\n        uint256 headerNumber,\\r\\n        bytes memory blockProof\\r\\n    ) private view returns (uint256) {\\r\\n        (bytes32 headerRoot, uint256 startBlock, , uint256 createdAt, ) = checkpointManager.headerBlocks(headerNumber);\\r\\n\\r\\n        require(\\r\\n            keccak256(abi.encodePacked(blockNumber, blockTime, txRoot, receiptRoot)).checkMembership(\\r\\n                blockNumber - startBlock,\\r\\n                headerRoot,\\r\\n                blockProof\\r\\n            ),\\r\\n            \\\"FxRootTunnel: INVALID_HEADER\\\"\\r\\n        );\\r\\n        return createdAt;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice receive message from  L2 to L1, validated by proof\\r\\n     * @dev This function verifies if the transaction actually happened on child chain\\r\\n     *\\r\\n     * @param inputData RLP encoded data of the reference tx containing following list of fields\\r\\n     *  0 - headerNumber - Checkpoint header block number containing the reference tx\\r\\n     *  1 - blockProof - Proof that the block header (in the child chain) is a leaf in the submitted merkle root\\r\\n     *  2 - blockNumber - Block number containing the reference tx on child chain\\r\\n     *  3 - blockTime - Reference tx block time\\r\\n     *  4 - txRoot - Transactions root of block\\r\\n     *  5 - receiptRoot - Receipts root of block\\r\\n     *  6 - receipt - Receipt of the reference transaction\\r\\n     *  7 - receiptProof - Merkle proof of the reference receipt\\r\\n     *  8 - branchMask - 32 bits denoting the path of receipt in merkle tree\\r\\n     *  9 - receiptLogIndex - Log Index to read from the receipt\\r\\n     */\\r\\n    function receiveMessage(bytes memory inputData) public {\\r\\n        bytes memory message = _validateAndExtractMessage(inputData);\\r\\n        _processMessageFromChild(message);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Process message received from Child Tunnel\\r\\n     * @dev function needs to be implemented to handle message as per requirement\\r\\n     * This is called by onStateReceive function.\\r\\n     * Since it is called via a system call, any event will not be emitted during its execution.\\r\\n     * @param message bytes message that was sent from Child Tunnel\\r\\n     */\\r\\n    function _processMessageFromChild(bytes memory message) internal virtual;\\r\\n}\\r\\n\",\"keccak256\":\"0x07c060a4a9882350b9579d29c7b0edfa0a66c23d3e7b1f5b301ef15fa7dd50c8\",\"license\":\"MIT\"},\"src/dependencies/lib/CappedMath.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n * @authors: [@mtsalenc, @hbarcelos]\\r\\n * @reviewers: [@clesaege*, @ferittuncer]\\r\\n * @auditors: []\\r\\n * @bounties: []\\r\\n * @deployments: []\\r\\n * SPDX-License-Identifier: MIT\\r\\n */\\r\\n\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\n/**\\r\\n * @title CappedMath\\r\\n * @dev Math operations with caps for under and overflow.\\r\\n */\\r\\nlibrary CappedMath {\\r\\n    uint256 private constant UINT_MAX = 2**256 - 1;\\r\\n\\r\\n    /**\\r\\n     * @dev Adds two unsigned integers, returns 2^256 - 1 on overflow.\\r\\n     */\\r\\n    function addCap(uint256 _a, uint256 _b) internal pure returns (uint256) {\\r\\n        uint256 c = _a + _b;\\r\\n        return c >= _a ? c : UINT_MAX;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @dev Subtracts two integers, returns 0 on underflow.\\r\\n     */\\r\\n    function subCap(uint256 _a, uint256 _b) internal pure returns (uint256) {\\r\\n        if (_b > _a) return 0;\\r\\n        else return _a - _b;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @dev Multiplies two unsigned integers, returns 2^256 - 1 on overflow.\\r\\n     */\\r\\n    function mulCap(uint256 _a, uint256 _b) internal pure returns (uint256) {\\r\\n        // Gas optimization: this is cheaper than requiring '_a' not being zero, but the\\r\\n        // benefit is lost if '_b' is also tested.\\r\\n        // See: https://github.com/OpenZeppelin/openzeppelin-solidity/pull/522\\r\\n        if (_a == 0) return 0;\\r\\n\\r\\n        uint256 c = _a * _b;\\r\\n        return c / _a == _b ? c : UINT_MAX;\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0xf1e8364a21fdfb7ea607de6ec959c34d3dc02543ad57cc81b1cb0d5885f7e553\",\"license\":\"MIT\"},\"src/dependencies/lib/ExitPayloadReader.sol\":{\"content\":\"pragma solidity ^0.8.0;\\r\\n\\r\\nimport {RLPReader} from \\\"./RLPReader.sol\\\";\\r\\n\\r\\nlibrary ExitPayloadReader {\\r\\n    using RLPReader for bytes;\\r\\n    using RLPReader for RLPReader.RLPItem;\\r\\n\\r\\n    uint8 constant WORD_SIZE = 32;\\r\\n\\r\\n    struct ExitPayload {\\r\\n        RLPReader.RLPItem[] data;\\r\\n    }\\r\\n\\r\\n    struct Receipt {\\r\\n        RLPReader.RLPItem[] data;\\r\\n        bytes raw;\\r\\n        uint256 logIndex;\\r\\n    }\\r\\n\\r\\n    struct Log {\\r\\n        RLPReader.RLPItem data;\\r\\n        RLPReader.RLPItem[] list;\\r\\n    }\\r\\n\\r\\n    struct LogTopics {\\r\\n        RLPReader.RLPItem[] data;\\r\\n    }\\r\\n\\r\\n    // copy paste of private copy() from RLPReader to avoid changing of existing contracts\\r\\n    function copy(\\r\\n        uint256 src,\\r\\n        uint256 dest,\\r\\n        uint256 len\\r\\n    ) private pure {\\r\\n        if (len == 0) return;\\r\\n\\r\\n        // copy as many word sizes as possible\\r\\n        for (; len >= WORD_SIZE; len -= WORD_SIZE) {\\r\\n            assembly {\\r\\n                mstore(dest, mload(src))\\r\\n            }\\r\\n\\r\\n            src += WORD_SIZE;\\r\\n            dest += WORD_SIZE;\\r\\n        }\\r\\n\\r\\n        // left over bytes. Mask is used to remove unwanted bytes from the word\\r\\n        uint256 mask = 256**(WORD_SIZE - len) - 1;\\r\\n        assembly {\\r\\n            let srcpart := and(mload(src), not(mask)) // zero out src\\r\\n            let destpart := and(mload(dest), mask) // retrieve the bytes\\r\\n            mstore(dest, or(destpart, srcpart))\\r\\n        }\\r\\n    }\\r\\n\\r\\n    function toExitPayload(bytes memory data) internal pure returns (ExitPayload memory) {\\r\\n        RLPReader.RLPItem[] memory payloadData = data.toRlpItem().toList();\\r\\n\\r\\n        return ExitPayload(payloadData);\\r\\n    }\\r\\n\\r\\n    function getHeaderNumber(ExitPayload memory payload) internal pure returns (uint256) {\\r\\n        return payload.data[0].toUint();\\r\\n    }\\r\\n\\r\\n    function getBlockProof(ExitPayload memory payload) internal pure returns (bytes memory) {\\r\\n        return payload.data[1].toBytes();\\r\\n    }\\r\\n\\r\\n    function getBlockNumber(ExitPayload memory payload) internal pure returns (uint256) {\\r\\n        return payload.data[2].toUint();\\r\\n    }\\r\\n\\r\\n    function getBlockTime(ExitPayload memory payload) internal pure returns (uint256) {\\r\\n        return payload.data[3].toUint();\\r\\n    }\\r\\n\\r\\n    function getTxRoot(ExitPayload memory payload) internal pure returns (bytes32) {\\r\\n        return bytes32(payload.data[4].toUint());\\r\\n    }\\r\\n\\r\\n    function getReceiptRoot(ExitPayload memory payload) internal pure returns (bytes32) {\\r\\n        return bytes32(payload.data[5].toUint());\\r\\n    }\\r\\n\\r\\n    function getReceipt(ExitPayload memory payload) internal pure returns (Receipt memory receipt) {\\r\\n        receipt.raw = payload.data[6].toBytes();\\r\\n        RLPReader.RLPItem memory receiptItem = receipt.raw.toRlpItem();\\r\\n\\r\\n        if (receiptItem.isList()) {\\r\\n            // legacy tx\\r\\n            receipt.data = receiptItem.toList();\\r\\n        } else {\\r\\n            // pop first byte before parsting receipt\\r\\n            bytes memory typedBytes = receipt.raw;\\r\\n            bytes memory result = new bytes(typedBytes.length - 1);\\r\\n            uint256 srcPtr;\\r\\n            uint256 destPtr;\\r\\n            assembly {\\r\\n                srcPtr := add(33, typedBytes)\\r\\n                destPtr := add(0x20, result)\\r\\n            }\\r\\n\\r\\n            copy(srcPtr, destPtr, result.length);\\r\\n            receipt.data = result.toRlpItem().toList();\\r\\n        }\\r\\n\\r\\n        receipt.logIndex = getReceiptLogIndex(payload);\\r\\n        return receipt;\\r\\n    }\\r\\n\\r\\n    function getReceiptProof(ExitPayload memory payload) internal pure returns (bytes memory) {\\r\\n        return payload.data[7].toBytes();\\r\\n    }\\r\\n\\r\\n    function getBranchMaskAsBytes(ExitPayload memory payload) internal pure returns (bytes memory) {\\r\\n        return payload.data[8].toBytes();\\r\\n    }\\r\\n\\r\\n    function getBranchMaskAsUint(ExitPayload memory payload) internal pure returns (uint256) {\\r\\n        return payload.data[8].toUint();\\r\\n    }\\r\\n\\r\\n    function getReceiptLogIndex(ExitPayload memory payload) internal pure returns (uint256) {\\r\\n        return payload.data[9].toUint();\\r\\n    }\\r\\n\\r\\n    // Receipt methods\\r\\n    function toBytes(Receipt memory receipt) internal pure returns (bytes memory) {\\r\\n        return receipt.raw;\\r\\n    }\\r\\n\\r\\n    function getLog(Receipt memory receipt) internal pure returns (Log memory) {\\r\\n        RLPReader.RLPItem memory logData = receipt.data[3].toList()[receipt.logIndex];\\r\\n        return Log(logData, logData.toList());\\r\\n    }\\r\\n\\r\\n    // Log methods\\r\\n    function getEmitter(Log memory log) internal pure returns (address) {\\r\\n        return RLPReader.toAddress(log.list[0]);\\r\\n    }\\r\\n\\r\\n    function getTopics(Log memory log) internal pure returns (LogTopics memory) {\\r\\n        return LogTopics(log.list[1].toList());\\r\\n    }\\r\\n\\r\\n    function getData(Log memory log) internal pure returns (bytes memory) {\\r\\n        return log.list[2].toBytes();\\r\\n    }\\r\\n\\r\\n    function toRlpBytes(Log memory log) internal pure returns (bytes memory) {\\r\\n        return log.data.toRlpBytes();\\r\\n    }\\r\\n\\r\\n    // LogTopics methods\\r\\n    function getField(LogTopics memory topics, uint256 index) internal pure returns (RLPReader.RLPItem memory) {\\r\\n        return topics.data[index];\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0xd0ac681de696e1607351bc12fae295f170017bd7b1634abbfaa3157a6dc0f162\"},\"src/dependencies/lib/Merkle.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\nlibrary Merkle {\\r\\n    function checkMembership(\\r\\n        bytes32 leaf,\\r\\n        uint256 index,\\r\\n        bytes32 rootHash,\\r\\n        bytes memory proof\\r\\n    ) internal pure returns (bool) {\\r\\n        require(proof.length % 32 == 0, \\\"Invalid proof length\\\");\\r\\n        uint256 proofHeight = proof.length / 32;\\r\\n        // Proof of size n means, height of the tree is n+1.\\r\\n        // In a tree of height n+1, max #leafs possible is 2 ^ n\\r\\n        require(index < 2**proofHeight, \\\"Leaf index is too big\\\");\\r\\n\\r\\n        bytes32 proofElement;\\r\\n        bytes32 computedHash = leaf;\\r\\n        for (uint256 i = 32; i <= proof.length; i += 32) {\\r\\n            assembly {\\r\\n                proofElement := mload(add(proof, i))\\r\\n            }\\r\\n\\r\\n            if (index % 2 == 0) {\\r\\n                computedHash = keccak256(abi.encodePacked(computedHash, proofElement));\\r\\n            } else {\\r\\n                computedHash = keccak256(abi.encodePacked(proofElement, computedHash));\\r\\n            }\\r\\n\\r\\n            index = index / 2;\\r\\n        }\\r\\n        return computedHash == rootHash;\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x5d79fb8afb2d2d16b92dc644e9f68c2e2b0a684d3c4b0b20a6d8a2f2b6287c79\",\"license\":\"MIT\"},\"src/dependencies/lib/MerklePatriciaProof.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\nimport {RLPReader} from \\\"./RLPReader.sol\\\";\\r\\n\\r\\nlibrary MerklePatriciaProof {\\r\\n    /*\\r\\n     * @dev Verifies a merkle patricia proof.\\r\\n     * @param value The terminating value in the trie.\\r\\n     * @param encodedPath The path in the trie leading to value.\\r\\n     * @param rlpParentNodes The rlp encoded stack of nodes.\\r\\n     * @param root The root hash of the trie.\\r\\n     * @return The boolean validity of the proof.\\r\\n     */\\r\\n    function verify(\\r\\n        bytes memory value,\\r\\n        bytes memory encodedPath,\\r\\n        bytes memory rlpParentNodes,\\r\\n        bytes32 root\\r\\n    ) internal pure returns (bool) {\\r\\n        RLPReader.RLPItem memory item = RLPReader.toRlpItem(rlpParentNodes);\\r\\n        RLPReader.RLPItem[] memory parentNodes = RLPReader.toList(item);\\r\\n\\r\\n        bytes memory currentNode;\\r\\n        RLPReader.RLPItem[] memory currentNodeList;\\r\\n\\r\\n        bytes32 nodeKey = root;\\r\\n        uint256 pathPtr = 0;\\r\\n\\r\\n        bytes memory path = _getNibbleArray(encodedPath);\\r\\n        if (path.length == 0) {\\r\\n            return false;\\r\\n        }\\r\\n\\r\\n        for (uint256 i = 0; i < parentNodes.length; i++) {\\r\\n            if (pathPtr > path.length) {\\r\\n                return false;\\r\\n            }\\r\\n\\r\\n            currentNode = RLPReader.toRlpBytes(parentNodes[i]);\\r\\n            if (nodeKey != keccak256(currentNode)) {\\r\\n                return false;\\r\\n            }\\r\\n            currentNodeList = RLPReader.toList(parentNodes[i]);\\r\\n\\r\\n            if (currentNodeList.length == 17) {\\r\\n                if (pathPtr == path.length) {\\r\\n                    if (keccak256(RLPReader.toBytes(currentNodeList[16])) == keccak256(value)) {\\r\\n                        return true;\\r\\n                    } else {\\r\\n                        return false;\\r\\n                    }\\r\\n                }\\r\\n\\r\\n                uint8 nextPathNibble = uint8(path[pathPtr]);\\r\\n                if (nextPathNibble > 16) {\\r\\n                    return false;\\r\\n                }\\r\\n                nodeKey = bytes32(RLPReader.toUintStrict(currentNodeList[nextPathNibble]));\\r\\n                pathPtr += 1;\\r\\n            } else if (currentNodeList.length == 2) {\\r\\n                uint256 traversed = _nibblesToTraverse(RLPReader.toBytes(currentNodeList[0]), path, pathPtr);\\r\\n                if (pathPtr + traversed == path.length) {\\r\\n                    //leaf node\\r\\n                    if (keccak256(RLPReader.toBytes(currentNodeList[1])) == keccak256(value)) {\\r\\n                        return true;\\r\\n                    } else {\\r\\n                        return false;\\r\\n                    }\\r\\n                }\\r\\n\\r\\n                //extension node\\r\\n                if (traversed == 0) {\\r\\n                    return false;\\r\\n                }\\r\\n\\r\\n                pathPtr += traversed;\\r\\n                nodeKey = bytes32(RLPReader.toUintStrict(currentNodeList[1]));\\r\\n            } else {\\r\\n                return false;\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n\\r\\n    function _nibblesToTraverse(\\r\\n        bytes memory encodedPartialPath,\\r\\n        bytes memory path,\\r\\n        uint256 pathPtr\\r\\n    ) private pure returns (uint256) {\\r\\n        uint256 len = 0;\\r\\n        // encodedPartialPath has elements that are each two hex characters (1 byte), but partialPath\\r\\n        // and slicedPath have elements that are each one hex character (1 nibble)\\r\\n        bytes memory partialPath = _getNibbleArray(encodedPartialPath);\\r\\n        bytes memory slicedPath = new bytes(partialPath.length);\\r\\n\\r\\n        // pathPtr counts nibbles in path\\r\\n        // partialPath.length is a number of nibbles\\r\\n        for (uint256 i = pathPtr; i < pathPtr + partialPath.length; i++) {\\r\\n            bytes1 pathNibble = path[i];\\r\\n            slicedPath[i - pathPtr] = pathNibble;\\r\\n        }\\r\\n\\r\\n        if (keccak256(partialPath) == keccak256(slicedPath)) {\\r\\n            len = partialPath.length;\\r\\n        } else {\\r\\n            len = 0;\\r\\n        }\\r\\n        return len;\\r\\n    }\\r\\n\\r\\n    // bytes b must be hp encoded\\r\\n    function _getNibbleArray(bytes memory b) internal pure returns (bytes memory) {\\r\\n        bytes memory nibbles = \\\"\\\";\\r\\n        if (b.length > 0) {\\r\\n            uint8 offset;\\r\\n            uint8 hpNibble = uint8(_getNthNibbleOfBytes(0, b));\\r\\n            if (hpNibble == 1 || hpNibble == 3) {\\r\\n                nibbles = new bytes(b.length * 2 - 1);\\r\\n                bytes1 oddNibble = _getNthNibbleOfBytes(1, b);\\r\\n                nibbles[0] = oddNibble;\\r\\n                offset = 1;\\r\\n            } else {\\r\\n                nibbles = new bytes(b.length * 2 - 2);\\r\\n                offset = 0;\\r\\n            }\\r\\n\\r\\n            for (uint256 i = offset; i < nibbles.length; i++) {\\r\\n                nibbles[i] = _getNthNibbleOfBytes(i - offset + 2, b);\\r\\n            }\\r\\n        }\\r\\n        return nibbles;\\r\\n    }\\r\\n\\r\\n    function _getNthNibbleOfBytes(uint256 n, bytes memory str) private pure returns (bytes1) {\\r\\n        return bytes1(n % 2 == 0 ? uint8(str[n / 2]) / 0x10 : uint8(str[n / 2]) % 0x10);\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x0283b63eeb275b8447d1b1442a7fc529a43347b767f8d4f4a57d777b7b583965\",\"license\":\"MIT\"},\"src/dependencies/lib/RLPReader.sol\":{\"content\":\"/*\\r\\n * <AUTHOR> * Please reach out with any questions or concerns\\r\\n */\\r\\npragma solidity ^0.8.0;\\r\\n\\r\\nlibrary RLPReader {\\r\\n    uint8 constant STRING_SHORT_START = 0x80;\\r\\n    uint8 constant STRING_LONG_START = 0xb8;\\r\\n    uint8 constant LIST_SHORT_START = 0xc0;\\r\\n    uint8 constant LIST_LONG_START = 0xf8;\\r\\n    uint8 constant WORD_SIZE = 32;\\r\\n\\r\\n    struct RLPItem {\\r\\n        uint256 len;\\r\\n        uint256 memPtr;\\r\\n    }\\r\\n\\r\\n    struct Iterator {\\r\\n        RLPItem item; // Item that's being iterated over.\\r\\n        uint256 nextPtr; // Position of the next item in the list.\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @dev Returns the next element in the iteration. Reverts if it has not next element.\\r\\n     * @param self The iterator.\\r\\n     * @return The next element in the iteration.\\r\\n     */\\r\\n    function next(Iterator memory self) internal pure returns (RLPItem memory) {\\r\\n        require(hasNext(self));\\r\\n\\r\\n        uint256 ptr = self.nextPtr;\\r\\n        uint256 itemLength = _itemLength(ptr);\\r\\n        self.nextPtr = ptr + itemLength;\\r\\n\\r\\n        return RLPItem(itemLength, ptr);\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @dev Returns true if the iteration has more elements.\\r\\n     * @param self The iterator.\\r\\n     * @return true if the iteration has more elements.\\r\\n     */\\r\\n    function hasNext(Iterator memory self) internal pure returns (bool) {\\r\\n        RLPItem memory item = self.item;\\r\\n        return self.nextPtr < item.memPtr + item.len;\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @param item RLP encoded bytes\\r\\n     */\\r\\n    function toRlpItem(bytes memory item) internal pure returns (RLPItem memory) {\\r\\n        uint256 memPtr;\\r\\n        assembly {\\r\\n            memPtr := add(item, 0x20)\\r\\n        }\\r\\n\\r\\n        return RLPItem(item.length, memPtr);\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @dev Create an iterator. Reverts if item is not a list.\\r\\n     * @param self The RLP item.\\r\\n     * @return An 'Iterator' over the item.\\r\\n     */\\r\\n    function iterator(RLPItem memory self) internal pure returns (Iterator memory) {\\r\\n        require(isList(self));\\r\\n\\r\\n        uint256 ptr = self.memPtr + _payloadOffset(self.memPtr);\\r\\n        return Iterator(self, ptr);\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @param item RLP encoded bytes\\r\\n     */\\r\\n    function rlpLen(RLPItem memory item) internal pure returns (uint256) {\\r\\n        return item.len;\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @param item RLP encoded bytes\\r\\n     */\\r\\n    function payloadLen(RLPItem memory item) internal pure returns (uint256) {\\r\\n        return item.len - _payloadOffset(item.memPtr);\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @param item RLP encoded list in bytes\\r\\n     */\\r\\n    function toList(RLPItem memory item) internal pure returns (RLPItem[] memory) {\\r\\n        require(isList(item));\\r\\n\\r\\n        uint256 items = numItems(item);\\r\\n        RLPItem[] memory result = new RLPItem[](items);\\r\\n\\r\\n        uint256 memPtr = item.memPtr + _payloadOffset(item.memPtr);\\r\\n        uint256 dataLen;\\r\\n        for (uint256 i = 0; i < items; i++) {\\r\\n            dataLen = _itemLength(memPtr);\\r\\n            result[i] = RLPItem(dataLen, memPtr);\\r\\n            memPtr = memPtr + dataLen;\\r\\n        }\\r\\n\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    // @return indicator whether encoded payload is a list. negate this function call for isData.\\r\\n    function isList(RLPItem memory item) internal pure returns (bool) {\\r\\n        if (item.len == 0) return false;\\r\\n\\r\\n        uint8 byte0;\\r\\n        uint256 memPtr = item.memPtr;\\r\\n        assembly {\\r\\n            byte0 := byte(0, mload(memPtr))\\r\\n        }\\r\\n\\r\\n        if (byte0 < LIST_SHORT_START) return false;\\r\\n        return true;\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @dev A cheaper version of keccak256(toRlpBytes(item)) that avoids copying memory.\\r\\n     * @return keccak256 hash of RLP encoded bytes.\\r\\n     */\\r\\n    function rlpBytesKeccak256(RLPItem memory item) internal pure returns (bytes32) {\\r\\n        uint256 ptr = item.memPtr;\\r\\n        uint256 len = item.len;\\r\\n        bytes32 result;\\r\\n        assembly {\\r\\n            result := keccak256(ptr, len)\\r\\n        }\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    function payloadLocation(RLPItem memory item) internal pure returns (uint256, uint256) {\\r\\n        uint256 offset = _payloadOffset(item.memPtr);\\r\\n        uint256 memPtr = item.memPtr + offset;\\r\\n        uint256 len = item.len - offset; // data length\\r\\n        return (memPtr, len);\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @dev A cheaper version of keccak256(toBytes(item)) that avoids copying memory.\\r\\n     * @return keccak256 hash of the item payload.\\r\\n     */\\r\\n    function payloadKeccak256(RLPItem memory item) internal pure returns (bytes32) {\\r\\n        (uint256 memPtr, uint256 len) = payloadLocation(item);\\r\\n        bytes32 result;\\r\\n        assembly {\\r\\n            result := keccak256(memPtr, len)\\r\\n        }\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    /** RLPItem conversions into data types **/\\r\\n\\r\\n    // @returns raw rlp encoding in bytes\\r\\n    function toRlpBytes(RLPItem memory item) internal pure returns (bytes memory) {\\r\\n        bytes memory result = new bytes(item.len);\\r\\n        if (result.length == 0) return result;\\r\\n\\r\\n        uint256 ptr;\\r\\n        assembly {\\r\\n            ptr := add(0x20, result)\\r\\n        }\\r\\n\\r\\n        copy(item.memPtr, ptr, item.len);\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    // any non-zero byte is considered true\\r\\n    function toBoolean(RLPItem memory item) internal pure returns (bool) {\\r\\n        require(item.len == 1);\\r\\n        uint256 result;\\r\\n        uint256 memPtr = item.memPtr;\\r\\n        assembly {\\r\\n            result := byte(0, mload(memPtr))\\r\\n        }\\r\\n\\r\\n        return result == 0 ? false : true;\\r\\n    }\\r\\n\\r\\n    function toAddress(RLPItem memory item) internal pure returns (address) {\\r\\n        // 1 byte for the length prefix\\r\\n        require(item.len == 21);\\r\\n\\r\\n        return address(uint160(toUint(item)));\\r\\n    }\\r\\n\\r\\n    function toUint(RLPItem memory item) internal pure returns (uint256) {\\r\\n        require(item.len > 0 && item.len <= 33);\\r\\n\\r\\n        uint256 offset = _payloadOffset(item.memPtr);\\r\\n        uint256 len = item.len - offset;\\r\\n\\r\\n        uint256 result;\\r\\n        uint256 memPtr = item.memPtr + offset;\\r\\n        assembly {\\r\\n            result := mload(memPtr)\\r\\n\\r\\n            // shfit to the correct location if neccesary\\r\\n            if lt(len, 32) {\\r\\n                result := div(result, exp(256, sub(32, len)))\\r\\n            }\\r\\n        }\\r\\n\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    // enforces 32 byte length\\r\\n    function toUintStrict(RLPItem memory item) internal pure returns (uint256) {\\r\\n        // one byte prefix\\r\\n        require(item.len == 33);\\r\\n\\r\\n        uint256 result;\\r\\n        uint256 memPtr = item.memPtr + 1;\\r\\n        assembly {\\r\\n            result := mload(memPtr)\\r\\n        }\\r\\n\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    function toBytes(RLPItem memory item) internal pure returns (bytes memory) {\\r\\n        require(item.len > 0);\\r\\n\\r\\n        uint256 offset = _payloadOffset(item.memPtr);\\r\\n        uint256 len = item.len - offset; // data length\\r\\n        bytes memory result = new bytes(len);\\r\\n\\r\\n        uint256 destPtr;\\r\\n        assembly {\\r\\n            destPtr := add(0x20, result)\\r\\n        }\\r\\n\\r\\n        copy(item.memPtr + offset, destPtr, len);\\r\\n        return result;\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * Private Helpers\\r\\n     */\\r\\n\\r\\n    // @return number of payload items inside an encoded list.\\r\\n    function numItems(RLPItem memory item) private pure returns (uint256) {\\r\\n        if (item.len == 0) return 0;\\r\\n\\r\\n        uint256 count = 0;\\r\\n        uint256 currPtr = item.memPtr + _payloadOffset(item.memPtr);\\r\\n        uint256 endPtr = item.memPtr + item.len;\\r\\n        while (currPtr < endPtr) {\\r\\n            currPtr = currPtr + _itemLength(currPtr); // skip over an item\\r\\n            count++;\\r\\n        }\\r\\n\\r\\n        return count;\\r\\n    }\\r\\n\\r\\n    // @return entire rlp item byte length\\r\\n    function _itemLength(uint256 memPtr) private pure returns (uint256) {\\r\\n        uint256 itemLen;\\r\\n        uint256 byte0;\\r\\n        assembly {\\r\\n            byte0 := byte(0, mload(memPtr))\\r\\n        }\\r\\n\\r\\n        if (byte0 < STRING_SHORT_START) itemLen = 1;\\r\\n        else if (byte0 < STRING_LONG_START) itemLen = byte0 - STRING_SHORT_START + 1;\\r\\n        else if (byte0 < LIST_SHORT_START) {\\r\\n            assembly {\\r\\n                let byteLen := sub(byte0, 0xb7) // # of bytes the actual length is\\r\\n                memPtr := add(memPtr, 1) // skip over the first byte\\r\\n                /* 32 byte word size */\\r\\n                let dataLen := div(mload(memPtr), exp(256, sub(32, byteLen))) // right shifting to get the len\\r\\n                itemLen := add(dataLen, add(byteLen, 1))\\r\\n            }\\r\\n        } else if (byte0 < LIST_LONG_START) {\\r\\n            itemLen = byte0 - LIST_SHORT_START + 1;\\r\\n        } else {\\r\\n            assembly {\\r\\n                let byteLen := sub(byte0, 0xf7)\\r\\n                memPtr := add(memPtr, 1)\\r\\n\\r\\n                let dataLen := div(mload(memPtr), exp(256, sub(32, byteLen))) // right shifting to the correct length\\r\\n                itemLen := add(dataLen, add(byteLen, 1))\\r\\n            }\\r\\n        }\\r\\n\\r\\n        return itemLen;\\r\\n    }\\r\\n\\r\\n    // @return number of bytes until the data\\r\\n    function _payloadOffset(uint256 memPtr) private pure returns (uint256) {\\r\\n        uint256 byte0;\\r\\n        assembly {\\r\\n            byte0 := byte(0, mload(memPtr))\\r\\n        }\\r\\n\\r\\n        if (byte0 < STRING_SHORT_START) return 0;\\r\\n        else if (byte0 < STRING_LONG_START || (byte0 >= LIST_SHORT_START && byte0 < LIST_LONG_START)) return 1;\\r\\n        else if (byte0 < LIST_SHORT_START)\\r\\n            // being explicit\\r\\n            return byte0 - (STRING_LONG_START - 1) + 1;\\r\\n        else return byte0 - (LIST_LONG_START - 1) + 1;\\r\\n    }\\r\\n\\r\\n    /*\\r\\n     * @param src Pointer to source\\r\\n     * @param dest Pointer to destination\\r\\n     * @param len Amount of memory to copy from the source\\r\\n     */\\r\\n    function copy(\\r\\n        uint256 src,\\r\\n        uint256 dest,\\r\\n        uint256 len\\r\\n    ) private pure {\\r\\n        if (len == 0) return;\\r\\n\\r\\n        // copy as many word sizes as possible\\r\\n        for (; len >= WORD_SIZE; len -= WORD_SIZE) {\\r\\n            assembly {\\r\\n                mstore(dest, mload(src))\\r\\n            }\\r\\n\\r\\n            src += WORD_SIZE;\\r\\n            dest += WORD_SIZE;\\r\\n        }\\r\\n\\r\\n        if (len == 0) return;\\r\\n\\r\\n        // left over bytes. Mask is used to remove unwanted bytes from the word\\r\\n        uint256 mask = 256**(WORD_SIZE - len) - 1;\\r\\n\\r\\n        assembly {\\r\\n            let srcpart := and(mload(src), not(mask)) // zero out src\\r\\n            let destpart := and(mload(dest), mask) // retrieve the bytes\\r\\n            mstore(dest, or(destpart, srcpart))\\r\\n        }\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0xd3505afc29397bd4ca396f732515c1a498bc2561a99b678bfd53c4bd978a5eb8\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.", "kind": "dev", "methods": {"constructor": {"params": {"_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_checkpointManager": "For Polygon FX-portal bridge.", "_fxRoot": "Address of the FxRoot contract of the Polygon bridge.", "_loserAppealPeriodMultiplier": "Multiplier for calculating the appeal period for the losing answer.", "_loserMultiplier": "Multiplier for calculation the appeal cost of the losing answer.", "_metaEvidence": "The URI of the meta evidence file.", "_winnerMultiplier": "Multiplier for calculating the appeal cost of the winning answer."}}, "externalIDtoLocalID(uint256)": {"params": {"_externalDisputeID": "Dispute id as in arbitrator side."}, "returns": {"_0": "localDisputeID Dispute id as in arbitrable contract."}}, "fundAppeal(uint256,uint256)": {"params": {"_answer": "One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.", "_arbitrationID": "The ID of the arbitration, which is questionID cast into uint256."}, "returns": {"_0": "Whether the answer was fully funded or not."}}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_contributor": "The address whose contributions to query.", "_round": "The round to query."}, "returns": {"contributions": "The amount contributed to each funded answer by the contributor.", "fundedAnswers": "IDs of the answers that are fully funded."}}, "getDisputeFee(bytes32)": {"returns": {"_0": "The fee to create a dispute."}}, "getFundingStatus(uint256,uint256,uint256)": {"params": {"_answer": "The answer choice to get funding status for.", "_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"fullyFunded": "Whether the answer is fully funded or not.", "raised": "The amount paid for this answer."}}, "getMultipliers()": {"returns": {"divisor": "Multiplier divisor.", "loser": "Losers stake multiplier.", "loserAppealPeriod": "Multiplier for calculating an appeal period duration for the losing side.", "winner": "Winners stake multiplier."}}, "getNumberOfRounds(uint256)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question."}, "returns": {"_0": "The number of rounds."}}, "getRoundInfo(uint256,uint256)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"feeRewards": "The amount of fees that will be used as rewards.", "fundedAnswers": "IDs of fully funded answers.", "paidFees": "The amount of fees paid for each fully funded answer."}}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The contributor for which to query.", "_contributedTo": "Answer that received contributions from contributor."}, "returns": {"sum": "The total amount available to withdraw."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "numberOfRulingOptions(uint256)": {"returns": {"_0": "count The number of ruling options."}}, "questionIDToArbitrationID(bytes32)": {"params": {"_questionID": "The ID of the question."}, "returns": {"_0": "The ID of the arbitration."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveMessage(bytes)": {"details": "This function verifies if the transaction actually happened on child chain", "params": {"inputData": "RLP encoded data of the reference tx containing following list of fields  0 - headerNumber - Checkpoint header block number containing the reference tx  1 - blockProof - Proof that the block header (in the child chain) is a leaf in the submitted merkle root  2 - blockNumber - Block number containing the reference tx on child chain  3 - blockTime - Reference tx block time  4 - txRoot - Transactions root of block  5 - receiptRoot - Receipts root of block  6 - receipt - Receipt of the reference transaction  7 - receiptProof - Merkle proof of the reference receipt  8 - branchMask - 32 bits denoting the path of receipt in merkle tree  9 - receiptLogIndex - Log Index to read from the receipt"}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}, "submitEvidence(uint256,string)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question.", "_evidenceURI": "Link to evidence."}}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"params": {"_answer": "The answer to query the reward from.", "_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address to send reward to.", "_round": "The round from which to withdraw."}, "returns": {"reward": "The withdrawn amount."}}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address that made contributions.", "_contributedTo": "Answer that received contributions from contributor."}}}, "title": "Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the foreign chain."}, "externalIDtoLocalID(uint256)": {"notice": "Maps external (arbitrator side) dispute id to local (arbitrable) dispute id."}, "fundAppeal(uint256,uint256)": {"notice": "Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded."}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"notice": "Gets contributions to the answers that are fully funded."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "getFundingStatus(uint256,uint256,uint256)": {"notice": "Gets the information of a round of a question for a specific answer choice."}, "getMultipliers()": {"notice": "Returns stake multipliers."}, "getNumberOfRounds(uint256)": {"notice": "Gets the number of rounds of the specific question."}, "getRoundInfo(uint256,uint256)": {"notice": "Gets the information of a round of a question."}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"notice": "Returns the sum of withdrawable amount."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created."}, "numberOfRulingOptions(uint256)": {"notice": "Returns number of possible ruling options. Valid rulings are [0, return value]."}, "questionIDToArbitrationID(bytes32)": {"notice": "Casts question ID into uint256 thus returning the related arbitration ID."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "receiveMessage(bytes)": {"notice": "receive message from  L2 to L1, validated by proof"}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute. Can only be called by the arbitrator."}, "submitEvidence(uint256,string)": {"notice": "Allows to submit evidence for a particular question."}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"notice": "Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner."}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"notice": "Allows to withdraw any rewards or reimbursable fees for all rounds at once."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 2959, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "fxRoot", "offset": 0, "slot": "0", "type": "t_contract(IFxStateSender)2909"}, {"astId": 2962, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "checkpointManager", "offset": 0, "slot": "1", "type": "t_contract(ICheckpointManager)2927"}, {"astId": 2964, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "fxChildTunnel", "offset": 0, "slot": "2", "type": "t_address"}, {"astId": 2968, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "processedExits", "offset": 0, "slot": "3", "type": "t_mapping(t_bytes32,t_bool)"}, {"astId": 562, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitratorExtraData", "offset": 0, "slot": "4", "type": "t_bytes_storage"}, {"astId": 575, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationRequests", "offset": 0, "slot": "5", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)532_storage))"}, {"astId": 580, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "disputeIDToDisputeDetails", "offset": 0, "slot": "6", "type": "t_mapping(t_uint256,t_struct(DisputeDetails)537_storage)"}, {"astId": 584, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationIDToDisputeExists", "offset": 0, "slot": "7", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 588, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationIDToRequester", "offset": 0, "slot": "8", "type": "t_mapping(t_uint256,t_address)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_struct(Round)557_storage)dyn_storage": {"base": "t_struct(Round)557_storage", "encoding": "dynamic_array", "label": "struct RealitioForeignArbitrationProxyWithAppeals.Round[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_contract(ICheckpointManager)2927": {"encoding": "inplace", "label": "contract ICheckpointManager", "numberOfBytes": "20"}, "t_contract(IFxStateSender)2909": {"encoding": "inplace", "label": "contract IFxStateSender", "numberOfBytes": "20"}, "t_enum(Status)518": {"encoding": "inplace", "label": "enum RealitioForeignArbitrationProxyWithAppeals.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_uint256)"}, "t_mapping(t_address,t_struct(ArbitrationRequest)532_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignArbitrationProxyWithAppeals.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)532_storage"}, "t_mapping(t_bytes32,t_bool)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_uint256,t_bool)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)532_storage))": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => mapping(address => struct RealitioForeignArbitrationProxyWithAppeals.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)532_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)537_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignArbitrationProxyWithAppeals.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)537_storage"}, "t_mapping(t_uint256,t_uint256)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(ArbitrationRequest)532_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxyWithAppeals.ArbitrationRequest", "members": [{"astId": 521, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)518"}, {"astId": 523, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}, {"astId": 525, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "disputeID", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 527, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "answer", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 531, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "rounds", "offset": 0, "slot": "3", "type": "t_array(t_struct(Round)557_storage)dyn_storage"}], "numberOfBytes": "128"}, "t_struct(DisputeDetails)537_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxyWithAppeals.DisputeDetails", "members": [{"astId": 534, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "arbitrationID", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 536, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_struct(Round)557_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxyWithAppeals.Round", "members": [{"astId": 541, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "paidFees", "offset": 0, "slot": "0", "type": "t_mapping(t_uint256,t_uint256)"}, {"astId": 545, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "hasPaid", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 551, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "contributions", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))"}, {"astId": 553, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "feeRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 556, "contract": "src/RealitioForeignArbitrationProxyWithAppeals.sol:RealitioForeignArbitrationProxyWithAppeals", "label": "fundedAnswers", "offset": 0, "slot": "4", "type": "t_array(t_uint256)dyn_storage"}], "numberOfBytes": "160"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}
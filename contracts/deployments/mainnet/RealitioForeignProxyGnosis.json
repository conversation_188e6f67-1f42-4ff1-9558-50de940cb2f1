{"address": "0x79d0464Ec27F67663DADf761432fC8DD0AeA3D49", "abi": [{"inputs": [{"internalType": "contract IAMB", "name": "_amb", "type": "address"}, {"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "bytes32", "name": "_homeChainId", "type": "bytes32"}, {"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "string", "name": "_termsOfService", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"inputs": [], "name": "META_EVIDENCE_ID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "amb", "outputs": [{"internalType": "contract IAMB", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignArbitrationProxy.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "disputeIDToDisputeDetails", "outputs": [{"internalType": "bytes32", "name": "questionID", "type": "bytes32"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "homeChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "questionIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "termsOfService", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0xc54791433ec4a80075d3a0f9abb63184126858f56def29e8030b9ef8c1247d82", "receipt": {"to": null, "from": "0xd52345cc4845f90933849f4bd6f05Db47271dd2b", "contractAddress": "0x79d0464Ec27F67663DADf761432fC8DD0AeA3D49", "transactionIndex": 103, "gasUsed": "1787957", "logsBloom": "0x00000000000000000000000001000000000000000000000000000004000000000000000000000000000008000000000000000000000000000000000002002000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xb87867727990a3b9d63fb62416ea36149aa0e6b485ccd2692e7d6adfb4cf0e09", "transactionHash": "0xc54791433ec4a80075d3a0f9abb63184126858f56def29e8030b9ef8c1247d82", "logs": [{"transactionIndex": 103, "blockNumber": 11771713, "transactionHash": "0xc54791433ec4a80075d3a0f9abb63184126858f56def29e8030b9ef8c1247d82", "address": "0x79d0464Ec27F67663DADf761432fC8DD0AeA3D49", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000422f697066732f516d63366257547a504d4665527839565748776e4470445866696d774e73766e45674a6f3367796d6733377252642f7265616c6974696f2e6a736f6e000000000000000000000000000000000000000000000000000000000000", "logIndex": 292, "blockHash": "0xb87867727990a3b9d63fb62416ea36149aa0e6b485ccd2692e7d6adfb4cf0e09"}], "blockNumber": 11771713, "cumulativeGasUsed": "11374381", "status": 1, "byzantium": true}, "args": ["0x4C36d2919e407f0Cc2Ee3c993ccF8ac26d9CE64e", "0xe40DD83a262da3f56976038F1554Fe541Fa75ecd", "0x0000000000000000000000000000000000000000000000000000000000000064", "0x988b3a538b618c7a603e1c11ab82cd16dbe28069", "0x000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f4", "/ipfs/Qmc6bWTzPMFeRx9VWHwnDpDXfimwNsvnEgJo3gymg37rRd/realitio.json", "/ipfs/QmZM12kkguXFk2C94ykrKpambt4iUVKsVsxGxDEdLS68ws/omen-rules.pdf"], "solcInputHash": "3065d68a599ec4dedf3fe6c2af478726", "metadata": "{\"compiler\":{\"version\":\"0.7.6+commit.7338295f\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"_amb\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_homeProxy\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_homeChainId\",\"type\":\"bytes32\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_termsOfService\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"META_EVIDENCE_ID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"amb\",\"outputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignArbitrationProxy.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"disputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeChainId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"questionIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"termsOfService\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\",\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_amb\":\"ArbitraryMessageBridge contract address.\",\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_homeChainId\":\"The ID of the counter-party Home Chain.\",\"_homeProxy\":\"The address of the proxy contract in the counter-party Home Chain (i.e.: xDAI)\",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_termsOfService\":\"The path for the Terms of Service for Kleros as an arbitrator for Realitio.\"}},\"getDisputeFee(bytes32)\":{\"params\":{\"_questionID\":\"the ID of the question.\"},\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Note that 0 is reserved for \\\"Unable/refused to arbitrate\\\" and we map it to `bytes32(-1)` which has a similar meaning in Realitio.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}}},\"stateVariables\":{\"META_EVIDENCE_ID\":{\"details\":\"The ID of the MetaEvidence for disputes.\"},\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\":{\"details\":\"The number of choices for the arbitrator. Kleros is currently able to provide ruling values of up to 2^256 - 2.\"},\"amb\":{\"details\":\"ArbitraryMessageBridge contract address. TRUSTED.\"},\"arbitrationRequests\":{\"details\":\"Tracks arbitration requests for question ID. arbitrationRequests[questionID][requester]\"},\"arbitrator\":{\"details\":\"The address of the arbitrator. TRUSTED.\"},\"arbitratorExtraData\":{\"details\":\"The extra data used to raise a dispute in the arbitrator.\"},\"disputeIDToDisputeDetails\":{\"details\":\"Associates dispute ID to question ID and the requester. disputeIDToDisputeDetails[disputeID] -> {questionID, requester}\"},\"homeChainId\":{\"details\":\"The chain ID where the home proxy is deployed.\"},\"homeProxy\":{\"details\":\"Address of the counter-party proxy on the Home Chain. TRUSTED.\"},\"questionIDToDisputeExists\":{\"details\":\"Whether a dispute has already been created for the given question ID or not. questionIDToDisputeExists[questionID]\"},\"termsOfService\":{\"details\":\"The path for the Terms of Service for Kleros as an arbitrator for Realitio.\"}},\"title\":\"Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioForeignArbitrationProxy.sol\":\"RealitioForeignArbitrationProxy\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0x1803a3433a78c509b20bd9477a2c60a71b2ce1ee7e17eb0ef0601618a8a72526\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {Waiting, Appealable, Solved}\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0x240a4142f9ec379da0333dfc82409b7b058cff9ea118368eb5e8f15447996c1e\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence A link to the meta-evidence JSON.\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0x1ccedf5213730632540c748486637d7b1977ee73375818bf498a8276ca49dd13\",\"license\":\"MIT\"},\"src/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.7.2;\\n\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\n\\ninterface IHomeArbitrationProxy {\\n    /**\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\n     */\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice To be emitted when arbitration request is rejected.\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\n     * or if the question is already finalized.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\n     * @param _reason The reason why the request was rejected.\\n     */\\n    event RequestRejected(\\n        bytes32 indexed _questionID,\\n        address indexed _requester,\\n        uint256 _maxPrevious,\\n        string _reason\\n    );\\n\\n    /**\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the arbitration request is canceled.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\n\\n    /**\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\n     * @param _questionID The ID of the question.\\n     */\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\n\\n    /**\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function receiveArbitrationRequest(\\n        bytes32 _questionID,\\n        address _requester,\\n        uint256 _maxPrevious\\n    ) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been rejected.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\n     * Reasons why the request might be rejected:\\n     *  - The question does not exist\\n     *  - The question was not answered yet\\n     *  - The quesiton bond value changed while the arbitration was being requested\\n     *  - Another request was already accepted\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\n     * @dev Currently this can happen only if the arbitration cost increased.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the answer to a specified question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\n}\\n\\ninterface IForeignArbitrationProxy is IArbitrable, IEvidence {\\n    /**\\n     * @notice Should be emitted when the arbitration is requested.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice Should be emitted when the dispute is created.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _disputeID The ID of the dispute.\\n     */\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\n\\n    /**\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the dispute could not be created.\\n     * @dev This will happen if there is an increase in the arbitration fees\\n     * between the time the arbitration is made and the time it is acknowledged.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Requests arbitration for the given question.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @param _questionID the ID of the question.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\n}\\n\",\"keccak256\":\"0xac7e3511c4ba45ed083e3896c388258a742f1dc2b9b40c7edb908f15fe56df2e\",\"license\":\"MIT\"},\"src/RealitioForeignArbitrationProxy.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@hbarcelos]\\n *  @reviewers: [@ferittuncer*, @fnanni-0, @nix1g, @epiqueras*, @clesaege, @unknownunknown1]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.7.2;\\n\\nimport {IArbitrator} from \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\nimport {IAMB} from \\\"./dependencies/IAMB.sol\\\";\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./ArbitrationProxyInterfaces.sol\\\";\\n\\n/**\\n * @title Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\\n * @dev This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\\n */\\ncontract RealitioForeignArbitrationProxy is IForeignArbitrationProxy {\\n    /// @dev ArbitraryMessageBridge contract address. TRUSTED.\\n    IAMB public immutable amb;\\n\\n    /// @dev Address of the counter-party proxy on the Home Chain. TRUSTED.\\n    address public immutable homeProxy;\\n\\n    /// @dev The chain ID where the home proxy is deployed.\\n    bytes32 public immutable homeChainId;\\n\\n    /// @dev The address of the arbitrator. TRUSTED.\\n    IArbitrator public immutable arbitrator;\\n\\n    /// @dev The extra data used to raise a dispute in the arbitrator.\\n    bytes public arbitratorExtraData;\\n\\n    /// @dev The path for the Terms of Service for Kleros as an arbitrator for Realitio.\\n    string public termsOfService;\\n\\n    /// @dev The ID of the MetaEvidence for disputes.\\n    uint256 public constant META_EVIDENCE_ID = 0;\\n\\n    /// @dev The number of choices for the arbitrator. Kleros is currently able to provide ruling values of up to 2^256 - 2.\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max - 1;\\n\\n    enum Status {None, Requested, Created, Ruled, Failed}\\n\\n    struct ArbitrationRequest {\\n        Status status; // Status of the arbitration.\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\n    }\\n\\n    struct DisputeDetails {\\n        bytes32 questionID; // The question ID for the dispute.\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\n    }\\n\\n    /// @dev Tracks arbitration requests for question ID. arbitrationRequests[questionID][requester]\\n    mapping(bytes32 => mapping(address => ArbitrationRequest)) public arbitrationRequests;\\n\\n    /// @dev Associates dispute ID to question ID and the requester. disputeIDToDisputeDetails[disputeID] -> {questionID, requester}\\n    mapping(uint256 => DisputeDetails) public disputeIDToDisputeDetails;\\n\\n    /// @dev Whether a dispute has already been created for the given question ID or not. questionIDToDisputeExists[questionID]\\n    mapping(bytes32 => bool) public questionIDToDisputeExists;\\n\\n    modifier onlyArbitrator() {\\n        require(msg.sender == address(arbitrator), \\\"Only arbitrator allowed\\\");\\n        _;\\n    }\\n\\n    modifier onlyHomeProxy() {\\n        require(msg.sender == address(amb), \\\"Only AMB allowed\\\");\\n        require(amb.messageSourceChainId() == homeChainId, \\\"Only home chain allowed\\\");\\n        require(amb.messageSender() == homeProxy, \\\"Only home proxy allowed\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @notice Creates an arbitration proxy on the foreign chain.\\n     * @param _amb ArbitraryMessageBridge contract address.\\n     * @param _homeProxy The address of the proxy contract in the counter-party Home Chain (i.e.: xDAI)\\n     * @param _homeChainId The ID of the counter-party Home Chain.\\n     * @param _arbitrator Arbitrator contract address.\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\n     * @param _metaEvidence The URI of the meta evidence file.\\n     * @param _termsOfService The path for the Terms of Service for Kleros as an arbitrator for Realitio.\\n     */\\n    constructor(\\n        IAMB _amb,\\n        address _homeProxy,\\n        bytes32 _homeChainId,\\n        IArbitrator _arbitrator,\\n        bytes memory _arbitratorExtraData,\\n        string memory _metaEvidence,\\n        string memory _termsOfService\\n    ) {\\n        amb = _amb;\\n        homeProxy = _homeProxy;\\n        homeChainId = _homeChainId;\\n        arbitrator = _arbitrator;\\n        arbitratorExtraData = _arbitratorExtraData;\\n        termsOfService = _termsOfService;\\n\\n        emit MetaEvidence(META_EVIDENCE_ID, _metaEvidence);\\n    }\\n\\n    /**\\n     * @notice Requests arbitration for the given question and contested answer.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\n        require(!questionIDToDisputeExists[_questionID], \\\"Dispute already exists\\\");\\n\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_questionID][msg.sender];\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\n\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\n        require(msg.value >= arbitrationCost, \\\"Deposit value too low\\\");\\n\\n        arbitration.status = Status.Requested;\\n        arbitration.deposit = uint248(msg.value);\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy(0).receiveArbitrationRequest.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\n\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\n    }\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester)\\n        external\\n        override\\n        onlyHomeProxy\\n    {\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_questionID][_requester];\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\n\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\n\\n        if (arbitration.deposit >= arbitrationCost) {\\n            try\\n                arbitrator.createDispute{value: arbitrationCost}(NUMBER_OF_CHOICES_FOR_ARBITRATOR, arbitratorExtraData)\\n            returns (uint256 disputeID) {\\n                DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[disputeID];\\n                disputeDetails.questionID = _questionID;\\n                disputeDetails.requester = _requester;\\n\\n                questionIDToDisputeExists[_questionID] = true;\\n\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\n\\n                arbitration.status = Status.Created;\\n                arbitration.deposit = 0;\\n\\n                if (remainder > 0) {\\n                    payable(_requester).send(remainder);\\n                }\\n\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\n                emit Dispute(arbitrator, disputeID, META_EVIDENCE_ID, uint256(_questionID));\\n            } catch {\\n                arbitration.status = Status.Failed;\\n                emit ArbitrationFailed(_questionID, _requester);\\n            }\\n        } else {\\n            arbitration.status = Status.Failed;\\n            emit ArbitrationFailed(_questionID, _requester);\\n        }\\n    }\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external override onlyHomeProxy {\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_questionID][_requester];\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\n        uint256 deposit = arbitration.deposit;\\n\\n        delete arbitrationRequests[_questionID][_requester];\\n\\n        payable(_requester).send(deposit);\\n\\n        emit ArbitrationCanceled(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external override {\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_questionID][_requester];\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\n        uint256 deposit = arbitration.deposit;\\n\\n        delete arbitrationRequests[_questionID][_requester];\\n\\n        payable(_requester).send(deposit);\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy(0).receiveArbitrationFailure.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\n\\n        emit ArbitrationCanceled(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @notice Rules a specified dispute.\\n     * @dev Note that 0 is reserved for \\\"Unable/refused to arbitrate\\\" and we map it to `bytes32(-1)` which has a similar meaning in Realitio.\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\n     * @param _ruling The ruling given by the arbitrator.\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external override onlyArbitrator {\\n        DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[_disputeID];\\n        bytes32 questionID = disputeDetails.questionID;\\n        address requester = disputeDetails.requester;\\n\\n        ArbitrationRequest storage arbitration = arbitrationRequests[questionID][requester];\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\n\\n        arbitration.status = Status.Ruled;\\n\\n        // Realitio ruling is shifted by 1 compared to Kleros.\\n        // For example, jurors refusing to rule is `0` on Kleros, but uint(-1) on Realitio.\\n        // The line below could be written more explicitly as:\\n        //     bytes32(_ruling == 0 ? uint256(-1) : _ruling - 1)\\n        // But the way it is written saves some gas.\\n        bytes32 answer = bytes32(_ruling - 1);\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy(0).receiveArbitrationAnswer.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, questionID, answer);\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\n\\n        emit Ruling(arbitrator, _disputeID, _ruling);\\n    }\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @param _questionID the ID of the question.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 _questionID) external view override returns (uint256) {\\n        return arbitrator.arbitrationCost(arbitratorExtraData);\\n    }\\n}\\n\",\"keccak256\":\"0x46e2464f64e63c577ddabbc75c4011a690a4b1f60ab41b1ec814825b47f84763\",\"license\":\"MIT\"},\"src/dependencies/IAMB.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.7.2;\\n\\ninterface IAMB {\\n    function requireToPassMessage(\\n        address _contract,\\n        bytes memory _data,\\n        uint256 _gas\\n    ) external returns (bytes32);\\n\\n    function maxGasPerTx() external view returns (uint256);\\n\\n    function messageSender() external view returns (address);\\n\\n    function messageSourceChainId() external view returns (bytes32);\\n\\n    function messageId() external view returns (bytes32);\\n}\\n\",\"keccak256\":\"0x9fa6c7595f10a3c46a9cc6fdb4f2bcdff0a2476ae7f4e2f41a3182797a74dc4e\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.", "kind": "dev", "methods": {"constructor": {"params": {"_amb": "ArbitraryMessageBridge contract address.", "_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_homeChainId": "The ID of the counter-party Home Chain.", "_homeProxy": "The address of the proxy contract in the counter-party Home Chain (i.e.: xDAI)", "_metaEvidence": "The URI of the meta evidence file.", "_termsOfService": "The path for the Terms of Service for Kleros as an arbitrator for Realitio."}}, "getDisputeFee(bytes32)": {"params": {"_questionID": "the ID of the question."}, "returns": {"_0": "The fee to create a dispute."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Note that 0 is reserved for \"Unable/refused to arbitrate\" and we map it to `bytes32(-1)` which has a similar meaning in Realitio.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}}, "stateVariables": {"META_EVIDENCE_ID": {"details": "The ID of the MetaEvidence for disputes."}, "NUMBER_OF_CHOICES_FOR_ARBITRATOR": {"details": "The number of choices for the arbitrator. K<PERSON><PERSON> is currently able to provide ruling values of up to 2^256 - 2."}, "amb": {"details": "ArbitraryMessageBridge contract address. TRUSTED."}, "arbitrationRequests": {"details": "Tracks arbitration requests for question ID. arbitrationRequests[questionID][requester]"}, "arbitrator": {"details": "The address of the arbitrator. TRUSTED."}, "arbitratorExtraData": {"details": "The extra data used to raise a dispute in the arbitrator."}, "disputeIDToDisputeDetails": {"details": "Associates dispute ID to question <PERSON> and the requester. disputeIDToDisputeDetails[disputeID] -> {questionID, requester}"}, "homeChainId": {"details": "The chain ID where the home proxy is deployed."}, "homeProxy": {"details": "Address of the counter-party proxy on the Home Chain. TRUSTED."}, "questionIDToDisputeExists": {"details": "Whether a dispute has already been created for the given question ID or not. questionIDToDisputeExists[questionID]"}, "termsOfService": {"details": "The path for the Terms of Service for Kleros as an arbitrator for Realitio."}}, "title": "Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the foreign chain."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 574, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "arbitratorExtraData", "offset": 0, "slot": "0", "type": "t_bytes_storage"}, {"astId": 577, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "termsOfService", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 614, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "arbitrationRequests", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_mapping(t_address,t_struct(ArbitrationRequest)602_storage))"}, {"astId": 619, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "disputeIDToDisputeDetails", "offset": 0, "slot": "3", "type": "t_mapping(t_uint256,t_struct(DisputeDetails)607_storage)"}, {"astId": 624, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "questionIDToDisputeExists", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_bool)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_enum(Status)597": {"encoding": "inplace", "label": "enum RealitioForeignArbitrationProxy.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_struct(ArbitrationRequest)602_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignArbitrationProxy.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)602_storage"}, "t_mapping(t_bytes32,t_bool)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_bytes32,t_mapping(t_address,t_struct(ArbitrationRequest)602_storage))": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => mapping(address => struct RealitioForeignArbitrationProxy.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)602_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)607_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignArbitrationProxy.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)607_storage"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(ArbitrationRequest)602_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxy.ArbitrationRequest", "members": [{"astId": 599, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)597"}, {"astId": 601, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}], "numberOfBytes": "32"}, "t_struct(DisputeDetails)607_storage": {"encoding": "inplace", "label": "struct RealitioForeignArbitrationProxy.DisputeDetails", "members": [{"astId": 604, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "questionID", "offset": 0, "slot": "0", "type": "t_bytes32"}, {"astId": 606, "contract": "src/RealitioForeignArbitrationProxy.sol:RealitioForeignArbitrationProxy", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}
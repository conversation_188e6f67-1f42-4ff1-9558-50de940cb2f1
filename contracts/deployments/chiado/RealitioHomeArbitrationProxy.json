{"address": "0xE620947519E8102aa625BBB4669fE317c9FffeD7", "abi": [{"inputs": [{"internalType": "contract IAMB", "name": "_amb", "type": "address"}, {"internalType": "address", "name": "_foreignProxy", "type": "address"}, {"internalType": "bytes32", "name": "_foreignChainId", "type": "bytes32"}, {"internalType": "contract RealitioInterface", "name": "_realitio", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "ArbitrationFinished", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "ArbitratorAnswered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestAcknowledged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "RequestNotified", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_reason", "type": "string"}], "name": "RequestRejected", "type": "event"}, {"inputs": [], "name": "amb", "outputs": [{"internalType": "contract IAMB", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleNotifiedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleRejectedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "metadata", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "questionIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "realitio", "outputs": [{"internalType": "contract RealitioInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "receiveArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationFailure", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "receiveArbitrationRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastHistoryHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastAnswerOrCommitmentID", "type": "bytes32"}, {"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "reportArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "requests", "outputs": [{"internalType": "enum RealitioHomeArbitrationProxy.Status", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "arbitratorAnswer", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0x6ac21faa445ae46009adaea995f41b915becbdf7175101056c043afe8b981957", "receipt": {"to": null, "from": "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "contractAddress": "0xE620947519E8102aa625BBB4669fE317c9FffeD7", "transactionIndex": 1, "gasUsed": "1372519", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x441bd9520fc065b944a7758c1b5cc2abd41e6f938efda8a603ac9039abc8c3c2", "transactionHash": "0x6ac21faa445ae46009adaea995f41b915becbdf7175101056c043afe8b981957", "logs": [], "blockNumber": 10690841, "cumulativeGasUsed": "1411179", "status": 1, "byzantium": true}, "args": ["0x8448E15d0e706C0298dECA99F0b4744030e59d7d", "0x5d7cB72B31C080CF2de5f57fd38DedBeaf969D42", "0x0000000000000000000000000000000000000000000000000000000000aa36a7", "0x1E732a1C5e9181622DD5A931Ec6801889ce66185"], "numDeployments": 1, "solcInputHash": "b1a777c496e98e8464951bb1dc2ef902", "metadata": "{\"compiler\":{\"version\":\"0.7.6+commit.7338295f\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"_amb\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_foreignProxy\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"_foreignChainId\",\"type\":\"bytes32\"},{\"internalType\":\"contract RealitioInterface\",\"name\":\"_realitio\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"ArbitrationFinished\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_answer\",\"type\":\"bytes32\"}],\"name\":\"ArbitratorAnswered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"RequestAcknowledged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"RequestCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"RequestNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_reason\",\"type\":\"string\"}],\"name\":\"RequestRejected\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"amb\",\"outputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"foreignChainId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"foreignProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleNotifiedRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleRejectedRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"metadata\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"questionIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"realitio\",\"outputs\":[{\"internalType\":\"contract RealitioInterface\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_answer\",\"type\":\"bytes32\"}],\"name\":\"receiveArbitrationAnswer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationFailure\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"receiveArbitrationRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_lastHistoryHash\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_lastAnswerOrCommitmentID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_lastAnswerer\",\"type\":\"address\"}],\"name\":\"reportArbitrationAnswer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"requests\",\"outputs\":[{\"internalType\":\"enum RealitioHomeArbitrationProxy.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"arbitratorAnswer\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to side-chains (i.e.: xDAI) in which Reality.eth is deployed.\",\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_amb\":\"ArbitraryMessageBridge contract address.\",\"_foreignChainId\":\"The ID of the chain where the foreign proxy is deployed.\",\"_foreignProxy\":\"The address of the proxy.\",\"_realitio\":\"Realitio contract address.\"}},\"handleNotifiedRequest(bytes32,address)\":{\"details\":\"This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"handleRejectedRequest(bytes32,address)\":{\"details\":\"This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it. Reasons why the request might be rejected:  - The question does not exist  - The question was not answered yet  - The quesiton bond value changed while the arbitration was being requested  - Another request was already accepted\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"receiveArbitrationAnswer(bytes32,bytes32)\":{\"params\":{\"_answer\":\"The answer from the arbitrator.\",\"_questionID\":\"The ID of the question.\"}},\"receiveArbitrationFailure(bytes32,address)\":{\"details\":\"Currently this can happen only if the arbitration cost increased.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"receiveArbitrationRequest(bytes32,address,uint256)\":{\"details\":\"Receives the requested arbitration for a question. TRUSTED.\",\"params\":{\"_maxPrevious\":\"The maximum value of the previous bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"reportArbitrationAnswer(bytes32,bytes32,bytes32,address)\":{\"details\":\"The Realitio contract validates the input parameters passed to this method, so making this publicly accessible is safe.\",\"params\":{\"_lastAnswerOrCommitmentID\":\"The last answer given, or its commitment ID if it was a commitment, to the question in the Realitio contract.\",\"_lastAnswerer\":\"The last answerer to the question in the Realitio contract.\",\"_lastHistoryHash\":\"The history hash given with the last answer to the question in the Realitio contract.\",\"_questionID\":\"The ID of the question.\"}}},\"stateVariables\":{\"amb\":{\"details\":\"ArbitraryMessageBridge contract address. TRUSTED.\"},\"foreignChainId\":{\"details\":\"The chain ID where the foreign proxy is deployed.\"},\"foreignProxy\":{\"details\":\"Address of the counter-party proxy on the Foreign Chain. TRUSTED.\"},\"metadata\":{\"details\":\"Metadata for Realitio interface.\"},\"questionIDToRequester\":{\"details\":\"Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]\"},\"realitio\":{\"details\":\"The address of the Realitio contract (v2.1+ required). TRUSTED.\"},\"requests\":{\"details\":\"Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]\"}},\"title\":\"Arbitration proxy for Realitio on the side-chain side (A.K.A. the Home Chain).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"To be emitted when the dispute could not be created on the Foreign Chain.\"},\"ArbitrationFinished(bytes32)\":{\"notice\":\"To be emitted when reporting the arbitrator answer to Realitio.\"},\"ArbitratorAnswered(bytes32,bytes32)\":{\"notice\":\"To be emitted when receiving the answer from the arbitrator.\"},\"RequestAcknowledged(bytes32,address)\":{\"notice\":\"To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\"},\"RequestCanceled(bytes32,address)\":{\"notice\":\"To be emitted when the arbitration request is canceled.\"},\"RequestNotified(bytes32,address,uint256)\":{\"notice\":\"To be emitted when the Realitio contract has been notified of an arbitration request.\"},\"RequestRejected(bytes32,address,uint256,string)\":{\"notice\":\"To be emitted when arbitration request is rejected.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the home chain.\"},\"handleNotifiedRequest(bytes32,address)\":{\"notice\":\"Handles arbitration request after it has been notified to Realitio for a given question.\"},\"handleRejectedRequest(bytes32,address)\":{\"notice\":\"Handles arbitration request after it has been rejected.\"},\"receiveArbitrationAnswer(bytes32,bytes32)\":{\"notice\":\"Receives the answer to a specified question. TRUSTED.\"},\"receiveArbitrationFailure(bytes32,address)\":{\"notice\":\"Receives a failed attempt to request arbitration. TRUSTED.\"},\"reportArbitrationAnswer(bytes32,bytes32,bytes32,address)\":{\"notice\":\"Reports the answer provided by the arbitrator to a specified question.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioHomeArbitrationProxy.sol\":\"RealitioHomeArbitrationProxy\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0x1803a3433a78c509b20bd9477a2c60a71b2ce1ee7e17eb0ef0601618a8a72526\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {Waiting, Appealable, Solved}\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0x240a4142f9ec379da0333dfc82409b7b058cff9ea118368eb5e8f15447996c1e\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence A link to the meta-evidence JSON.\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0x1ccedf5213730632540c748486637d7b1977ee73375818bf498a8276ca49dd13\",\"license\":\"MIT\"},\"src/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\r\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\r\\n\\r\\ninterface IHomeArbitrationProxy {\\r\\n    /**\\r\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when arbitration request is rejected.\\r\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\r\\n     * or if the question is already finalized.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\r\\n     * @param _reason The reason why the request was rejected.\\r\\n     */\\r\\n    event RequestRejected(\\r\\n        bytes32 indexed _questionID,\\r\\n        address indexed _requester,\\r\\n        uint256 _maxPrevious,\\r\\n        string _reason\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request is canceled.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\r\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\r\\n     * @param _questionID The ID of the question.\\r\\n     */\\r\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function receiveArbitrationRequest(\\r\\n        bytes32 _questionID,\\r\\n        address _requester,\\r\\n        uint256 _maxPrevious\\r\\n    ) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\r\\n}\\r\\n\\r\\ninterface IForeignArbitrationProxy is IArbitrable, IEvidence {\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is requested.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute is created.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _disputeID The ID of the dispute.\\r\\n     */\\r\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute could not be created.\\r\\n     * @dev This will happen if there is an increase in the arbitration fees\\r\\n     * between the time the arbitration is made and the time it is acknowledged.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @param _questionID the ID of the question.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\r\\n}\\r\\n\",\"keccak256\":\"0xd8328caaa97aa516acde0345e30b7a102092e8e4ab608608d62c8c02ff2f1605\",\"license\":\"MIT\"},\"src/RealitioHomeArbitrationProxy.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n *  @authors: [@hbarcelos]\\r\\n *  @reviewers: [@ferittuncer, @fnanni-0, @nix1g, @epiqueras*, @clesaege, @unknownunknown1]\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: [0xe40DD83a262da3f56976038F1554Fe541Fa75ecd]\\r\\n */\\r\\n\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\nimport {IAMB} from \\\"./dependencies/IAMB.sol\\\";\\r\\nimport {RealitioInterface} from \\\"./dependencies/RealitioInterface.sol\\\";\\r\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./ArbitrationProxyInterfaces.sol\\\";\\r\\n\\r\\n/**\\r\\n * @title Arbitration proxy for Realitio on the side-chain side (A.K.A. the Home Chain).\\r\\n * @dev This contract is meant to be deployed to side-chains (i.e.: xDAI) in which Reality.eth is deployed.\\r\\n */\\r\\ncontract RealitioHomeArbitrationProxy is IHomeArbitrationProxy {\\r\\n    /// @dev The address of the Realitio contract (v2.1+ required). TRUSTED.\\r\\n    RealitioInterface public immutable realitio;\\r\\n\\r\\n    /// @dev ArbitraryMessageBridge contract address. TRUSTED.\\r\\n    IAMB public immutable amb;\\r\\n\\r\\n    /// @dev Address of the counter-party proxy on the Foreign Chain. TRUSTED.\\r\\n    address public immutable foreignProxy;\\r\\n\\r\\n    /// @dev The chain ID where the foreign proxy is deployed.\\r\\n    bytes32 public immutable foreignChainId;\\r\\n\\r\\n    /// @dev Metadata for Realitio interface.\\r\\n    string public constant metadata = '{\\\"foreignProxy\\\":true}';\\r\\n\\r\\n    enum Status {\\r\\n        None,\\r\\n        Rejected,\\r\\n        Notified,\\r\\n        AwaitingRuling,\\r\\n        Ruled,\\r\\n        Finished\\r\\n    }\\r\\n\\r\\n    struct Request {\\r\\n        Status status;\\r\\n        bytes32 arbitratorAnswer;\\r\\n    }\\r\\n\\r\\n    /// @dev Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]\\r\\n    mapping(bytes32 => mapping(address => Request)) public requests;\\r\\n\\r\\n    /// @dev Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]\\r\\n    mapping(bytes32 => address) public questionIDToRequester;\\r\\n\\r\\n    modifier onlyForeignProxy() {\\r\\n        require(msg.sender == address(amb), \\\"Only AMB allowed\\\");\\r\\n        require(amb.messageSourceChainId() == foreignChainId, \\\"Only foreign chain allowed\\\");\\r\\n        require(amb.messageSender() == foreignProxy, \\\"Only foreign proxy allowed\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Creates an arbitration proxy on the home chain.\\r\\n     * @param _amb ArbitraryMessageBridge contract address.\\r\\n     * @param _foreignProxy The address of the proxy.\\r\\n     * @param _foreignChainId The ID of the chain where the foreign proxy is deployed.\\r\\n     * @param _realitio Realitio contract address.\\r\\n     */\\r\\n    constructor(\\r\\n        IAMB _amb,\\r\\n        address _foreignProxy,\\r\\n        bytes32 _foreignChainId,\\r\\n        RealitioInterface _realitio\\r\\n    ) {\\r\\n        amb = _amb;\\r\\n        foreignProxy = _foreignProxy;\\r\\n        foreignChainId = _foreignChainId;\\r\\n        realitio = _realitio;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    function receiveArbitrationRequest(\\r\\n        bytes32 _questionID,\\r\\n        address _requester,\\r\\n        uint256 _maxPrevious\\r\\n    ) external override onlyForeignProxy {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.None, \\\"Request already exists\\\");\\r\\n\\r\\n        try realitio.notifyOfArbitrationRequest(_questionID, _requester, _maxPrevious) {\\r\\n            request.status = Status.Notified;\\r\\n            questionIDToRequester[_questionID] = _requester;\\r\\n\\r\\n            emit RequestNotified(_questionID, _requester, _maxPrevious);\\r\\n        } catch Error(string memory reason) {\\r\\n            /*\\r\\n             * Will fail if:\\r\\n             *  - The question does not exist.\\r\\n             *  - The question was not answered yet.\\r\\n             *  - Another request was already accepted.\\r\\n             *  - Someone increased the bond on the question to a value > _maxPrevious\\r\\n             */\\r\\n            request.status = Status.Rejected;\\r\\n\\r\\n            emit RequestRejected(_questionID, _requester, _maxPrevious, reason);\\r\\n        } catch {\\r\\n            // In case `reject` did not have a reason string or some other error happened\\r\\n            request.status = Status.Rejected;\\r\\n\\r\\n            emit RequestRejected(_questionID, _requester, _maxPrevious, \\\"\\\");\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external override {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.Notified, \\\"Invalid request status\\\");\\r\\n\\r\\n        request.status = Status.AwaitingRuling;\\r\\n\\r\\n        bytes4 selector = IForeignArbitrationProxy(0).receiveArbitrationAcknowledgement.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(selector, _questionID, _requester);\\r\\n        amb.requireToPassMessage(foreignProxy, data, amb.maxGasPerTx());\\r\\n\\r\\n        emit RequestAcknowledged(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external override {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.Rejected, \\\"Invalid request status\\\");\\r\\n\\r\\n        // At this point, only the request.status is set, simply reseting the status to Status.None is enough.\\r\\n        request.status = Status.None;\\r\\n\\r\\n        bytes4 selector = IForeignArbitrationProxy(0).receiveArbitrationCancelation.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(selector, _questionID, _requester);\\r\\n        amb.requireToPassMessage(foreignProxy, data, amb.maxGasPerTx());\\r\\n\\r\\n        emit RequestCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external override onlyForeignProxy {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.AwaitingRuling, \\\"Invalid request status\\\");\\r\\n\\r\\n        // At this point, only the request.status is set, simply reseting the status to Status.None is enough.\\r\\n        request.status = Status.None;\\r\\n\\r\\n        realitio.cancelArbitration(_questionID);\\r\\n\\r\\n        emit ArbitrationFailed(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external override onlyForeignProxy {\\r\\n        address requester = questionIDToRequester[_questionID];\\r\\n        Request storage request = requests[_questionID][requester];\\r\\n        require(request.status == Status.AwaitingRuling, \\\"Invalid request status\\\");\\r\\n\\r\\n        request.status = Status.Ruled;\\r\\n        request.arbitratorAnswer = _answer;\\r\\n\\r\\n        emit ArbitratorAnswered(_questionID, _answer);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Reports the answer provided by the arbitrator to a specified question.\\r\\n     * @dev The Realitio contract validates the input parameters passed to this method,\\r\\n     * so making this publicly accessible is safe.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _lastHistoryHash The history hash given with the last answer to the question in the Realitio contract.\\r\\n     * @param _lastAnswerOrCommitmentID The last answer given, or its commitment ID if it was a commitment,\\r\\n     * to the question in the Realitio contract.\\r\\n     * @param _lastAnswerer The last answerer to the question in the Realitio contract.\\r\\n     */\\r\\n    function reportArbitrationAnswer(\\r\\n        bytes32 _questionID,\\r\\n        bytes32 _lastHistoryHash,\\r\\n        bytes32 _lastAnswerOrCommitmentID,\\r\\n        address _lastAnswerer\\r\\n    ) external {\\r\\n        address requester = questionIDToRequester[_questionID];\\r\\n        Request storage request = requests[_questionID][requester];\\r\\n        require(request.status == Status.Ruled, \\\"Arbitrator has not ruled yet\\\");\\r\\n\\r\\n        realitio.assignWinnerAndSubmitAnswerByArbitrator(\\r\\n            _questionID,\\r\\n            request.arbitratorAnswer,\\r\\n            requester,\\r\\n            _lastHistoryHash,\\r\\n            _lastAnswerOrCommitmentID,\\r\\n            _lastAnswerer\\r\\n        );\\r\\n\\r\\n        request.status = Status.Finished;\\r\\n\\r\\n        emit ArbitrationFinished(_questionID);\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x77d2105e42b9a72e1dd9f2935b3696fd7b1cfcac60484c3c2f28a8ebff8d11d9\",\"license\":\"MIT\"},\"src/dependencies/IAMB.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\ninterface IAMB {\\r\\n    function requireToPassMessage(\\r\\n        address _contract,\\r\\n        bytes memory _data,\\r\\n        uint256 _gas\\r\\n    ) external returns (bytes32);\\r\\n\\r\\n    function maxGasPerTx() external view returns (uint256);\\r\\n\\r\\n    function messageSender() external view returns (address);\\r\\n\\r\\n    function messageSourceChainId() external view returns (bytes32);\\r\\n\\r\\n    function messageId() external view returns (bytes32);\\r\\n}\\r\\n\",\"keccak256\":\"0x2e79d0690426c2daa93907cebce6ec6f91c03a25cf04b1fd6aefda033b3134bb\",\"license\":\"MIT\"},\"src/dependencies/RealitioInterface.sol\":{\"content\":\"/* solhint-disable var-name-mixedcase */\\r\\n// SPDX-License-Identifier: MIT\\r\\n\\r\\n/** Interface of https://github.com/realitio/realitio-contracts/blob/master/truffle/contracts/Realitio_v2_1.sol original contract is to be reviewed.\\r\\n *  @reviewers: [@hbarcelos, @fnanni-0, @nix1g, @unknownunknown1, @ferittuncer]\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity ^0.7.2;\\r\\n\\r\\ninterface RealitioInterface {\\r\\n    event LogNewAnswer(\\r\\n        bytes32 answer,\\r\\n        bytes32 indexed question_id,\\r\\n        bytes32 history_hash,\\r\\n        address indexed user,\\r\\n        uint256 bond,\\r\\n        uint256 ts,\\r\\n        bool is_commitment\\r\\n    );\\r\\n\\r\\n    event LogNewTemplate(uint256 indexed template_id, address indexed user, string question_text);\\r\\n\\r\\n    event LogNewQuestion(\\r\\n        bytes32 indexed question_id,\\r\\n        address indexed user,\\r\\n        uint256 template_id,\\r\\n        string question,\\r\\n        bytes32 indexed content_hash,\\r\\n        address arbitrator,\\r\\n        uint32 timeout,\\r\\n        uint32 opening_ts,\\r\\n        uint256 nonce,\\r\\n        uint256 created\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @dev The arbitrator contract is trusted to only call this if they've been paid, and tell us who paid them.\\r\\n     * @notice Notify the contract that the arbitrator has been paid for a question, freezing it pending their decision.\\r\\n     * @param question_id The ID of the question.\\r\\n     * @param requester The account that requested arbitration.\\r\\n     * @param max_previous If specified, reverts if a bond higher than this was submitted after you sent your transaction.\\r\\n     */\\r\\n    function notifyOfArbitrationRequest(\\r\\n        bytes32 question_id,\\r\\n        address requester,\\r\\n        uint256 max_previous\\r\\n    ) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancel a previously-requested arbitration and extend the timeout\\r\\n     * @dev Useful when doing arbitration across chains that can't be requested atomically\\r\\n     * @param question_id The ID of the question\\r\\n     */\\r\\n    function cancelArbitration(bytes32 question_id) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Submit the answer for a question, for use by the arbitrator, working out the appropriate winner based on the last answer details.\\r\\n     * @dev Doesn't require (or allow) a bond.\\r\\n     * @param question_id The ID of the question\\r\\n     * @param answer The answer, encoded into bytes32\\r\\n     * @param payee_if_wrong The account to be credited as winner if the last answer given is wrong, usually the account that paid the arbitrator\\r\\n     * @param last_history_hash The history hash before the final one\\r\\n     * @param last_answer_or_commitment_id The last answer given, or the commitment ID if it was a commitment.\\r\\n     * @param last_answerer The address that supplied the last answer\\r\\n     */\\r\\n    function assignWinnerAndSubmitAnswerByArbitrator(\\r\\n        bytes32 question_id,\\r\\n        bytes32 answer,\\r\\n        address payee_if_wrong,\\r\\n        bytes32 last_history_hash,\\r\\n        bytes32 last_answer_or_commitment_id,\\r\\n        address last_answerer\\r\\n    ) external;\\r\\n}\\r\\n\",\"keccak256\":\"0x3acacbd4c0a7f00773ebe771c93326e55d89d5d006475fe5f7c2dd564815c397\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to side-chains (i.e.: xDAI) in which Reality.eth is deployed.", "kind": "dev", "methods": {"constructor": {"params": {"_amb": "ArbitraryMessageBridge contract address.", "_foreignChainId": "The ID of the chain where the foreign proxy is deployed.", "_foreignProxy": "The address of the proxy.", "_realitio": "Realitio contract address."}}, "handleNotifiedRequest(bytes32,address)": {"details": "This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "handleRejectedRequest(bytes32,address)": {"details": "This method exists because `receiveArbitrationRequest` is called by the AMB and cannot send messages back to it. Reasons why the request might be rejected:  - The question does not exist  - The question was not answered yet  - The quesiton bond value changed while the arbitration was being requested  - Another request was already accepted", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "receiveArbitrationAnswer(bytes32,bytes32)": {"params": {"_answer": "The answer from the arbitrator.", "_questionID": "The ID of the question."}}, "receiveArbitrationFailure(bytes32,address)": {"details": "Currently this can happen only if the arbitration cost increased.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "receiveArbitrationRequest(bytes32,address,uint256)": {"details": "Receives the requested arbitration for a question. TRUSTED.", "params": {"_maxPrevious": "The maximum value of the previous bond for the question.", "_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "reportArbitrationAnswer(bytes32,bytes32,bytes32,address)": {"details": "The Realitio contract validates the input parameters passed to this method, so making this publicly accessible is safe.", "params": {"_lastAnswerOrCommitmentID": "The last answer given, or its commitment ID if it was a commitment, to the question in the Realitio contract.", "_lastAnswerer": "The last answerer to the question in the Realitio contract.", "_lastHistoryHash": "The history hash given with the last answer to the question in the Realitio contract.", "_questionID": "The ID of the question."}}}, "stateVariables": {"amb": {"details": "ArbitraryMessageBridge contract address. TRUSTED."}, "foreignChainId": {"details": "The chain ID where the foreign proxy is deployed."}, "foreignProxy": {"details": "Address of the counter-party proxy on the Foreign Chain. TRUSTED."}, "metadata": {"details": "Metadata for Realitio interface."}, "questionIDToRequester": {"details": "Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]"}, "realitio": {"details": "The address of the Realitio contract (v2.1+ required). TRUSTED."}, "requests": {"details": "Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]"}}, "title": "Arbitration proxy for Realitio on the side-chain side (A.K.A. the Home Chain).", "version": 1}, "userdoc": {"events": {"ArbitrationFailed(bytes32,address)": {"notice": "To be emitted when the dispute could not be created on the Foreign Chain."}, "ArbitrationFinished(bytes32)": {"notice": "To be emitted when reporting the arbitrator answer to <PERSON><PERSON><PERSON>."}, "ArbitratorAnswered(bytes32,bytes32)": {"notice": "To be emitted when receiving the answer from the arbitrator."}, "RequestAcknowledged(bytes32,address)": {"notice": "To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain."}, "RequestCanceled(bytes32,address)": {"notice": "To be emitted when the arbitration request is canceled."}, "RequestNotified(bytes32,address,uint256)": {"notice": "To be emitted when the Realitio contract has been notified of an arbitration request."}, "RequestRejected(bytes32,address,uint256,string)": {"notice": "To be emitted when arbitration request is rejected."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the home chain."}, "handleNotifiedRequest(bytes32,address)": {"notice": "Handles arbitration request after it has been notified to Realitio for a given question."}, "handleRejectedRequest(bytes32,address)": {"notice": "Handles arbitration request after it has been rejected."}, "receiveArbitrationAnswer(bytes32,bytes32)": {"notice": "Receives the answer to a specified question. TRUSTED."}, "receiveArbitrationFailure(bytes32,address)": {"notice": "Receives a failed attempt to request arbitration. TRUSTED."}, "reportArbitrationAnswer(bytes32,bytes32,bytes32,address)": {"notice": "Reports the answer provided by the arbitrator to a specified question."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 3056, "contract": "src/RealitioHomeArbitrationProxy.sol:RealitioHomeArbitrationProxy", "label": "requests", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_mapping(t_address,t_struct(Request)3049_storage))"}, {"astId": 3061, "contract": "src/RealitioHomeArbitrationProxy.sol:RealitioHomeArbitrationProxy", "label": "questionIDToRequester", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_address)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_enum(Status)3044": {"encoding": "inplace", "label": "enum RealitioHomeArbitrationProxy.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_struct(Request)3049_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioHomeArbitrationProxy.Request)", "numberOfBytes": "32", "value": "t_struct(Request)3049_storage"}, "t_mapping(t_bytes32,t_address)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_bytes32,t_mapping(t_address,t_struct(Request)3049_storage))": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => mapping(address => struct RealitioHomeArbitrationProxy.Request))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(Request)3049_storage)"}, "t_struct(Request)3049_storage": {"encoding": "inplace", "label": "struct RealitioHomeArbitrationProxy.Request", "members": [{"astId": 3046, "contract": "src/RealitioHomeArbitrationProxy.sol:RealitioHomeArbitrationProxy", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)3044"}, {"astId": 3048, "contract": "src/RealitioHomeArbitrationProxy.sol:RealitioHomeArbitrationProxy", "label": "arbitratorAnswer", "offset": 0, "slot": "1", "type": "t_bytes32"}], "numberOfBytes": "64"}}}}
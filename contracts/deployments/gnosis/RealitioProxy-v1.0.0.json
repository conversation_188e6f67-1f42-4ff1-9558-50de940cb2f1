{"version": "1.0.0", "versionNotes": "No appeals", "deployments": [{"name": "Omen AI", "realitio": {"contract": "Realitio_v2_1", "address": "0x79e32aE03fb27B07C89c0c568F80287C01ca2E57", "token": ""}, "homeProxy": {"address": "0xe40DD83a262da3f56976038F1554Fe541Fa75ecd", "tos": "", "blockNumber": "14332325", "transactionHash": "0x36f965a4ec0ef7460d8932b865087a7b85bfca3a350f00008e214cb52d8ca459"}, "foreignProxy": {"courtId": "0", "minJurors": "500", "metaevidence": "https://cdn.kleros.link/ipfs/Qmc6bWTzPMFeRx9VWHwnDpDXfimwNsvnEgJo3gymg37rRd/realitio.json", "address": "0x79d0464Ec27F67663DADf761432fC8DD0AeA3D49", "chainId": "1", "blockNumber": "11771713", "transactionHash": "0xc54791433ec4a80075d3a0f9abb63184126858f56def29e8030b9ef8c1247d82"}}], "homeProxyAbi": [{"inputs": [{"internalType": "contract IAMB", "name": "_amb", "type": "address"}, {"internalType": "address", "name": "_foreignProxy", "type": "address"}, {"internalType": "bytes32", "name": "_foreignChainId", "type": "bytes32"}, {"internalType": "contract RealitioInterface", "name": "_realitio", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "ArbitrationFinished", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "ArbitratorAnswered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestAcknowledged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "RequestNotified", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_reason", "type": "string"}], "name": "RequestRejected", "type": "event"}, {"inputs": [], "name": "amb", "outputs": [{"internalType": "contract IAMB", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleNotifiedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleRejectedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "metadata", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "questionIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "realitio", "outputs": [{"internalType": "contract RealitioInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "receiveArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationFailure", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "receiveArbitrationRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastHistoryHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastAnswerOrCommitmentID", "type": "bytes32"}, {"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "reportArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "requests", "outputs": [{"internalType": "enum RealitioHomeArbitrationProxy.Status", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "arbitratorAnswer", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "foreignProxyAbi": [{"inputs": [{"internalType": "contract IAMB", "name": "_amb", "type": "address"}, {"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "bytes32", "name": "_homeChainId", "type": "bytes32"}, {"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "string", "name": "_termsOfService", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"inputs": [], "name": "META_EVIDENCE_ID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "amb", "outputs": [{"internalType": "contract IAMB", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignArbitrationProxy.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "disputeIDToDisputeDetails", "outputs": [{"internalType": "bytes32", "name": "questionID", "type": "bytes32"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "homeChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "questionIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "termsOfService", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}]}
{"address": "0x890deB4111F92fE9447e83aBEF1b754372d6770e", "abi": [{"inputs": [{"internalType": "contract RealitioInterface", "name": "_realitio", "type": "address"}, {"internalType": "uint256", "name": "_foreignChainId", "type": "uint256"}, {"internalType": "address", "name": "_foreignProxy", "type": "address"}, {"internalType": "string", "name": "_metadata", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "ArbitrationFinished", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "ArbitratorAnswered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "withdrawalId", "type": "uint256"}], "name": "L2ToL1TxCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestAcknowledged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "RequestNotified", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_reason", "type": "string"}], "name": "RequestRejected", "type": "event"}, {"inputs": [], "name": "foreignChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleNotifiedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleRejectedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "metadata", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "questionIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "realitio", "outputs": [{"internalType": "contract RealitioInterface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "receiveArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationFailure", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "receiveArbitrationRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastHistoryHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastAnswerOrCommitmentID", "type": "bytes32"}, {"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "reportArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "requests", "outputs": [{"internalType": "enum RealitioHomeProxyArb.Status", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "arbitratorAnswer", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0x455cc643777fbcbe2010188d8a220164653a1d626af1039542c291802eb14494", "receipt": {"to": null, "from": "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "contractAddress": "0x890deB4111F92fE9447e83aBEF1b754372d6770e", "transactionIndex": 1, "gasUsed": "1064200", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x2ebf2431b4944828d37a2060ae5aed092a2f3068efa7f722d3286a441ee3d7d0", "transactionHash": "0x455cc643777fbcbe2010188d8a220164653a1d626af1039542c291802eb14494", "logs": [], "blockNumber": 96090013, "cumulativeGasUsed": "1064200", "status": 1, "byzantium": true}, "args": ["0xB78396EFaF0a177d125e9d45B2C6398Ac5f803B9", 11155111, "0x26222Ec1F548953a4fEaE4C5A216337E26A821F9", "{\"tos\":\"ipfs://QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf\", \"foreignProxy\":true}"], "numDeployments": 5, "solcInputHash": "35ea137d2a0fa48b09d37dc4ecf48c2d", "metadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract RealitioInterface\",\"name\":\"_realitio\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_foreignChainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_foreignProxy\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"_metadata\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"ArbitrationFinished\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_answer\",\"type\":\"bytes32\"}],\"name\":\"ArbitratorAnswered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"withdrawalId\",\"type\":\"uint256\"}],\"name\":\"L2ToL1TxCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"RequestAcknowledged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"RequestCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"RequestNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_reason\",\"type\":\"string\"}],\"name\":\"RequestRejected\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"foreignChainId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"foreignProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleNotifiedRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleRejectedRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"metadata\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"questionIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"realitio\",\"outputs\":[{\"internalType\":\"contract RealitioInterface\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_answer\",\"type\":\"bytes32\"}],\"name\":\"receiveArbitrationAnswer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationFailure\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"receiveArbitrationRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_lastHistoryHash\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_lastAnswerOrCommitmentID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_lastAnswerer\",\"type\":\"address\"}],\"name\":\"reportArbitrationAnswer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"requests\",\"outputs\":[{\"internalType\":\"enum RealitioHomeProxyArb.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"arbitratorAnswer\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to L2 where Reality.eth is deployed. https://docs.arbitrum.io/arbos/l2-to-l1-messaging Example https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/arbitrum/GreeterL2.sol\",\"events\":{\"ArbitrationFailed(bytes32,address)\":{\"details\":\"This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationFinished(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"}},\"ArbitratorAnswered(bytes32,bytes32)\":{\"params\":{\"_answer\":\"The answer from the arbitrator.\",\"_questionID\":\"The ID of the question.\"}},\"RequestAcknowledged(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"RequestCanceled(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"RequestNotified(bytes32,address,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the previous bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"RequestRejected(bytes32,address,uint256,string)\":{\"details\":\"This can happen if the current bond for the question is higher than maxPrevious or if the question is already finalized.\",\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_reason\":\"The reason why the request was rejected.\",\"_requester\":\"The address of the arbitration requester.\"}}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_foreignChainId\":\"The ID of foreign chain (Goerli/Mainnet).\",\"_foreignProxy\":\"Address of the proxy on L1.\",\"_metadata\":\"Metadata for Realitio.\",\"_realitio\":\"Realitio contract address.\"}},\"handleNotifiedRequest(bytes32,address)\":{\"details\":\"Relays arbitration request back to L1 after it has been notified by Realitio for a given question.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"handleRejectedRequest(bytes32,address)\":{\"details\":\"Relays arbitration request back to L1 after it has been rejected. Reasons why the request might be rejected:  - The question does not exist  - The question was not answered yet  - The quesiton bond value changed while the arbitration was being requested  - Another request was already accepted\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"receiveArbitrationAnswer(bytes32,bytes32)\":{\"params\":{\"_answer\":\"The answer from the arbitrator.\",\"_questionID\":\"The ID of the question.\"}},\"receiveArbitrationFailure(bytes32,address)\":{\"details\":\"Currently this can happen only if the arbitration cost increased.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"receiveArbitrationRequest(bytes32,address,uint256)\":{\"details\":\"Receives the requested arbitration for a question. TRUSTED.\",\"params\":{\"_maxPrevious\":\"The maximum value of the previous bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"reportArbitrationAnswer(bytes32,bytes32,bytes32,address)\":{\"details\":\"The Realitio contract validates the input parameters passed to this method, so making this publicly accessible is safe.\",\"params\":{\"_lastAnswerOrCommitmentID\":\"The last answer given, or its commitment ID if it was a commitment, to the question in the Realitio contract.\",\"_lastAnswerer\":\"The last answerer to the question in the Realitio contract.\",\"_lastHistoryHash\":\"The history hash given with the last answer to the question in the Realitio contract.\",\"_questionID\":\"The ID of the question.\"}}},\"stateVariables\":{\"foreignChainId\":{\"details\":\"ID of the foreign chain, required for Realitio.\"},\"metadata\":{\"details\":\"Metadata for Realitio interface.\"},\"questionIDToRequester\":{\"details\":\"Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]\"},\"realitio\":{\"details\":\"The address of the Realitio contract (v3.0 required). TRUSTED.\"},\"requests\":{\"details\":\"Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]\"}},\"title\":\"Arbitration proxy for Realitio on Arbitrum.\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"To be emitted when the dispute could not be created on the Foreign Chain.\"},\"ArbitrationFinished(bytes32)\":{\"notice\":\"To be emitted when reporting the arbitrator answer to Realitio.\"},\"ArbitratorAnswered(bytes32,bytes32)\":{\"notice\":\"To be emitted when receiving the answer from the arbitrator.\"},\"RequestAcknowledged(bytes32,address)\":{\"notice\":\"To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\"},\"RequestCanceled(bytes32,address)\":{\"notice\":\"To be emitted when the arbitration request is canceled.\"},\"RequestNotified(bytes32,address,uint256)\":{\"notice\":\"To be emitted when the Realitio contract has been notified of an arbitration request.\"},\"RequestRejected(bytes32,address,uint256,string)\":{\"notice\":\"To be emitted when arbitration request is rejected.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the home chain.\"},\"receiveArbitrationAnswer(bytes32,bytes32)\":{\"notice\":\"Receives an answer to a specified question. TRUSTED.\"},\"receiveArbitrationFailure(bytes32,address)\":{\"notice\":\"Receives a failed attempt to request arbitration. TRUSTED.\"},\"reportArbitrationAnswer(bytes32,bytes32,bytes32,address)\":{\"notice\":\"Reports the answer provided by the arbitrator to a specified question.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioHomeProxyArb.sol\":\"RealitioHomeProxyArb\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@arbitrum/nitro-contracts/src/libraries/AddressAliasHelper.sol\":{\"content\":\"// Copyright 2021-2022, Offchain Labs, Inc.\\n// For license information, see https://github.com/OffchainLabs/nitro-contracts/blob/main/LICENSE\\n// SPDX-License-Identifier: BUSL-1.1\\n\\npragma solidity ^0.8.0;\\n\\nlibrary AddressAliasHelper {\\n    uint160 internal constant OFFSET = uint160(0x1111000000000000000000000000000000001111);\\n\\n    /// @notice Utility function that converts the address in the L1 that submitted a tx to\\n    /// the inbox to the msg.sender viewed in the L2\\n    /// @param l1Address the address in the L1 that triggered the tx to L2\\n    /// @return l2Address L2 address as viewed in msg.sender\\n    function applyL1ToL2Alias(address l1Address) internal pure returns (address l2Address) {\\n        unchecked {\\n            l2Address = address(uint160(l1Address) + OFFSET);\\n        }\\n    }\\n\\n    /// @notice Utility function that converts the msg.sender viewed in the L2 to the\\n    /// address in the L1 that submitted a tx to the inbox\\n    /// @param l2Address L2 address as viewed in msg.sender\\n    /// @return l1Address the address in the L1 that triggered the tx to L2\\n    function undoL1ToL2Alias(address l2Address) internal pure returns (address l1Address) {\\n        unchecked {\\n            l1Address = address(uint160(l2Address) - OFFSET);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x53ab7b4f3e0179f4579bdaccbd68ef79246d28ce95632f8e3b585356fca0d6c0\",\"license\":\"BUSL-1.1\"},\"@arbitrum/nitro-contracts/src/precompiles/ArbSys.sol\":{\"content\":\"// Copyright 2021-2022, Offchain Labs, Inc.\\n// For license information, see https://github.com/OffchainLabs/nitro-contracts/blob/main/LICENSE\\n// SPDX-License-Identifier: BUSL-1.1\\n\\npragma solidity >=0.4.21 <0.9.0;\\n\\n/**\\n * @title System level functionality\\n * @notice For use by contracts to interact with core L2-specific functionality.\\n * Precompiled contract that exists in every Arbitrum chain at address(100), 0x0000000000000000000000000000000000000064.\\n */\\ninterface ArbSys {\\n    /**\\n     * @notice Get Arbitrum block number (distinct from L1 block number; Arbitrum genesis block has block number 0)\\n     * @return block number as int\\n     */\\n    function arbBlockNumber() external view returns (uint256);\\n\\n    /**\\n     * @notice Get Arbitrum block hash (reverts unless currentBlockNum-256 <= arbBlockNum < currentBlockNum)\\n     * @return block hash\\n     */\\n    function arbBlockHash(uint256 arbBlockNum) external view returns (bytes32);\\n\\n    /**\\n     * @notice Gets the rollup's unique chain identifier\\n     * @return Chain identifier as int\\n     */\\n    function arbChainID() external view returns (uint256);\\n\\n    /**\\n     * @notice Get internal version number identifying an ArbOS build\\n     * @return version number as int\\n     */\\n    function arbOSVersion() external view returns (uint256);\\n\\n    /**\\n     * @notice Returns 0 since Nitro has no concept of storage gas\\n     * @return uint 0\\n     */\\n    function getStorageGasAvailable() external view returns (uint256);\\n\\n    /**\\n     * @notice (deprecated) check if current call is top level (meaning it was triggered by an EoA or a L1 contract)\\n     * @dev this call has been deprecated and may be removed in a future release\\n     * @return true if current execution frame is not a call by another L2 contract\\n     */\\n    function isTopLevelCall() external view returns (bool);\\n\\n    /**\\n     * @notice map L1 sender contract address to its L2 alias\\n     * @param sender sender address\\n     * @param unused argument no longer used\\n     * @return aliased sender address\\n     */\\n    function mapL1SenderContractAddressToL2Alias(address sender, address unused)\\n        external\\n        pure\\n        returns (address);\\n\\n    /**\\n     * @notice check if the caller (of this caller of this) is an aliased L1 contract address\\n     * @return true iff the caller's address is an alias for an L1 contract address\\n     */\\n    function wasMyCallersAddressAliased() external view returns (bool);\\n\\n    /**\\n     * @notice return the address of the caller (of this caller of this), without applying L1 contract address aliasing\\n     * @return address of the caller's caller, without applying L1 contract address aliasing\\n     */\\n    function myCallersAddressWithoutAliasing() external view returns (address);\\n\\n    /**\\n     * @notice Send given amount of Eth to dest from sender.\\n     * This is a convenience function, which is equivalent to calling sendTxToL1 with empty data.\\n     * @param destination recipient address on L1\\n     * @return unique identifier for this L2-to-L1 transaction.\\n     */\\n    function withdrawEth(address destination) external payable returns (uint256);\\n\\n    /**\\n     * @notice Send a transaction to L1\\n     * @dev it is not possible to execute on the L1 any L2-to-L1 transaction which contains data\\n     * to a contract address without any code (as enforced by the Bridge contract).\\n     * @param destination recipient address on L1\\n     * @param data (optional) calldata for L1 contract call\\n     * @return a unique identifier for this L2-to-L1 transaction.\\n     */\\n    function sendTxToL1(address destination, bytes calldata data)\\n        external\\n        payable\\n        returns (uint256);\\n\\n    /**\\n     * @notice Get send Merkle tree state\\n     * @return size number of sends in the history\\n     * @return root root hash of the send history\\n     * @return partials hashes of partial subtrees in the send history tree\\n     */\\n    function sendMerkleTreeState()\\n        external\\n        view\\n        returns (\\n            uint256 size,\\n            bytes32 root,\\n            bytes32[] memory partials\\n        );\\n\\n    /**\\n     * @notice creates a send txn from L2 to L1\\n     * @param position = (level << 192) + leaf = (0 << 192) + leaf = leaf\\n     */\\n    event L2ToL1Tx(\\n        address caller,\\n        address indexed destination,\\n        uint256 indexed hash,\\n        uint256 indexed position,\\n        uint256 arbBlockNum,\\n        uint256 ethBlockNum,\\n        uint256 timestamp,\\n        uint256 callvalue,\\n        bytes data\\n    );\\n\\n    /// @dev DEPRECATED in favour of the new L2ToL1Tx event above after the nitro upgrade\\n    event L2ToL1Transaction(\\n        address caller,\\n        address indexed destination,\\n        uint256 indexed uniqueId,\\n        uint256 indexed batchNumber,\\n        uint256 indexInBatch,\\n        uint256 arbBlockNum,\\n        uint256 ethBlockNum,\\n        uint256 timestamp,\\n        uint256 callvalue,\\n        bytes data\\n    );\\n\\n    /**\\n     * @notice logs a merkle branch for proof synthesis\\n     * @param reserved an index meant only to align the 4th index with L2ToL1Transaction's 4th event\\n     * @param hash the merkle hash\\n     * @param position = (level << 192) + leaf\\n     */\\n    event SendMerkleUpdate(\\n        uint256 indexed reserved,\\n        bytes32 indexed hash,\\n        uint256 indexed position\\n    );\\n\\n    error InvalidBlockNumber(uint256 requested, uint256 current);\\n}\\n\",\"keccak256\":\"0x4bc9f91d6a3973af46cc95dd5fe9a29d23e2e64b13512dd0b2d1bfcde705f132\",\"license\":\"BUSL-1.1\"},\"src/RealitioHomeProxyArb.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n *  @authors: [@unknownunknown1]\\r\\n *  @reviewers: []\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\nimport \\\"./interfaces/RealitioInterface.sol\\\";\\r\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./interfaces/ArbitrationProxyInterfaces.sol\\\";\\r\\nimport \\\"@arbitrum/nitro-contracts/src/precompiles/ArbSys.sol\\\";\\r\\nimport \\\"@arbitrum/nitro-contracts/src/libraries/AddressAliasHelper.sol\\\";\\r\\n\\r\\n/**\\r\\n * @title Arbitration proxy for Realitio on Arbitrum.\\r\\n * @dev This contract is meant to be deployed to L2 where Reality.eth is deployed.\\r\\n * https://docs.arbitrum.io/arbos/l2-to-l1-messaging\\r\\n * Example https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/arbitrum/GreeterL2.sol\\r\\n */\\r\\ncontract RealitioHomeProxyArb is IHomeArbitrationProxy {\\r\\n    // Precompiled contract for L2 -> L1 communication\\r\\n    // https://docs.arbitrum.io/build-decentralized-apps/precompiles/reference#arbsys\\r\\n    ArbSys constant ARB_SYS = ArbSys(address(100));\\r\\n\\r\\n    /// @dev The address of the Realitio contract (v3.0 required). TRUSTED.\\r\\n    RealitioInterface public immutable realitio;\\r\\n    address public immutable foreignProxy; // Address of the proxy on L1.\\r\\n    /// @dev ID of the foreign chain, required for Realitio.\\r\\n    bytes32 public immutable foreignChainId;\\r\\n\\r\\n    /// @dev Metadata for Realitio interface.\\r\\n    string public override metadata;\\r\\n\\r\\n    enum Status {\\r\\n        None,\\r\\n        Rejected,\\r\\n        Notified,\\r\\n        AwaitingRuling,\\r\\n        Ruled,\\r\\n        Finished\\r\\n    }\\r\\n\\r\\n    struct Request {\\r\\n        Status status;\\r\\n        bytes32 arbitratorAnswer;\\r\\n    }\\r\\n\\r\\n    /// @dev Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]\\r\\n    mapping(bytes32 => mapping(address => Request)) public requests;\\r\\n\\r\\n    /// @dev Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]\\r\\n    mapping(bytes32 => address) public questionIDToRequester;\\r\\n\\r\\n    event L2ToL1TxCreated(uint256 indexed withdrawalId);\\r\\n\\r\\n    /// @dev Foreign proxy uses its alias to make calls on L2.\\r\\n    modifier onlyForeignProxyAlias() virtual {\\r\\n        require(msg.sender == AddressAliasHelper.applyL1ToL2Alias(foreignProxy), \\\"Can only be called by foreign proxy\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Creates an arbitration proxy on the home chain.\\r\\n     * @param _realitio Realitio contract address.\\r\\n     * @param _foreignChainId The ID of foreign chain (Goerli/Mainnet).\\r\\n     * @param _foreignProxy Address of the proxy on L1.\\r\\n     * @param _metadata Metadata for Realitio.\\r\\n     */\\r\\n    constructor(\\r\\n        RealitioInterface _realitio,\\r\\n        uint256 _foreignChainId,\\r\\n        address _foreignProxy,\\r\\n        string memory _metadata\\r\\n    ) {\\r\\n        realitio = _realitio;\\r\\n        foreignChainId = bytes32(_foreignChainId);\\r\\n        foreignProxy = _foreignProxy;\\r\\n        metadata = _metadata;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    function receiveArbitrationRequest(\\r\\n        bytes32 _questionID,\\r\\n        address _requester,\\r\\n        uint256 _maxPrevious\\r\\n    ) external override onlyForeignProxyAlias {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.None, \\\"Request already exists\\\");\\r\\n\\r\\n        try realitio.notifyOfArbitrationRequest(_questionID, _requester, _maxPrevious) {\\r\\n            request.status = Status.Notified;\\r\\n            questionIDToRequester[_questionID] = _requester;\\r\\n\\r\\n            emit RequestNotified(_questionID, _requester, _maxPrevious);\\r\\n        } catch Error(string memory reason) {\\r\\n            /*\\r\\n             * Will fail if:\\r\\n             *  - The question does not exist.\\r\\n             *  - The question was not answered yet.\\r\\n             *  - Another request was already accepted.\\r\\n             *  - Someone increased the bond on the question to a value > _maxPrevious\\r\\n             */\\r\\n            request.status = Status.Rejected;\\r\\n\\r\\n            emit RequestRejected(_questionID, _requester, _maxPrevious, reason);\\r\\n        } catch {\\r\\n            // In case `reject` did not have a reason string or some other error happened\\r\\n            request.status = Status.Rejected;\\r\\n\\r\\n            emit RequestRejected(_questionID, _requester, _maxPrevious, \\\"\\\");\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @dev Relays arbitration request back to L1 after it has been notified by Realitio for a given question.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external override {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.Notified, \\\"Invalid request status\\\");\\r\\n\\r\\n        request.status = Status.AwaitingRuling;\\r\\n\\r\\n        bytes4 selector = IForeignArbitrationProxy.receiveArbitrationAcknowledgement.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(selector, _questionID, _requester);\\r\\n        sendToL1(data);\\r\\n        emit RequestAcknowledged(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @dev Relays arbitration request back to L1 after it has been rejected.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external override {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.Rejected, \\\"Invalid request status\\\");\\r\\n\\r\\n        // At this point, only the request.status is set, simply reseting the status to Status.None is enough.\\r\\n        request.status = Status.None;\\r\\n\\r\\n        bytes4 selector = IForeignArbitrationProxy.receiveArbitrationCancelation.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(selector, _questionID, _requester);\\r\\n        sendToL1(data);\\r\\n        emit RequestCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the user that requested arbitration.\\r\\n     */\\r\\n    function receiveArbitrationFailure(\\r\\n        bytes32 _questionID,\\r\\n        address _requester\\r\\n    ) external override onlyForeignProxyAlias {\\r\\n        Request storage request = requests[_questionID][_requester];\\r\\n        require(request.status == Status.AwaitingRuling, \\\"Invalid request status\\\");\\r\\n\\r\\n        // At this point, only the request.status is set, simply reseting the status to Status.None is enough.\\r\\n        request.status = Status.None;\\r\\n\\r\\n        realitio.cancelArbitration(_questionID);\\r\\n\\r\\n        emit ArbitrationFailed(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives an answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external override onlyForeignProxyAlias {\\r\\n        address requester = questionIDToRequester[_questionID];\\r\\n        Request storage request = requests[_questionID][requester];\\r\\n        require(request.status == Status.AwaitingRuling, \\\"Invalid request status\\\");\\r\\n\\r\\n        request.status = Status.Ruled;\\r\\n        request.arbitratorAnswer = _answer;\\r\\n\\r\\n        emit ArbitratorAnswered(_questionID, _answer);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Reports the answer provided by the arbitrator to a specified question.\\r\\n     * @dev The Realitio contract validates the input parameters passed to this method,\\r\\n     * so making this publicly accessible is safe.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _lastHistoryHash The history hash given with the last answer to the question in the Realitio contract.\\r\\n     * @param _lastAnswerOrCommitmentID The last answer given, or its commitment ID if it was a commitment,\\r\\n     * to the question in the Realitio contract.\\r\\n     * @param _lastAnswerer The last answerer to the question in the Realitio contract.\\r\\n     */\\r\\n    function reportArbitrationAnswer(\\r\\n        bytes32 _questionID,\\r\\n        bytes32 _lastHistoryHash,\\r\\n        bytes32 _lastAnswerOrCommitmentID,\\r\\n        address _lastAnswerer\\r\\n    ) external {\\r\\n        address requester = questionIDToRequester[_questionID];\\r\\n        Request storage request = requests[_questionID][requester];\\r\\n        require(request.status == Status.Ruled, \\\"Arbitrator has not ruled yet\\\");\\r\\n\\r\\n        realitio.assignWinnerAndSubmitAnswerByArbitrator(\\r\\n            _questionID,\\r\\n            request.arbitratorAnswer,\\r\\n            requester,\\r\\n            _lastHistoryHash,\\r\\n            _lastAnswerOrCommitmentID,\\r\\n            _lastAnswerer\\r\\n        );\\r\\n\\r\\n        request.status = Status.Finished;\\r\\n\\r\\n        emit ArbitrationFinished(_questionID);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Sends a message to L1.\\r\\n     * @param _data The data sent.\\r\\n     */\\r\\n    function sendToL1(bytes memory _data) internal virtual {\\r\\n        uint256 withdrawalId = ARB_SYS.sendTxToL1(foreignProxy, _data);\\r\\n        emit L2ToL1TxCreated(withdrawalId);\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x50bd3e614127c3313f97a75dc2e1bfe21ce6f357005c3e041e9b2ec756795fbc\",\"license\":\"MIT\"},\"src/interfaces/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity 0.8.25;\\r\\n\\r\\ninterface IHomeArbitrationProxy {\\r\\n    /**\\r\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when arbitration request is rejected.\\r\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\r\\n     * or if the question is already finalized.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\r\\n     * @param _reason The reason why the request was rejected.\\r\\n     */\\r\\n    event RequestRejected(\\r\\n        bytes32 indexed _questionID,\\r\\n        address indexed _requester,\\r\\n        uint256 _maxPrevious,\\r\\n        string _reason\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request is canceled.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\r\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\r\\n     * @param _questionID The ID of the question.\\r\\n     */\\r\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function receiveArbitrationRequest(bytes32 _questionID, address _requester, uint256 _maxPrevious) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\r\\n\\r\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\r\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\r\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\r\\n     *  @dev Template_hashes won't be used by this home proxy. \\r\\n     */\\r\\n    function metadata() external view returns (string calldata);\\r\\n}\\r\\n\\r\\ninterface IForeignArbitrationProxy {\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is requested.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute is created.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _disputeID The ID of the dispute.\\r\\n     */\\r\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute could not be created.\\r\\n     * @dev This will happen if there is an increase in the arbitration fees\\r\\n     * between the time the arbitration is made and the time it is acknowledged.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the ruling is relayed to home proxy.\\r\\n     * @param _questionID The ID of the question with the ruling to relay.\\r\\n     * @param _ruling Ruling converted into Realitio format.\\r\\n     */\\r\\n    event RulingRelayed(bytes32 _questionID, bytes32 _ruling);\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @param _questionID the ID of the question.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\r\\n}\\r\\n\",\"keccak256\":\"0x64699ba74323bd9c3c5cf00d97c4d3004d5dffe60fedd4113d3344a560bf2aee\",\"license\":\"MIT\"},\"src/interfaces/RealitioInterface.sol\":{\"content\":\"/* solhint-disable var-name-mixedcase */\\r\\n// SPDX-License-Identifier: MIT\\r\\n\\r\\n/** Interface of https://github.com/RealityETH/reality-eth-monorepo/blob/main/packages/contracts/flat/RealityETH-3.0.sol.\\r\\n *  @reviewers: [@hbarcelos, @fnanni-0, @nix1g, @unknownunknown1, @ferittuncer, @jaybuidl]\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\ninterface RealitioInterface {\\r\\n    event LogNewAnswer(\\r\\n        bytes32 answer,\\r\\n        bytes32 indexed question_id,\\r\\n        bytes32 history_hash,\\r\\n        address indexed user,\\r\\n        uint256 bond,\\r\\n        uint256 ts,\\r\\n        bool is_commitment\\r\\n    );\\r\\n\\r\\n    event LogNewTemplate(uint256 indexed template_id, address indexed user, string question_text);\\r\\n\\r\\n    event LogNewQuestion(\\r\\n        bytes32 indexed question_id,\\r\\n        address indexed user,\\r\\n        uint256 template_id,\\r\\n        string question,\\r\\n        bytes32 indexed content_hash,\\r\\n        address arbitrator,\\r\\n        uint32 timeout,\\r\\n        uint32 opening_ts,\\r\\n        uint256 nonce,\\r\\n        uint256 created\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @dev The arbitrator contract is trusted to only call this if they've been paid, and tell us who paid them.\\r\\n     * @notice Notify the contract that the arbitrator has been paid for a question, freezing it pending their decision.\\r\\n     * @param question_id The ID of the question.\\r\\n     * @param requester The account that requested arbitration.\\r\\n     * @param max_previous If specified, reverts if a bond higher than this was submitted after you sent your transaction.\\r\\n     */\\r\\n    function notifyOfArbitrationRequest(bytes32 question_id, address requester, uint256 max_previous) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancel a previously-requested arbitration and extend the timeout\\r\\n     * @dev Useful when doing arbitration across chains that can't be requested atomically\\r\\n     * @param question_id The ID of the question\\r\\n     */\\r\\n    function cancelArbitration(bytes32 question_id) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Submit the answer for a question, for use by the arbitrator, working out the appropriate winner based on the last answer details.\\r\\n     * @dev Doesn't require (or allow) a bond.\\r\\n     * @param question_id The ID of the question\\r\\n     * @param answer The answer, encoded into bytes32\\r\\n     * @param payee_if_wrong The account to be credited as winner if the last answer given is wrong, usually the account that paid the arbitrator\\r\\n     * @param last_history_hash The history hash before the final one\\r\\n     * @param last_answer_or_commitment_id The last answer given, or the commitment ID if it was a commitment.\\r\\n     * @param last_answerer The address that supplied the last answer\\r\\n     */\\r\\n    function assignWinnerAndSubmitAnswerByArbitrator(\\r\\n        bytes32 question_id,\\r\\n        bytes32 answer,\\r\\n        address payee_if_wrong,\\r\\n        bytes32 last_history_hash,\\r\\n        bytes32 last_answer_or_commitment_id,\\r\\n        address last_answerer\\r\\n    ) external;\\r\\n}\\r\\n\",\"keccak256\":\"0x5e59c01ac9241276df50bb4ec8be510660ef88e4fe01aeb01da6b6866025e298\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to L2 where Reality.eth is deployed. https://docs.arbitrum.io/arbos/l2-to-l1-messaging Example https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/arbitrum/GreeterL2.sol", "events": {"ArbitrationFailed(bytes32,address)": {"details": "This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "ArbitrationFinished(bytes32)": {"params": {"_questionID": "The ID of the question."}}, "ArbitratorAnswered(bytes32,bytes32)": {"params": {"_answer": "The answer from the arbitrator.", "_questionID": "The ID of the question."}}, "RequestAcknowledged(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "RequestCanceled(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "RequestNotified(bytes32,address,uint256)": {"params": {"_maxPrevious": "The maximum value of the previous bond for the question.", "_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "RequestRejected(bytes32,address,uint256,string)": {"details": "This can happen if the current bond for the question is higher than maxPrevious or if the question is already finalized.", "params": {"_maxPrevious": "The maximum value of the current bond for the question.", "_questionID": "The ID of the question.", "_reason": "The reason why the request was rejected.", "_requester": "The address of the arbitration requester."}}}, "kind": "dev", "methods": {"constructor": {"params": {"_foreignChainId": "The ID of foreign chain (Goerli/Mainnet).", "_foreignProxy": "Address of the proxy on L1.", "_metadata": "Metadata for Realitio.", "_realitio": "Realitio contract address."}}, "handleNotifiedRequest(bytes32,address)": {"details": "Relays arbitration request back to L1 after it has been notified by Realitio for a given question.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "handleRejectedRequest(bytes32,address)": {"details": "Relays arbitration request back to L1 after it has been rejected. Reasons why the request might be rejected:  - The question does not exist  - The question was not answered yet  - The quesiton bond value changed while the arbitration was being requested  - Another request was already accepted", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "receiveArbitrationAnswer(bytes32,bytes32)": {"params": {"_answer": "The answer from the arbitrator.", "_questionID": "The ID of the question."}}, "receiveArbitrationFailure(bytes32,address)": {"details": "Currently this can happen only if the arbitration cost increased.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "receiveArbitrationRequest(bytes32,address,uint256)": {"details": "Receives the requested arbitration for a question. TRUSTED.", "params": {"_maxPrevious": "The maximum value of the previous bond for the question.", "_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "reportArbitrationAnswer(bytes32,bytes32,bytes32,address)": {"details": "The Realitio contract validates the input parameters passed to this method, so making this publicly accessible is safe.", "params": {"_lastAnswerOrCommitmentID": "The last answer given, or its commitment ID if it was a commitment, to the question in the Realitio contract.", "_lastAnswerer": "The last answerer to the question in the Realitio contract.", "_lastHistoryHash": "The history hash given with the last answer to the question in the Realitio contract.", "_questionID": "The ID of the question."}}}, "stateVariables": {"foreignChainId": {"details": "ID of the foreign chain, required for Realitio."}, "metadata": {"details": "Metadata for Realitio interface."}, "questionIDToRequester": {"details": "Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]"}, "realitio": {"details": "The address of the Realitio contract (v3.0 required). TRUSTED."}, "requests": {"details": "Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]"}}, "title": "Arbitration proxy for Realitio on Arbitrum.", "version": 1}, "userdoc": {"events": {"ArbitrationFailed(bytes32,address)": {"notice": "To be emitted when the dispute could not be created on the Foreign Chain."}, "ArbitrationFinished(bytes32)": {"notice": "To be emitted when reporting the arbitrator answer to <PERSON><PERSON><PERSON>."}, "ArbitratorAnswered(bytes32,bytes32)": {"notice": "To be emitted when receiving the answer from the arbitrator."}, "RequestAcknowledged(bytes32,address)": {"notice": "To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain."}, "RequestCanceled(bytes32,address)": {"notice": "To be emitted when the arbitration request is canceled."}, "RequestNotified(bytes32,address,uint256)": {"notice": "To be emitted when the Realitio contract has been notified of an arbitration request."}, "RequestRejected(bytes32,address,uint256,string)": {"notice": "To be emitted when arbitration request is rejected."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the home chain."}, "receiveArbitrationAnswer(bytes32,bytes32)": {"notice": "Receives an answer to a specified question. TRUSTED."}, "receiveArbitrationFailure(bytes32,address)": {"notice": "Receives a failed attempt to request arbitration. TRUSTED."}, "reportArbitrationAnswer(bytes32,bytes32,bytes32,address)": {"notice": "Reports the answer provided by the arbitrator to a specified question."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 2826, "contract": "src/RealitioHomeProxyArb.sol:RealitioHomeProxyArb", "label": "metadata", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 2847, "contract": "src/RealitioHomeProxyArb.sol:RealitioHomeProxyArb", "label": "requests", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_mapping(t_address,t_struct(Request)2839_storage))"}, {"astId": 2852, "contract": "src/RealitioHomeProxyArb.sol:RealitioHomeProxyArb", "label": "questionIDToRequester", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_address)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_enum(Status)2833": {"encoding": "inplace", "label": "enum RealitioHomeProxyArb.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_struct(Request)2839_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioHomeProxyArb.Request)", "numberOfBytes": "32", "value": "t_struct(Request)2839_storage"}, "t_mapping(t_bytes32,t_address)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_bytes32,t_mapping(t_address,t_struct(Request)2839_storage))": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => mapping(address => struct RealitioHomeProxyArb.Request))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(Request)2839_storage)"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Request)2839_storage": {"encoding": "inplace", "label": "struct RealitioHomeProxyArb.Request", "members": [{"astId": 2836, "contract": "src/RealitioHomeProxyArb.sol:RealitioHomeProxyArb", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)2833"}, {"astId": 2838, "contract": "src/RealitioHomeProxyArb.sol:RealitioHomeProxyArb", "label": "arbitratorAnswer", "offset": 0, "slot": "1", "type": "t_bytes32"}], "numberOfBytes": "64"}}}}
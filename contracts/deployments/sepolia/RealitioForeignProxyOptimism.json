{"address": "0x6a41AF8FC7f68bdd13B2c7D50824Ed49155DC3bA", "abi": [{"inputs": [{"internalType": "address", "name": "_messenger", "type": "address"}, {"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "address", "name": "_governor", "type": "address"}, {"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "uint256[3]", "name": "_multipliers", "type": "uint256[3]"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "Contribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "RulingFunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_ruling", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "MULTIPLIER_DIVISOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFUSE_TO_ARBITRATE_REALITIO", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationCreatedBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignProxyRedStone.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}, {"internalType": "uint256", "name": "disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "answer", "type": "uint256"}, {"internalType": "contract IArbitrator", "name": "arbitrator", "type": "address"}, {"internalType": "bytes", "name": "arbitratorExtraData", "type": "bytes"}, {"internalType": "uint256", "name": "metaEvidenceID", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitratorDisputeIDToDisputeDetails", "outputs": [{"internalType": "uint256", "name": "arbitrationID", "type": "uint256"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}], "name": "changeArbitrator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_governor", "type": "address"}], "name": "changeGovernor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_loserAppealPeriodMultiplier", "type": "uint256"}], "name": "changeLoserAppealPeriodMultiplier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_loserMultiplier", "type": "uint256"}], "name": "changeLoserMultiplier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_messenger", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_metaEvidence", "type": "string"}], "name": "changeMetaevidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_minGasLimit", "type": "uint32"}], "name": "changeMinGasLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_winnerMultiplier", "type": "uint256"}], "name": "changeWinnerMultiplier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_externalDisputeID", "type": "uint256"}], "name": "externalIDtoLocalID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "fundAppeal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "address", "name": "_contributor", "type": "address"}], "name": "getContributionsToSuccessfulFundings", "outputs": [{"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "contributions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "getFundingStatus", "outputs": [{"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "bool", "name": "fullyFunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMultipliers", "outputs": [{"internalType": "uint256", "name": "winner", "type": "uint256"}, {"internalType": "uint256", "name": "loser", "type": "uint256"}, {"internalType": "uint256", "name": "loserAppealPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "divisor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}], "name": "getNumberOfRounds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256[]", "name": "paidFees", "type": "uint256[]"}, {"internalType": "uint256", "name": "feeRewards", "type": "uint256"}, {"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "getTotalWithdrawableAmount", "outputs": [{"internalType": "uint256", "name": "sum", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserAppealPeriodMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "messenger", "outputs": [{"internalType": "contract ICrossDomainMessenger", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "metaEvidenceUpdates", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minGasLimit", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "numberOfRulingOptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "questionIDToArbitrationID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "relayRule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "string", "name": "_evidenceURI", "type": "string"}], "name": "submitEvidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "winner<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "withdrawFeesAndRewards", "outputs": [{"internalType": "uint256", "name": "reward", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "withdrawFeesAndRewardsForAllRounds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0xc5a2c20cfc8d1f5ed164213769d66d302e7b184576cfac1e3bd0234c078c4557", "receipt": {"to": null, "from": "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "contractAddress": "0x6a41AF8FC7f68bdd13B2c7D50824Ed49155DC3bA", "transactionIndex": 25, "gasUsed": "3334226", "logsBloom": "0x20000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000400000000000020000000000000000000800000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x0a2644f37eff3cd9d3552db8f3e34e4e02b224e93fdbd49231ca29519d69e1cb", "transactionHash": "0xc5a2c20cfc8d1f5ed164213769d66d302e7b184576cfac1e3bd0234c078c4557", "logs": [{"transactionIndex": 25, "blockNumber": 7049959, "transactionHash": "0xc5a2c20cfc8d1f5ed164213769d66d302e7b184576cfac1e3bd0234c078c4557", "address": "0x6a41AF8FC7f68bdd13B2c7D50824Ed49155DC3bA", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000352f697066732f516d596a3950527444563448704e4b584a624a38416159763546426b6e4e75536f346b6a4832726148583437654d2f0000000000000000000000", "logIndex": 40, "blockHash": "0x0a2644f37eff3cd9d3552db8f3e34e4e02b224e93fdbd49231ca29519d69e1cb"}], "blockNumber": 7049959, "cumulativeGasUsed": "7961806", "status": 1, "byzantium": true}, "args": ["0x58Cc85b8D04EA49cC6DBd3CbFFd00B4B8D6cb3ef", "0xFe0eb5fC686f929Eb26D541D75Bb59F816c0Aa68", "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "0x90992fb4E15ce0C59aEFfb376460Fda4Ee19C879", "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "/ipfs/QmYj9PRtDV4HpNKXJbJ8AaYv5FBknNuSo4kjH2raHX47eM/", [3000, 7000, 5000]], "numDeployments": 5, "solcInputHash": "26185f45a9b13c653f18d6f7af211215", "metadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_messenger\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_homeProxy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_governor\",\"type\":\"address\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"uint256[3]\",\"name\":\"_multipliers\",\"type\":\"uint256[3]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Contribution\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"RulingFunded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_ruling\",\"type\":\"bytes32\"}],\"name\":\"RulingRelayed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_reward\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"MULTIPLIER_DIVISOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REFUSE_TO_ARBITRATE_REALITIO\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationCreatedBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignProxyRedStone.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"},{\"internalType\":\"uint256\",\"name\":\"disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"answer\",\"type\":\"uint256\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"metaEvidenceID\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitratorDisputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"}],\"name\":\"changeArbitrator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_governor\",\"type\":\"address\"}],\"name\":\"changeGovernor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_loserAppealPeriodMultiplier\",\"type\":\"uint256\"}],\"name\":\"changeLoserAppealPeriodMultiplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_loserMultiplier\",\"type\":\"uint256\"}],\"name\":\"changeLoserMultiplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_messenger\",\"type\":\"address\"}],\"name\":\"changeMessenger\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"}],\"name\":\"changeMetaevidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_minGasLimit\",\"type\":\"uint32\"}],\"name\":\"changeMinGasLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_winnerMultiplier\",\"type\":\"uint256\"}],\"name\":\"changeWinnerMultiplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_externalDisputeID\",\"type\":\"uint256\"}],\"name\":\"externalIDtoLocalID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"fundAppeal\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"}],\"name\":\"getContributionsToSuccessfulFundings\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"contributions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"getFundingStatus\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"fullyFunded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMultipliers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"winner\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loser\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loserAppealPeriod\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"divisor\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"}],\"name\":\"getNumberOfRounds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"paidFees\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"feeRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"getTotalWithdrawableAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sum\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserAppealPeriodMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"messenger\",\"outputs\":[{\"internalType\":\"contract ICrossDomainMessenger\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"metaEvidenceUpdates\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minGasLimit\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"numberOfRulingOptions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"questionIDToArbitrationID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"relayRule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_evidenceURI\",\"type\":\"string\"}],\"name\":\"submitEvidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"winnerMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewardsForAllRounds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"https://docs.optimism.io/builders/app-developers/bridging/messaging\",\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationCreated(bytes32,address,uint256)\":{\"params\":{\"_disputeID\":\"The ID of the dispute.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationFailed(bytes32,address)\":{\"details\":\"This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.\",\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationRequested(bytes32,address,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"Contribution(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contribution is made, inside fundAppeal function.\",\"params\":{\"_amount\":\"Contribution amount.\",\"_contributor\":\"Caller of fundAppeal function.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"The round number the contribution was made to.\",\"ruling\":\"Indicates the ruling option which got the contribution.\"}},\"Dispute(address,uint256,uint256,uint256)\":{\"details\":\"To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_evidenceGroupID\":\"Unique identifier of the evidence group that is linked to this dispute.\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Evidence(address,uint256,address,string)\":{\"details\":\"To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_evidence\":\"IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\",\"_evidenceGroupID\":\"Unique identifier of the evidence group the evidence belongs to.\",\"_party\":\"The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\"}},\"MetaEvidence(uint256,string)\":{\"details\":\"To be emitted when meta-evidence is submitted.\",\"params\":{\"_evidence\":\"IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Ruling(address,uint256,uint256)\":{\"details\":\"To be raised when a ruling is given.\",\"params\":{\"_arbitrator\":\"The arbitrator giving the ruling.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_ruling\":\"The ruling which was given.\"}},\"RulingFunded(uint256,uint256,uint256)\":{\"details\":\"To be raised when a ruling option is fully funded for appeal.\",\"params\":{\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"Number of the round this ruling option was fully funded in.\",\"_ruling\":\"The ruling option which just got fully funded.\"}},\"RulingRelayed(bytes32,bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question with the ruling to relay.\",\"_ruling\":\"Ruling converted into Realitio format.\"}},\"Withdrawal(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contributor withdraws non-zero value.\",\"params\":{\"_contributor\":\"The beneficiary of withdrawal.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_reward\":\"Total amount of withdrawal, consists of reimbursed deposits plus rewards.\",\"_round\":\"The round number the withdrawal was made from.\",\"_ruling\":\"Indicates the ruling option which contributor gets rewards from.\"}}},\"kind\":\"dev\",\"methods\":{\"changeArbitrator(address,bytes)\":{\"params\":{\"_arbitrator\":\"New arbitrator address.\",\"_arbitratorExtraData\":\"Extradata for the arbitrator\"}},\"changeGovernor(address)\":{\"params\":{\"_governor\":\"New governor address.\"}},\"changeLoserAppealPeriodMultiplier(uint256)\":{\"params\":{\"_loserAppealPeriodMultiplier\":\"New loser multiplier for appeal perido.\"}},\"changeLoserMultiplier(uint256)\":{\"params\":{\"_loserMultiplier\":\"New loser multiplier.\"}},\"changeMessenger(address)\":{\"params\":{\"_messenger\":\"New MESSENGER address.\"}},\"changeMetaevidence(string)\":{\"params\":{\"_metaEvidence\":\"Metaevidence URI.\"}},\"changeMinGasLimit(uint32)\":{\"params\":{\"_minGasLimit\":\"New minimum gas limit.\"}},\"changeWinnerMultiplier(uint256)\":{\"params\":{\"_winnerMultiplier\":\"New winner multiplier.\"}},\"constructor\":{\"params\":{\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_governor\":\"Governor of the contract.\",\"_homeProxy\":\"Proxy on L2.\",\"_messenger\":\"contract for L1 -> L2 tx\",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_multipliers\":\"Appeal multipliers:  - Multiplier for calculating the appeal cost of the winning answer.  - Multiplier for calculating the appeal cost of the losing answer.  - Multiplier for calculating the appeal period for the losing answer.\"}},\"externalIDtoLocalID(uint256)\":{\"params\":{\"_externalDisputeID\":\"Dispute id as in arbitrator side.\"},\"returns\":{\"_0\":\"localDisputeID Dispute id as in arbitrable contract.\"}},\"fundAppeal(uint256,uint256)\":{\"params\":{\"_answer\":\"One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.\",\"_arbitrationID\":\"The ID of the arbitration, which is questionID cast into uint256.\"},\"returns\":{\"_0\":\"Whether the answer was fully funded or not.\"}},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_contributor\":\"The address whose contributions to query.\",\"_round\":\"The round to query.\"},\"returns\":{\"contributions\":\"The amount contributed to each funded answer by the contributor.\",\"fundedAnswers\":\"IDs of the answers that are fully funded.\"}},\"getDisputeFee(bytes32)\":{\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"getFundingStatus(uint256,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer choice to get funding status for.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"fullyFunded\":\"Whether the answer is fully funded or not.\",\"raised\":\"The amount paid for this answer.\"}},\"getMultipliers()\":{\"returns\":{\"divisor\":\"Multiplier divisor.\",\"loser\":\"Losers stake multiplier.\",\"loserAppealPeriod\":\"Multiplier for calculating an appeal period duration for the losing side.\",\"winner\":\"Winners stake multiplier.\"}},\"getNumberOfRounds(uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\"},\"returns\":{\"_0\":\"The number of rounds.\"}},\"getRoundInfo(uint256,uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"feeRewards\":\"The amount of fees that will be used as rewards.\",\"fundedAnswers\":\"IDs of fully funded answers.\",\"paidFees\":\"The amount of fees paid for each fully funded answer.\"}},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The contributor for which to query.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"},\"returns\":{\"sum\":\"The total amount available to withdraw.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"numberOfRulingOptions(uint256)\":{\"returns\":{\"_0\":\"count The number of ruling options.\"}},\"questionIDToArbitrationID(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"},\"returns\":{\"_0\":\"The ID of the arbitration.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"relayRule(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}},\"submitEvidence(uint256,string)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\",\"_evidenceURI\":\"Link to evidence.\"}},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer to query the reward from.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address to send reward to.\",\"_round\":\"The round from which to withdraw.\"},\"returns\":{\"reward\":\"The withdrawn amount.\"}},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address that made contributions.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"}}},\"title\":\"Arbitration proxy for Realitio on foreign chain (eg. mainnet).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"},\"RulingRelayed(bytes32,bytes32)\":{\"notice\":\"Should be emitted when the ruling is relayed to home proxy.\"}},\"kind\":\"user\",\"methods\":{\"changeArbitrator(address,bytes)\":{\"notice\":\"Changes the arbitrator and extradata. The arbitrator is trusted to support appeal period and not reenter. Note avoid changing arbitrator if there is an active arbitration request in Requested phase, otherwise evidence submitted during this phase will be submitted to the new arbitrator, while arbitration request will be processed by the old one.\"},\"changeGovernor(address)\":{\"notice\":\"Changes the governor of the contract.\"},\"changeLoserAppealPeriodMultiplier(uint256)\":{\"notice\":\"Changes loser multiplier for appeal period.\"},\"changeLoserMultiplier(uint256)\":{\"notice\":\"Changes loser multiplier value.\"},\"changeMessenger(address)\":{\"notice\":\"Changes the L1 -> L2 MESSENGER contract.\"},\"changeMetaevidence(string)\":{\"notice\":\"Updates the meta evidence used for disputes.\"},\"changeMinGasLimit(uint32)\":{\"notice\":\"Changes minimum gas limit for L1 -> L2 tx.\"},\"changeWinnerMultiplier(uint256)\":{\"notice\":\"Changes winner multiplier value.\"},\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain (L1).\"},\"externalIDtoLocalID(uint256)\":{\"notice\":\"Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\"},\"fundAppeal(uint256,uint256)\":{\"notice\":\"Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\"},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"notice\":\"Gets contributions to the answers that are fully funded.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"getFundingStatus(uint256,uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question for a specific answer choice.\"},\"getMultipliers()\":{\"notice\":\"Returns stake multipliers.\"},\"getNumberOfRounds(uint256)\":{\"notice\":\"Gets the number of rounds of the specific question.\"},\"getRoundInfo(uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question.\"},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"notice\":\"Returns the sum of withdrawable amount.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover RedStone fees.\"},\"numberOfRulingOptions(uint256)\":{\"notice\":\"Returns number of possible ruling options. Valid rulings are [0, return value].\"},\"questionIDToArbitrationID(bytes32)\":{\"notice\":\"Casts question ID into uint256 thus returning the related arbitration ID.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"relayRule(bytes32,address)\":{\"notice\":\"Relays the ruling to home proxy. Requires a small deposit to cover RedStone fees.\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute. Can only be called by the arbitrator.\"},\"submitEvidence(uint256,string)\":{\"notice\":\"Allows to submit evidence for a particular question.\"},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"notice\":\"Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\"},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"notice\":\"Allows to withdraw any rewards or reimbursable fees for all rounds at once.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioForeignProxyRedStone.sol\":\"RealitioForeignProxyRedStone\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/dispute-resolver-interface-contract/contracts/IDisputeResolver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@ferittuncer]\\n *  @reviewers: [@mtsalenc*, @hbarcelos*, @unknownunknown1, @MerlinEgalite, @fnanni-0*, @shalzz]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\n\\n/**\\n *  @title This serves as a standard interface for crowdfunded appeals and evidence submission, which aren't a part of the arbitration (erc-792 and erc-1497) standard yet.\\n    This interface is used in Dispute Resolver (resolve.kleros.io).\\n */\\nabstract contract IDisputeResolver is IArbitrable, IEvidence {\\n    string public constant VERSION = \\\"2.0.0\\\"; // Can be used to distinguish between multiple deployed versions, if necessary.\\n\\n    /** @dev Raised when a contribution is made, inside fundAppeal function.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the contribution was made to.\\n     *  @param ruling Indicates the ruling option which got the contribution.\\n     *  @param _contributor Caller of fundAppeal function.\\n     *  @param _amount Contribution amount.\\n     */\\n    event Contribution(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 ruling, address indexed _contributor, uint256 _amount);\\n\\n    /** @dev Raised when a contributor withdraws non-zero value.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the withdrawal was made from.\\n     *  @param _ruling Indicates the ruling option which contributor gets rewards from.\\n     *  @param _contributor The beneficiary of withdrawal.\\n     *  @param _reward Total amount of withdrawal, consists of reimbursed deposits plus rewards.\\n     */\\n    event Withdrawal(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 _ruling, address indexed _contributor, uint256 _reward);\\n\\n    /** @dev To be raised when a ruling option is fully funded for appeal.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round Number of the round this ruling option was fully funded in.\\n     *  @param _ruling The ruling option which just got fully funded.\\n     */\\n    event RulingFunded(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 indexed _ruling);\\n\\n    /** @dev Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     *  @param _externalDisputeID Dispute id as in arbitrator contract.\\n     *  @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external virtual returns (uint256 localDisputeID);\\n\\n    /** @dev Returns number of possible ruling options. Valid rulings are [0, return value].\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 _localDisputeID) external view virtual returns (uint256 count);\\n\\n    /** @dev Allows to submit evidence for a given dispute.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _evidenceURI IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    function submitEvidence(uint256 _localDisputeID, string calldata _evidenceURI) external virtual;\\n\\n    /** @dev Manages contributions and calls appeal function of the specified arbitrator to appeal a dispute. This function lets appeals be crowdfunded.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _ruling The ruling option to which the caller wants to contribute.\\n     *  @return fullyFunded True if the ruling option got fully funded as a result of this contribution.\\n     */\\n    function fundAppeal(uint256 _localDisputeID, uint256 _ruling) external payable virtual returns (bool fullyFunded);\\n\\n    /** @dev Returns appeal multipliers.\\n     *  @return winnerStakeMultiplier Winners stake multiplier.\\n     *  @return loserStakeMultiplier Losers stake multiplier.\\n     *  @return loserAppealPeriodMultiplier Losers appeal period multiplier. The loser is given less time to fund its appeal to defend against last minute appeal funding attacks.\\n     *  @return denominator Multiplier denominator in basis points.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        virtual\\n        returns (\\n            uint256 winnerStakeMultiplier,\\n            uint256 loserStakeMultiplier,\\n            uint256 loserAppealPeriodMultiplier,\\n            uint256 denominator\\n        );\\n\\n    /** @dev Allows to withdraw any reimbursable fees or rewards after the dispute gets resolved.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _round Number of the round that caller wants to execute withdraw on.\\n     *  @param _ruling A ruling option that caller wants to execute withdraw on.\\n     *  @return sum The amount that is going to be transferred to contributor as a result of this function call.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _round,\\n        uint256 _ruling\\n    ) external virtual returns (uint256 sum);\\n\\n    /** @dev Allows to withdraw any rewards or reimbursable fees after the dispute gets resolved for all rounds at once.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to execute withdraw on.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external virtual;\\n\\n    /** @dev Returns the sum of withdrawable amount.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to get withdrawable amount from.\\n     *  @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external view virtual returns (uint256 sum);\\n}\\n\",\"keccak256\":\"0x9174a37ba69e682381a3ae6e14582a17d69f29be879ff27433fce2b971f871ae\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0xf1a2c2d7ec1237ef8d3c5f580ac73f56ed58fe4d023817a188363885b3eeb9f2\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {\\n        Waiting,\\n        Appealable,\\n        Solved\\n    }\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0xfd19582446ef635cfb02a035a18efae3bc242ccf1472bb9949cad3d291306333\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0xf9f105a2cbf5e34cdc5ce71d877cded1b502437f1cd6d28173898f88542418af\",\"license\":\"MIT\"},\"src/RealitioForeignProxyRedStone.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n *  @authors: [@anmol-dhiman]\\r\\n *  @reviewers: []\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\nimport {IDisputeResolver, IArbitrator} from \\\"@kleros/dispute-resolver-interface-contract/contracts/IDisputeResolver.sol\\\";\\r\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./interfaces/ArbitrationProxyInterfaces.sol\\\";\\r\\nimport {ICrossDomainMessenger} from \\\"./interfaces/ICrossDomainMessenger.sol\\\";\\r\\n\\r\\n/**\\r\\n * @title Arbitration proxy for Realitio on foreign chain (eg. mainnet).\\r\\n * @dev https://docs.optimism.io/builders/app-developers/bridging/messaging\\r\\n */\\r\\ncontract RealitioForeignProxyRedStone is IForeignArbitrationProxy, IDisputeResolver {\\r\\n    /* Constants */\\r\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max; // The number of choices for the arbitrator.\\r\\n    uint256 public constant REFUSE_TO_ARBITRATE_REALITIO = type(uint256).max; // Constant that represents \\\"Refuse to rule\\\" in realitio format.\\r\\n    uint256 public constant MULTIPLIER_DIVISOR = 10000; // Divisor parameter for multipliers.\\r\\n\\r\\n    /* Storage */\\r\\n\\r\\n    enum Status {\\r\\n        None,\\r\\n        Requested,\\r\\n        Created,\\r\\n        Ruled,\\r\\n        Relayed,\\r\\n        Failed\\r\\n    }\\r\\n\\r\\n    struct ArbitrationRequest {\\r\\n        Status status; // Status of the arbitration.\\r\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\r\\n        uint256 disputeID; // The ID of the dispute in arbitrator contract.\\r\\n        uint256 answer; // The answer given by the arbitrator.\\r\\n        Round[] rounds; // Tracks each appeal round of a dispute.\\r\\n        IArbitrator arbitrator; // The arbitrator trusted to solve disputes for this request.\\r\\n        bytes arbitratorExtraData; // The extra data for the trusted arbitrator of this request.\\r\\n        uint256 metaEvidenceID; // The meta evidence to be used in a dispute for this case.\\r\\n    }\\r\\n\\r\\n    struct DisputeDetails {\\r\\n        uint256 arbitrationID; // The ID of the arbitration.\\r\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\r\\n    }\\r\\n\\r\\n    // Round struct stores the contributions made to particular answers.\\r\\n    struct Round {\\r\\n        mapping(uint256 => uint256) paidFees; // Tracks the fees paid in this round in the form paidFees[answer].\\r\\n        mapping(uint256 => bool) hasPaid; // True if the fees for this particular answer have been fully paid in the form hasPaid[answer].\\r\\n        mapping(address => mapping(uint256 => uint256)) contributions; // Maps contributors to their contributions for each answer in the form contributions[address][answer].\\r\\n        uint256 feeRewards; // Sum of reimbursable appeal fees available to the parties that made contributions to the answer that ultimately wins a dispute.\\r\\n        uint256[] fundedAnswers; // Stores the answer choices that are fully funded.\\r\\n    }\\r\\n\\r\\n    // contract for L1 -> L2 communication\\r\\n    ICrossDomainMessenger public messenger;\\r\\n    uint32 public minGasLimit = 200000; // Gas limit of the transaction call on L2. Note that setting value too high results in high gas estimation fee (tested on Sepolia).\\r\\n\\r\\n    address public immutable homeProxy; // Proxy on L2.\\r\\n\\r\\n    address public governor; // Governor of the contract (e.g KlerosGovernor).\\r\\n    IArbitrator public arbitrator; // The address of the arbitrator. TRUSTED.\\r\\n    bytes public arbitratorExtraData; // The extra data used to raise a dispute in the arbitrator.\\r\\n    uint256 public metaEvidenceUpdates; // The number of times the meta evidence has been updated. Used to track the latest meta evidence ID.\\r\\n\\r\\n    // Multipliers are in basis points.\\r\\n    uint256 public winnerMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that was chosen by the arbitrator in the previous round.\\r\\n    uint256 public loserMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that the arbitrator didn't rule for in the previous round.\\r\\n    uint256 public loserAppealPeriodMultiplier; // Multiplier for calculating the duration of the appeal period for the loser, in basis points.\\r\\n\\r\\n    mapping(uint256 => mapping(address => ArbitrationRequest)) public arbitrationRequests; // Maps arbitration ID to its data. arbitrationRequests[uint(questionID)][requester].\\r\\n    mapping(address => mapping(uint256 => DisputeDetails)) public arbitratorDisputeIDToDisputeDetails; // Maps external dispute ids from a particular arbitrator to local arbitration ID and requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => bool) public arbitrationIDToDisputeExists; // Whether a dispute has already been created for the given arbitration ID or not.\\r\\n\\r\\n    mapping(uint256 => address) public arbitrationIDToRequester; // Maps arbitration ID to the requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => uint256) public arbitrationCreatedBlock; // Block of dispute creation.\\r\\n\\r\\n    modifier onlyHomeProxy() {\\r\\n        require(msg.sender == address(messenger), \\\"NOT_MESSENGER\\\");\\r\\n        require(messenger.xDomainMessageSender() == homeProxy, \\\"Can only be called by Home proxy\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    modifier onlyGovernor() {\\r\\n        require(msg.sender == governor, \\\"The caller must be the governor.\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Creates an arbitration proxy on the foreign chain (L1).\\r\\n     * @param _messenger contract for L1 -> L2 tx\\r\\n     * @param _homeProxy Proxy on L2.\\r\\n     * @param _governor Governor of the contract.\\r\\n     * @param _arbitrator Arbitrator contract address.\\r\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\r\\n     * @param _metaEvidence The URI of the meta evidence file.\\r\\n     * @param _multipliers Appeal multipliers:\\r\\n     *  - Multiplier for calculating the appeal cost of the winning answer.\\r\\n     *  - Multiplier for calculating the appeal cost of the losing answer.\\r\\n     *  - Multiplier for calculating the appeal period for the losing answer.\\r\\n     */\\r\\n    constructor(\\r\\n        address _messenger,\\r\\n        address _homeProxy,\\r\\n        address _governor,\\r\\n        IArbitrator _arbitrator,\\r\\n        bytes memory _arbitratorExtraData,\\r\\n        string memory _metaEvidence,\\r\\n        uint256[3] memory _multipliers\\r\\n    ) {\\r\\n        messenger = ICrossDomainMessenger(_messenger);\\r\\n        homeProxy = _homeProxy;\\r\\n        governor = _governor;\\r\\n        arbitrator = _arbitrator;\\r\\n        arbitratorExtraData = _arbitratorExtraData;\\r\\n        winnerMultiplier = _multipliers[0];\\r\\n        loserMultiplier = _multipliers[1];\\r\\n        loserAppealPeriodMultiplier = _multipliers[2];\\r\\n\\r\\n        emit MetaEvidence(metaEvidenceUpdates, _metaEvidence);\\r\\n    }\\r\\n\\r\\n    // ********************************* //\\r\\n    // *    Governor Functions    * //\\r\\n    // ********************************* //\\r\\n    /**\\r\\n     * @notice Changes the L1 -> L2 MESSENGER contract.\\r\\n     * @param _messenger New MESSENGER address.\\r\\n     */\\r\\n    function changeMessenger(address _messenger) external onlyGovernor {\\r\\n        messenger = ICrossDomainMessenger(_messenger);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes minimum gas limit for L1 -> L2 tx.\\r\\n     * @param _minGasLimit New minimum gas limit.\\r\\n     */\\r\\n    function changeMinGasLimit(uint32 _minGasLimit) external onlyGovernor {\\r\\n        minGasLimit = _minGasLimit;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the governor of the contract.\\r\\n     * @param _governor New governor address.\\r\\n     */\\r\\n    function changeGovernor(address _governor) external onlyGovernor {\\r\\n        governor = _governor;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the arbitrator and extradata. The arbitrator is trusted to support appeal period and not reenter.\\r\\n     * Note avoid changing arbitrator if there is an active arbitration request in Requested phase, otherwise evidence submitted during this phase\\r\\n     * will be submitted to the new arbitrator, while arbitration request will be processed by the old one.\\r\\n     * @param _arbitrator New arbitrator address.\\r\\n     * @param _arbitratorExtraData Extradata for the arbitrator\\r\\n     */\\r\\n    function changeArbitrator(IArbitrator _arbitrator, bytes calldata _arbitratorExtraData) external onlyGovernor {\\r\\n        arbitrator = _arbitrator;\\r\\n        arbitratorExtraData = _arbitratorExtraData;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Updates the meta evidence used for disputes.\\r\\n     * @param _metaEvidence Metaevidence URI.\\r\\n     */\\r\\n    function changeMetaevidence(string memory _metaEvidence) external onlyGovernor {\\r\\n        metaEvidenceUpdates++;\\r\\n        emit MetaEvidence(metaEvidenceUpdates, _metaEvidence);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes winner multiplier value.\\r\\n     * @param _winnerMultiplier New winner multiplier.\\r\\n     */\\r\\n    function changeWinnerMultiplier(uint256 _winnerMultiplier) external onlyGovernor {\\r\\n        winnerMultiplier = _winnerMultiplier;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes loser multiplier value.\\r\\n     * @param _loserMultiplier New loser multiplier.\\r\\n     */\\r\\n    function changeLoserMultiplier(uint256 _loserMultiplier) external onlyGovernor {\\r\\n        loserMultiplier = _loserMultiplier;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes loser multiplier for appeal period.\\r\\n     * @param _loserAppealPeriodMultiplier New loser multiplier for appeal perido.\\r\\n     */\\r\\n    function changeLoserAppealPeriodMultiplier(uint256 _loserAppealPeriodMultiplier) external onlyGovernor {\\r\\n        loserAppealPeriodMultiplier = _loserAppealPeriodMultiplier;\\r\\n    }\\r\\n\\r\\n    /*//////////////////////////////////////////////////////////////\\r\\n                             REALITIO LOGIC\\r\\n    //////////////////////////////////////////////////////////////*/\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\r\\n        require(!arbitrationIDToDisputeExists[uint256(_questionID)], \\\"Dispute already created\\\");\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[uint256(_questionID)][msg.sender];\\r\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\r\\n\\r\\n        arbitration.arbitrator = arbitrator;\\r\\n        arbitration.arbitratorExtraData = arbitratorExtraData;\\r\\n        arbitration.metaEvidenceID = metaEvidenceUpdates;\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationRequest.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\r\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n\\r\\n        require(msg.value >= arbitrationCost, \\\"Deposit value too low\\\");\\r\\n\\r\\n        arbitration.status = Status.Requested;\\r\\n        arbitration.deposit = uint248(msg.value);\\r\\n\\r\\n        messenger.sendMessage(homeProxy, data, minGasLimit);\\r\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) public override onlyHomeProxy {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n\\r\\n        uint256 arbitrationCost = arbitration.arbitrator.arbitrationCost(arbitration.arbitratorExtraData);\\r\\n        if (arbitration.deposit >= arbitrationCost) {\\r\\n            try\\r\\n                arbitration.arbitrator.createDispute{value: arbitrationCost}(\\r\\n                    NUMBER_OF_CHOICES_FOR_ARBITRATOR,\\r\\n                    arbitration.arbitratorExtraData\\r\\n                )\\r\\n            returns (uint256 disputeID) {\\r\\n                DisputeDetails storage disputeDetails = arbitratorDisputeIDToDisputeDetails[\\r\\n                    address(arbitration.arbitrator)\\r\\n                ][disputeID];\\r\\n                disputeDetails.arbitrationID = arbitrationID;\\r\\n                disputeDetails.requester = _requester;\\r\\n\\r\\n                arbitrationIDToDisputeExists[arbitrationID] = true;\\r\\n                arbitrationIDToRequester[arbitrationID] = _requester;\\r\\n                arbitrationCreatedBlock[disputeID] = block.number;\\r\\n\\r\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\r\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\r\\n\\r\\n                arbitration.status = Status.Created;\\r\\n                arbitration.deposit = 0;\\r\\n                arbitration.disputeID = disputeID;\\r\\n                arbitration.rounds.push();\\r\\n\\r\\n                if (remainder > 0) {\\r\\n                    payable(_requester).send(remainder);\\r\\n                }\\r\\n\\r\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\r\\n                emit Dispute(arbitration.arbitrator, disputeID, arbitration.metaEvidenceID, arbitrationID);\\r\\n            } catch {\\r\\n                arbitration.status = Status.Failed;\\r\\n                emit ArbitrationFailed(_questionID, _requester);\\r\\n            }\\r\\n        } else {\\r\\n            arbitration.status = Status.Failed;\\r\\n            emit ArbitrationFailed(_questionID, _requester);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) public override onlyHomeProxy {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover RedStone fees.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external override {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationFailure.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\r\\n\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        messenger.sendMessage(homeProxy, data, minGasLimit);\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    // ********************************* //\\r\\n    // *    Appeals and arbitration    * //\\r\\n    // ********************************* //\\r\\n\\r\\n    /**\\r\\n     * @notice Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\\r\\n     * @param _arbitrationID The ID of the arbitration, which is questionID cast into uint256.\\r\\n     * @param _answer One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question.\\r\\n     * Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format.\\r\\n     * Also note that '0' answer can be funded.\\r\\n     * @return Whether the answer was fully funded or not.\\r\\n     */\\r\\n    function fundAppeal(uint256 _arbitrationID, uint256 _answer) external payable override returns (bool) {\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][\\r\\n            arbitrationIDToRequester[_arbitrationID]\\r\\n        ];\\r\\n        require(arbitration.status == Status.Created, \\\"No dispute to appeal.\\\");\\r\\n\\r\\n        uint256 disputeID = arbitration.disputeID;\\r\\n        (uint256 appealPeriodStart, uint256 appealPeriodEnd) = arbitration.arbitrator.appealPeriod(disputeID);\\r\\n        require(block.timestamp >= appealPeriodStart && block.timestamp < appealPeriodEnd, \\\"Appeal period is over.\\\");\\r\\n\\r\\n        uint256 multiplier;\\r\\n        {\\r\\n            uint256 winner = arbitration.arbitrator.currentRuling(disputeID);\\r\\n            if (winner == _answer) {\\r\\n                multiplier = winnerMultiplier;\\r\\n            } else {\\r\\n                require(\\r\\n                    block.timestamp - appealPeriodStart <\\r\\n                        ((appealPeriodEnd - appealPeriodStart) * (loserAppealPeriodMultiplier)) / MULTIPLIER_DIVISOR,\\r\\n                    \\\"Appeal period is over for loser\\\"\\r\\n                );\\r\\n                multiplier = loserMultiplier;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        uint256 lastRoundID = arbitration.rounds.length - 1;\\r\\n        Round storage round = arbitration.rounds[lastRoundID];\\r\\n        require(!round.hasPaid[_answer], \\\"Appeal fee is already paid.\\\");\\r\\n        uint256 appealCost = arbitration.arbitrator.appealCost(disputeID, arbitration.arbitratorExtraData);\\r\\n        uint256 totalCost = appealCost + ((appealCost * multiplier) / MULTIPLIER_DIVISOR);\\r\\n\\r\\n        // Take up to the amount necessary to fund the current round at the current costs.\\r\\n        uint256 contribution = totalCost - (round.paidFees[_answer]) > msg.value\\r\\n            ? msg.value\\r\\n            : totalCost - (round.paidFees[_answer]);\\r\\n        emit Contribution(_arbitrationID, lastRoundID, _answer, msg.sender, contribution);\\r\\n\\r\\n        round.contributions[msg.sender][_answer] += contribution;\\r\\n        round.paidFees[_answer] += contribution;\\r\\n        if (round.paidFees[_answer] >= totalCost) {\\r\\n            round.feeRewards += round.paidFees[_answer];\\r\\n            round.fundedAnswers.push(_answer);\\r\\n            round.hasPaid[_answer] = true;\\r\\n            emit RulingFunded(_arbitrationID, lastRoundID, _answer);\\r\\n        }\\r\\n\\r\\n        if (round.fundedAnswers.length > 1) {\\r\\n            // At least two sides are fully funded.\\r\\n            arbitration.rounds.push();\\r\\n\\r\\n            round.feeRewards = round.feeRewards - appealCost;\\r\\n            arbitration.arbitrator.appeal{value: appealCost}(disputeID, arbitration.arbitratorExtraData);\\r\\n        }\\r\\n\\r\\n        if (msg.value - contribution > 0) payable(msg.sender).send(msg.value - contribution); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\r\\n        return round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address to send reward to.\\r\\n     * @param _round The round from which to withdraw.\\r\\n     * @param _answer The answer to query the reward from.\\r\\n     * @return reward The withdrawn amount.\\r\\n     */\\r\\n    function withdrawFeesAndRewards(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) public override returns (uint256 reward) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\r\\n        // Allow to reimburse if funding of the round was unsuccessful.\\r\\n        if (!round.hasPaid[_answer]) {\\r\\n            reward = round.contributions[_beneficiary][_answer];\\r\\n        } else if (!round.hasPaid[arbitration.answer]) {\\r\\n            // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n            // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n            reward = round.fundedAnswers.length > 1\\r\\n                ? (round.contributions[_beneficiary][_answer] * round.feeRewards) /\\r\\n                    (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                : 0;\\r\\n        } else if (arbitration.answer == _answer) {\\r\\n            uint256 paidFees = round.paidFees[_answer];\\r\\n            // Reward the winner.\\r\\n            reward = paidFees > 0 ? (round.contributions[_beneficiary][_answer] * round.feeRewards) / paidFees : 0;\\r\\n        }\\r\\n\\r\\n        if (reward != 0) {\\r\\n            round.contributions[_beneficiary][_answer] = 0;\\r\\n            _beneficiary.send(reward); // It is the user's responsibility to accept ETH.\\r\\n            emit Withdrawal(_arbitrationID, _round, _answer, _beneficiary, reward);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to withdraw any rewards or reimbursable fees for all rounds at once.\\r\\n     * @dev This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.\\r\\n     *      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address that made contributions.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     */\\r\\n    function withdrawFeesAndRewardsForAllRounds(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external override {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n\\r\\n        uint256 numberOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < numberOfRounds; roundNumber++) {\\r\\n            withdrawFeesAndRewards(_arbitrationID, _beneficiary, roundNumber, _contributedTo);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to submit evidence for a particular question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @param _evidenceURI Link to evidence.\\r\\n     */\\r\\n    function submitEvidence(uint256 _arbitrationID, string calldata _evidenceURI) external override {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        if (address(arbitration.arbitrator) == address(0)) {\\r\\n            // None or Requested status.\\r\\n            // Note that arbitrator set during requestArbitration might differ from default arbitrator, if default arbitrator was changed during Requested status.\\r\\n            emit Evidence(arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\r\\n        } else {\\r\\n            emit Evidence(arbitration.arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Rules a specified dispute. Can only be called by the arbitrator.\\r\\n     * @dev Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\\r\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\r\\n     * @param _ruling The ruling given by the arbitrator.\\r\\n     */\\r\\n    function rule(uint256 _disputeID, uint256 _ruling) external override {\\r\\n        DisputeDetails storage disputeDetails = arbitratorDisputeIDToDisputeDetails[msg.sender][_disputeID];\\r\\n        uint256 arbitrationID = disputeDetails.arbitrationID;\\r\\n        address requester = disputeDetails.requester;\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][requester];\\r\\n        require(msg.sender == address(arbitration.arbitrator), \\\"Only arbitrator allowed\\\");\\r\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\r\\n        uint256 finalRuling = _ruling;\\r\\n\\r\\n        // If one side paid its fees, the ruling is in its favor. Note that if the other side had also paid, an appeal would have been created.\\r\\n        Round storage round = arbitration.rounds[arbitration.rounds.length - 1];\\r\\n        if (round.fundedAnswers.length == 1) finalRuling = round.fundedAnswers[0];\\r\\n\\r\\n        arbitration.answer = finalRuling;\\r\\n        arbitration.status = Status.Ruled;\\r\\n        emit Ruling(IArbitrator(msg.sender), _disputeID, finalRuling);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Relays the ruling to home proxy. Requires a small deposit to cover RedStone fees.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function relayRule(bytes32 _questionID, address _requester) external {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\r\\n\\r\\n        // Realitio ruling is shifted by 1 compared to Kleros.\\r\\n        uint256 realitioRuling = arbitration.answer != 0 ? arbitration.answer - 1 : REFUSE_TO_ARBITRATE_REALITIO;\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationAnswer.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, bytes32(realitioRuling));\\r\\n\\r\\n        arbitration.status = Status.Relayed;\\r\\n\\r\\n        messenger.sendMessage(homeProxy, data, minGasLimit);\\r\\n        emit RulingRelayed(_questionID, bytes32(realitioRuling));\\r\\n    }\\r\\n\\r\\n    // ********************************* //\\r\\n    // *    External View Functions    * //\\r\\n    // ********************************* //\\r\\n\\r\\n    /**\\r\\n     * @notice Returns stake multipliers.\\r\\n     * @return winner Winners stake multiplier.\\r\\n     * @return loser Losers stake multiplier.\\r\\n     * @return loserAppealPeriod Multiplier for calculating an appeal period duration for the losing side.\\r\\n     * @return divisor Multiplier divisor.\\r\\n     */\\r\\n    function getMultipliers()\\r\\n        external\\r\\n        view\\r\\n        override\\r\\n        returns (uint256 winner, uint256 loser, uint256 loserAppealPeriod, uint256 divisor)\\r\\n    {\\r\\n        return (winnerMultiplier, loserMultiplier, loserAppealPeriodMultiplier, MULTIPLIER_DIVISOR);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns number of possible ruling options. Valid rulings are [0, return value].\\r\\n     * @return count The number of ruling options.\\r\\n     */\\r\\n    function numberOfRulingOptions(uint256 /* _arbitrationID */) external pure override returns (uint256) {\\r\\n        return NUMBER_OF_CHOICES_FOR_ARBITRATOR;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 /* _questionID */) external view override returns (uint256) {\\r\\n        return arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the number of rounds of the specific question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @return The number of rounds.\\r\\n     */\\r\\n    function getNumberOfRounds(uint256 _arbitrationID) external view returns (uint256) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        return arbitration.rounds.length;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @return paidFees The amount of fees paid for each fully funded answer.\\r\\n     * @return feeRewards The amount of fees that will be used as rewards.\\r\\n     * @return fundedAnswers IDs of fully funded answers.\\r\\n     */\\r\\n    function getRoundInfo(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round\\r\\n    ) external view returns (uint256[] memory paidFees, uint256 feeRewards, uint256[] memory fundedAnswers) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n\\r\\n        paidFees = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < round.fundedAnswers.length; i++) {\\r\\n            paidFees[i] = round.paidFees[round.fundedAnswers[i]];\\r\\n        }\\r\\n\\r\\n        feeRewards = round.feeRewards;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question for a specific answer choice.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _answer The answer choice to get funding status for.\\r\\n     * @return raised The amount paid for this answer.\\r\\n     * @return fullyFunded Whether the answer is fully funded or not.\\r\\n     */\\r\\n    function getFundingStatus(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) external view returns (uint256 raised, bool fullyFunded) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        raised = round.paidFees[_answer];\\r\\n        fullyFunded = round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets contributions to the answers that are fully funded.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _contributor The address whose contributions to query.\\r\\n     * @return fundedAnswers IDs of the answers that are fully funded.\\r\\n     * @return contributions The amount contributed to each funded answer by the contributor.\\r\\n     */\\r\\n    function getContributionsToSuccessfulFundings(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        address _contributor\\r\\n    ) external view returns (uint256[] memory fundedAnswers, uint256[] memory contributions) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n        contributions = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < contributions.length; i++) {\\r\\n            contributions[i] = round.contributions[_contributor][fundedAnswers[i]];\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns the sum of withdrawable amount.\\r\\n     * @dev This function is O(n) where n is the total number of rounds.\\r\\n     * @dev This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The contributor for which to query.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     * @return sum The total amount available to withdraw.\\r\\n     */\\r\\n    function getTotalWithdrawableAmount(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external view override returns (uint256 sum) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        if (arbitration.status < Status.Ruled) return sum;\\r\\n\\r\\n        uint256 finalAnswer = arbitration.answer;\\r\\n        uint256 noOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < noOfRounds; roundNumber++) {\\r\\n            Round storage round = arbitration.rounds[roundNumber];\\r\\n\\r\\n            if (!round.hasPaid[_contributedTo]) {\\r\\n                // Allow to reimburse if funding was unsuccessful for this answer option.\\r\\n                sum += round.contributions[_beneficiary][_contributedTo];\\r\\n            } else if (!round.hasPaid[finalAnswer]) {\\r\\n                // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n                // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n                sum += round.fundedAnswers.length > 1\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) /\\r\\n                        (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                    : 0;\\r\\n            } else if (finalAnswer == _contributedTo) {\\r\\n                uint256 paidFees = round.paidFees[_contributedTo];\\r\\n                // Reward the winner.\\r\\n                sum += paidFees > 0\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) / paidFees\\r\\n                    : 0;\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Casts question ID into uint256 thus returning the related arbitration ID.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @return The ID of the arbitration.\\r\\n     */\\r\\n    function questionIDToArbitrationID(bytes32 _questionID) external pure returns (uint256) {\\r\\n        return uint256(_questionID);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\r\\n     * @param _externalDisputeID Dispute id as in arbitrator side.\\r\\n     * @return localDisputeID Dispute id as in arbitrable contract.\\r\\n     */\\r\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external view override returns (uint256) {\\r\\n        // Note that in case of arbitrator's change external dispute from the new arbitrator\\r\\n        // will overwrite the external dispute with the same ID from the old arbitrator,\\r\\n        // which will make the data related to the old arbitrator's dispute unaccessible in DisputeResolver's UI.\\r\\n        // It should be fine since the dispute will be closed anyway.\\r\\n        // Ideally we would want to have arbitrator's address as one of the parameters, but we can't break the interface.\\r\\n        return arbitratorDisputeIDToDisputeDetails[address(arbitrator)][_externalDisputeID].arbitrationID;\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x1825c6259d3aabad0b600ee97a0d54acc2451ce84ec79965b0b0885d04df3bd6\",\"license\":\"MIT\"},\"src/interfaces/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity 0.8.25;\\r\\n\\r\\ninterface IHomeArbitrationProxy {\\r\\n    /**\\r\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when arbitration request is rejected.\\r\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\r\\n     * or if the question is already finalized.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\r\\n     * @param _reason The reason why the request was rejected.\\r\\n     */\\r\\n    event RequestRejected(\\r\\n        bytes32 indexed _questionID,\\r\\n        address indexed _requester,\\r\\n        uint256 _maxPrevious,\\r\\n        string _reason\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request is canceled.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\r\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\r\\n     * @param _questionID The ID of the question.\\r\\n     */\\r\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function receiveArbitrationRequest(bytes32 _questionID, address _requester, uint256 _maxPrevious) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\r\\n\\r\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\r\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\r\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\r\\n     *  @dev Template_hashes won't be used by this home proxy. \\r\\n     */\\r\\n    function metadata() external view returns (string calldata);\\r\\n}\\r\\n\\r\\ninterface IForeignArbitrationProxy {\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is requested.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute is created.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _disputeID The ID of the dispute.\\r\\n     */\\r\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute could not be created.\\r\\n     * @dev This will happen if there is an increase in the arbitration fees\\r\\n     * between the time the arbitration is made and the time it is acknowledged.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the ruling is relayed to home proxy.\\r\\n     * @param _questionID The ID of the question with the ruling to relay.\\r\\n     * @param _ruling Ruling converted into Realitio format.\\r\\n     */\\r\\n    event RulingRelayed(bytes32 _questionID, bytes32 _ruling);\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external ;\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @param _questionID the ID of the question.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\r\\n}\\r\\n\\r\\n\",\"keccak256\":\"0x2a8477d0330ba364f4711428454c274836b5f6c26700088e544a369faa7f3da8\",\"license\":\"MIT\"},\"src/interfaces/ICrossDomainMessenger.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n// @dev https://github.com/ethereum-optimism/optimism/blob/v1.7.7/packages/contracts-bedrock/src/universal/CrossDomainMessenger.sol\\r\\ninterface ICrossDomainMessenger {\\r\\n    function sendMessage(\\r\\n        address _target,\\r\\n        bytes calldata _message,\\r\\n        uint32 _gasLimit\\r\\n    ) external;\\r\\n\\r\\n    function xDomainMessageSender() external view returns (address);\\r\\n}\\r\\n\",\"keccak256\":\"0xe5f7074540dec5ab778f1c7ac76f217435ca6b7e97e44e8a043a8ec189742e4b\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "0x6080604052600436106102885760003560e01c80638a9bb02a1161015a578063be26fc88116100c1578063e742e2ed1161007a578063e742e2ed14610889578063ec85635b146108a9578063edabc474146108c9578063fc6f8f16146108fc578063fe2dddeb14610949578063ffa1ad741461096957600080fd5b8063be26fc88146107a2578063c21ae061146107c2578063c599c66f14610805578063c94828f014610833578063d661dd3114610853578063e4c0aaf41461086957600080fd5b8063a829c3d111610113578063a829c3d1146106cf578063aba980eb146106e2578063b2a834531461070f578063ba7079ca1461072f578063bb1637821461074f578063bb2fe65f1461076d57600080fd5b80638a9bb02a146106125780638d8b2d7e14610641578063965394ab146106635780639b3ac99814610679578063a22352e21461068f578063a6a7f0eb146106af57600080fd5b80634aa6a1e4116101fe5780636cc6cde1116101b75780636cc6cde11461054d5780636cdc090f1461056d578063756804c01461058357806379873f8a146105b95780637a7104911461033257806384cbc5d4146105f257600080fd5b80634aa6a1e41461041c5780634b2f0ea01461043c5780635082a7991461045f57806350f9c11a1461047f5780635aeb4d77146104e457806368cb30f51461051d57600080fd5b80631355d0a6116102505780631355d0a614610348578063311a6c5614610368578063325df18814610388578063362c3479146103a85780633cb747bf146103c857806348f37857146103e857600080fd5b8063055287151461028d5780630c139eb4146102af5780630c340a24146102d85780630c7ac7b6146103105780630fa8c7ce14610332575b600080fd5b34801561029957600080fd5b506102ad6102a8366004612e30565b61099a565b005b3480156102bb57600080fd5b506102c560075481565b6040519081526020015b60405180910390f35b3480156102e457600080fd5b506001546102f8906001600160a01b031681565b6040516001600160a01b0390911681526020016102cf565b34801561031c57600080fd5b506103256109d2565b6040516102cf9190612e8f565b34801561033e57600080fd5b506102c560001981565b34801561035457600080fd5b506102ad610363366004612ea2565b610a60565b34801561037457600080fd5b506102ad610383366004612ec8565b610ab0565b34801561039457600080fd5b506102ad6103a3366004612e30565b610c3b565b3480156103b457600080fd5b506102c56103c3366004612eff565b610c6a565b3480156103d457600080fd5b506000546102f8906001600160a01b031681565b3480156103f457600080fd5b506102f87f000000000000000000000000000000000000000000000000000000000000000081565b34801561042857600080fd5b506102ad610437366004612f3c565b610f51565b61044f61044a366004612ec8565b61115c565b60405190151581526020016102cf565b34801561046b57600080fd5b506102ad61047a366004612f3c565b6117b5565b34801561048b57600080fd5b506104c761049a366004612f6c565b6009602090815260009283526040808420909152908252902080546001909101546001600160a01b031682565b604080519283526001600160a01b039091166020830152016102cf565b3480156104f057600080fd5b5060005461050890600160a01b900463ffffffff1681565b60405163ffffffff90911681526020016102cf565b34801561052957600080fd5b5061044f610538366004612e30565b600a6020526000908152604090205460ff1681565b34801561055957600080fd5b506002546102f8906001600160a01b031681565b34801561057957600080fd5b506102c560045481565b34801561058f57600080fd5b506102f861059e366004612e30565b600b602052600090815260409020546001600160a01b031681565b3480156105c557600080fd5b506005546006546007546127106040805194855260208501939093529183015260608201526080016102cf565b3480156105fe57600080fd5b506102ad61060d366004612fae565b611985565b34801561061e57600080fd5b5061063261062d366004612ec8565b611a01565b6040516102cf9392919061309b565b34801561064d57600080fd5b506102c561065c366004612e30565b5060001990565b34801561066f57600080fd5b506102c560065481565b34801561068557600080fd5b506102c560055481565b34801561069b57600080fd5b506102c56106aa366004612e30565b611b79565b3480156106bb57600080fd5b506102ad6106ca366004613119565b611bec565b6102ad6106dd366004612ec8565b611cce565b3480156106ee57600080fd5b506102c56106fd366004612e30565b600c6020526000908152604090205481565b34801561071b57600080fd5b506102ad61072a366004613165565b611fd1565b34801561073b57600080fd5b506102ad61074a366004613182565b61201d565b34801561075b57600080fd5b506102c561076a366004612e30565b90565b34801561077957600080fd5b5061078d6107883660046131be565b612075565b604080519283529015156020830152016102cf565b3480156107ae57600080fd5b506102ad6107bd366004612f3c565b6120f9565b3480156107ce57600080fd5b506102c56107dd366004612e30565b6002546001600160a01b03166000908152600960209081526040808320938352929052205490565b34801561081157600080fd5b506108256108203660046131ea565b612360565b6040516102cf929190613223565b34801561083f57600080fd5b506102ad61084e366004612e30565b6124de565b34801561085f57600080fd5b506102c561271081565b34801561087557600080fd5b506102ad610884366004613165565b61250d565b34801561089557600080fd5b506102ad6108a4366004612f3c565b612559565b3480156108b557600080fd5b506102c56108c4366004613251565b6129e6565b3480156108d557600080fd5b506108e96108e4366004612f3c565b612c33565b6040516102cf979695949392919061329f565b34801561090857600080fd5b506102c5610917366004612e30565b6000908152600b6020908152604080832054600883528184206001600160a01b03909116845290915290206003015490565b34801561095557600080fd5b506102ad610964366004613251565b612d18565b34801561097557600080fd5b50610325604051806040016040528060058152602001640322e302e360dc1b81525081565b6001546001600160a01b031633146109cd5760405162461bcd60e51b81526004016109c490613317565b60405180910390fd5b600655565b600380546109df9061334c565b80601f0160208091040260200160405190810160405280929190818152602001828054610a0b9061334c565b8015610a585780601f10610a2d57610100808354040283529160200191610a58565b820191906000526020600020905b815481529060010190602001808311610a3b57829003601f168201915b505050505081565b6001546001600160a01b03163314610a8a5760405162461bcd60e51b81526004016109c490613317565b6000805463ffffffff909216600160a01b0263ffffffff60a01b19909216919091179055565b336000818152600960209081526040808320868452825280832080546001820154818652600885528386206001600160a01b039182168088529552929094206004810154919590929190911614610b495760405162461bcd60e51b815260206004820152601760248201527f4f6e6c792061726269747261746f7220616c6c6f77656400000000000000000060448201526064016109c4565b6002815460ff166005811115610b6157610b61613289565b14610b7e5760405162461bcd60e51b81526004016109c490613386565b6003810180548691600091610b95906001906133d3565b81548110610ba557610ba56133e6565b906000526020600020906005020190508060040180549050600103610be85780600401600081548110610bda57610bda6133e6565b906000526020600020015491505b60028301829055825460ff19166003178355604051828152889033907f394027a5fa6e098a1191094d1719d6929b9abc535fcc0c8f448d6a4e756222769060200160405180910390a35050505050505050565b6001546001600160a01b03163314610c655760405162461bcd60e51b81526004016109c490613317565b600555565b6000848152600b6020908152604080832054600883528184206001600160a01b039091168085529252822060038101805484919087908110610cae57610cae6133e6565b6000918252602090912060059091020190506003825460ff166005811115610cd857610cd8613289565b14610d1c5760405162461bcd60e51b8152602060048201526014602482015273111a5cdc1d5d19481b9bdd081c995cdbdb1d995960621b60448201526064016109c4565b600085815260018201602052604090205460ff16610d61576001600160a01b038716600090815260028201602090815260408083208884529091529020549350610eb7565b6002820154600090815260018201602052604090205460ff16610e4d576004810154600110610d91576000610e46565b80600001600082600401600181548110610dad57610dad6133e6565b906000526020600020015481526020019081526020016000205481600001600083600401600081548110610de357610de36133e6565b9060005260206000200154815260200190815260200160002054610e0791906133fc565b60038201546001600160a01b038916600090815260028401602090815260408083208a8452909152902054610e3c919061340f565b610e469190613426565b9350610eb7565b84826002015403610eb75760008581526020829052604090205480610e73576000610eb3565b60038201546001600160a01b038916600090815260028401602090815260408083208a84529091529020548291610ea99161340f565b610eb39190613426565b9450505b8315610f46576001600160a01b038716600081815260028301602090815260408083208984529091528082208290555186156108fc0291879190818181858888f1505060408051898152602081018990526001600160a01b038c1694508a93508c92507f54b3cab3cb5c4aca3209db1151caff092e878011202e43a36782d4ebe0b963ae910160405180910390a45b505050949350505050565b60008281526008602090815260408083206001600160a01b0385168452909152902082906005815460ff166005811115610f8d57610f8d613289565b14610faa5760405162461bcd60e51b81526004016109c490613386565b60408051602481018690526001600160a01b03851660448083018290528351808403909101815260649092018352602080830180516001600160e01b03166305fb2b4f60e51b90811790915285546000888152600884528681209481529390925293822082815560018101839055600281018390556101009091046001600160f81b03169161103c6003830182612d74565b6004820180546001600160a01b031916905561105c600583016000612d98565b5060006006919091018190556040516001600160a01b0388169183156108fc02918491818181858888f15050600054604051633dbb202b60e01b81526001600160a01b0382169450633dbb202b93506110eb92507f0000000000000000000000000000000000000000000000000000000000000000918791600160a01b90910463ffffffff1690600401613448565b600060405180830381600087803b15801561110557600080fd5b505af1158015611119573d6000803e3d6000fd5b50506040516001600160a01b03891692508991507fe7700735be0b02f71ef1d623678daf36cd936af4c349a22ad9d5a8b217df0dd990600090a350505050505050565b6000828152600860209081526040808320600b8352818420546001600160a01b0316845290915281206002815460ff16600581111561119d5761119d613289565b146111e25760405162461bcd60e51b81526020600482015260156024820152742737903234b9b83aba32903a379030b83832b0b61760591b60448201526064016109c4565b600181015460048083015460405163afe15cfb60e01b815291820183905260009182916001600160a01b03169063afe15cfb906024016040805180830381865afa158015611234573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906112589190613482565b9150915081421015801561126b57508042105b6112b05760405162461bcd60e51b815260206004820152601660248201527520b83832b0b6103832b934b7b21034b99037bb32b91760511b60448201526064016109c4565b600484810154604051631c3db16d60e01b815291820185905260009182916001600160a01b031690631c3db16d90602401602060405180830381865afa1580156112fe573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061132291906134a6565b90508781036113355760055491506113b7565b6007546127109061134686866133d3565b611350919061340f565b61135a9190613426565b61136485426133d3565b106113b15760405162461bcd60e51b815260206004820152601f60248201527f41707065616c20706572696f64206973206f76657220666f72206c6f7365720060448201526064016109c4565b60065491505b5060038501546000906113cc906001906133d3565b905060008660030182815481106113e5576113e56133e6565b600091825260208083208c84526001600590930201918201905260409091205490915060ff16156114585760405162461bcd60e51b815260206004820152601b60248201527f41707065616c2066656520697320616c726561647920706169642e000000000060448201526064016109c4565b60048088015460405163791f8b7360e11b81526000926001600160a01b039092169163f23f16e691611491918b9160058e01910161353c565b602060405180830381865afa1580156114ae573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906114d291906134a6565b905060006127106114e3868461340f565b6114ed9190613426565b6114f790836133fc565b60008c81526020859052604081205491925090349061151690846133d3565b116115395760008c81526020859052604090205461153490836133d3565b61153b565b345b9050336001600160a01b0316858e7fcae597f39a3ad75c2e10d46b031f023c5c2babcd58ca0491b122acda3968d4c08f85604051611583929190918252602082015260400190565b60405180910390a433600090815260028501602090815260408083208f8452909152812080548392906115b79084906133fc565b909155505060008c815260208590526040812080548392906115da9084906133fc565b909155505060008c81526020859052604090205482116116a85760008c815260208590526040812054600386018054919290916116189084906133fc565b92505081905550836004018c908060018154018082558091505060019003906000526020600020016000909190919091505560018460010160008e815260200190815260200160002060006101000a81548160ff0219169083151502179055508b858e7f39493c1b78d9a13bcc9e1d532fc7faed3889248d93affa811416ce3c6bcb1a6860405160405180910390a45b600484015460011015611756576003808b01805460010181556000528401546116d29084906133d3565b84600301819055508960040160009054906101000a90046001600160a01b03166001600160a01b03166349912f88848b8d6005016040518463ffffffff1660e01b815260040161172392919061353c565b6000604051808303818588803b15801561173c57600080fd5b505af1158015611750573d6000803e3d6000fd5b50505050505b600061176282346133d3565b111561178e57336108fc61177683346133d3565b6040518115909202916000818181858888f150505050505b50505060008981526001909101602052604090205460ff1696505050505050505b92915050565b60008281526008602090815260408083206001600160a01b0385168452909152902082906003815460ff1660058111156117f1576117f1613289565b146118355760405162461bcd60e51b8152602060048201526014602482015273111a5cdc1d5d19481b9bdd081c995cdbdb1d995960621b60448201526064016109c4565b6000816002015460000361184b5760001961185c565b6001826002015461185c91906133d3565b6040805160248101889052604480820184905282518083039091018152606490910182526020810180516306bad43760e51b6001600160e01b0390911681179091528554600460ff19909116811787556000549351633dbb202b60e01b8152949550909391926001600160a01b03811692633dbb202b92611910927f0000000000000000000000000000000000000000000000000000000000000000928792600160a01b90920463ffffffff169101613448565b600060405180830381600087803b15801561192a57600080fd5b505af115801561193e573d6000803e3d6000fd5b5050604080518a8152602081018790527fba07eb273dd4b62b682a13480e2838bfbd6afab07919d6bcad9156af107f8c41935001905060405180910390a150505050505050565b6001546001600160a01b031633146119af5760405162461bcd60e51b81526004016109c490613317565b600480549060006119bf8361355d565b91905055506004547f61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d826040516119f69190612e8f565b60405180910390a250565b6000828152600b6020908152604080832054600883528184206001600160a01b03909116808552925282206003810180546060949385939092909185919088908110611a4f57611a4f6133e6565b9060005260206000209060050201905080600401805480602002602001604051908101604052809291908181526020018280548015611aad57602002820191906000526020600020905b815481526020019060010190808311611a99575b5050506004840154929650505067ffffffffffffffff811115611ad257611ad2612f98565b604051908082528060200260200182016040528015611afb578160200160208202803683370190505b50955060005b6004820154811015611b6757816000016000836004018381548110611b2857611b286133e6565b9060005260206000200154815260200190815260200160002054878281518110611b5457611b546133e6565b6020908102919091010152600101611b01565b50806003015494505050509250925092565b60025460405163f7434ea960e01b81526000916001600160a01b03169063f7434ea990611bab90600390600401613576565b602060405180830381865afa158015611bc8573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906117af91906134a6565b6000838152600b6020908152604080832054600883528184206001600160a01b039182168086529352922060048101549192909116611c7757600254604051339187916001600160a01b03909116907fdccf2f8b2cc26eafcd61905cba744cff4b81d14740725f6376390dc6298a6a3c90611c6a9089908990613589565b60405180910390a4611cc7565b6004810154604051339187916001600160a01b03909116907fdccf2f8b2cc26eafcd61905cba744cff4b81d14740725f6376390dc6298a6a3c90611cbe9089908990613589565b60405180910390a45b5050505050565b6000828152600a602052604090205460ff1615611d2d5760405162461bcd60e51b815260206004820152601760248201527f4469737075746520616c7265616479206372656174656400000000000000000060448201526064016109c4565b6000828152600860209081526040808320338452909152812090815460ff166005811115611d5d57611d5d613289565b14611daa5760405162461bcd60e51b815260206004820152601d60248201527f4172626974726174696f6e20616c72656164792072657175657374656400000060448201526064016109c4565b6002546004820180546001600160a01b0319166001600160a01b0390921691909117905560058101611ddd600382613609565b506004805460068301556040805160248101869052336044820152606480820186905282518083039091018152608490910182526020810180516001600160e01b031663666b746960e11b908117909152600254925163f7434ea960e01b8152909391926000926001600160a01b039091169163f7434ea991611e639160039101613576565b602060405180830381865afa158015611e80573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611ea491906134a6565b905080341015611eee5760405162461bcd60e51b81526020600482015260156024820152744465706f7369742076616c756520746f6f206c6f7760581b60448201526064016109c4565b6001610100346001600160f81b031602178455600054604051633dbb202b60e01b81526001600160a01b03821691633dbb202b91611f60917f0000000000000000000000000000000000000000000000000000000000000000918791600160a01b900463ffffffff1690600401613448565b600060405180830381600087803b158015611f7a57600080fd5b505af1158015611f8e573d6000803e3d6000fd5b50506040518781523392508891507f2067d1b3170fe803dd001b95b04f9d372eb9c3e5a48ed131a62962a7300bdb209060200160405180910390a3505050505050565b6001546001600160a01b03163314611ffb5760405162461bcd60e51b81526004016109c490613317565b600080546001600160a01b0319166001600160a01b0392909216919091179055565b6001546001600160a01b031633146120475760405162461bcd60e51b81526004016109c490613317565b600280546001600160a01b0319166001600160a01b038516179055600361206f8284836136e6565b50505050565b6000838152600b6020908152604080832054600883528184206001600160a01b0390911680855292528220600381018054849392918491889081106120bc576120bc6133e6565b600091825260208083209883526005909102909701808852604080832054600190920190985296902054959860ff90961697509495505050505050565b6000546001600160a01b031633146121435760405162461bcd60e51b815260206004820152600d60248201526c2727aa2fa6a2a9a9a2a723a2a960991b60448201526064016109c4565b60005460408051636e296e4560e01b815290516001600160a01b037f00000000000000000000000000000000000000000000000000000000000000008116931691636e296e459160048083019260209291908290030181865afa1580156121ae573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906121d291906137a6565b6001600160a01b0316146122285760405162461bcd60e51b815260206004820181905260248201527f43616e206f6e6c792062652063616c6c656420627920486f6d652070726f787960448201526064016109c4565b60008281526008602090815260408083206001600160a01b0385168452909152902082906001815460ff16600581111561226457612264613289565b146122815760405162461bcd60e51b81526004016109c490613386565b805460008381526008602090815260408083206001600160a01b0388168452909152812081815560018101829055600281018290556101009092046001600160f81b031691906122d46003830182612d74565b6004820180546001600160a01b03191690556122f4600583016000612d98565b5060006006919091018190556040516001600160a01b0386169183156108fc02918491818181858888f150506040516001600160a01b03881693508892507fe7700735be0b02f71ef1d623678daf36cd936af4c349a22ad9d5a8b217df0dd99150600090a35050505050565b6000838152600b6020908152604080832054600883528184206001600160a01b0390911680855292528220600381018054606094859493929091889081106123aa576123aa6133e6565b906000526020600020906005020190508060040180548060200260200160405190810160405280929190818152602001828054801561240857602002820191906000526020600020905b8154815260200190600101908083116123f4575b5050506004840154929750505067ffffffffffffffff81111561242d5761242d612f98565b604051908082528060200260200182016040528015612456578160200160208202803683370190505b50935060005b84518110156124d2576001600160a01b038716600090815260028301602052604081208751909190889084908110612496576124966133e6565b60200260200101518152602001908152602001600020548582815181106124bf576124bf6133e6565b602090810291909101015260010161245c565b50505050935093915050565b6001546001600160a01b031633146125085760405162461bcd60e51b81526004016109c490613317565b600755565b6001546001600160a01b031633146125375760405162461bcd60e51b81526004016109c490613317565b600180546001600160a01b0319166001600160a01b0392909216919091179055565b6000546001600160a01b031633146125a35760405162461bcd60e51b815260206004820152600d60248201526c2727aa2fa6a2a9a9a2a723a2a960991b60448201526064016109c4565b60005460408051636e296e4560e01b815290516001600160a01b037f00000000000000000000000000000000000000000000000000000000000000008116931691636e296e459160048083019260209291908290030181865afa15801561260e573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061263291906137a6565b6001600160a01b0316146126885760405162461bcd60e51b815260206004820181905260248201527f43616e206f6e6c792062652063616c6c656420627920486f6d652070726f787960448201526064016109c4565b60008281526008602090815260408083206001600160a01b0385168452909152902082906001815460ff1660058111156126c4576126c4613289565b146126e15760405162461bcd60e51b81526004016109c490613386565b60048082015460405163f7434ea960e01b81526000926001600160a01b039092169163f7434ea99161271891600587019101613576565b602060405180830381865afa158015612735573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061275991906134a6565b825490915061010090046001600160f81b0316811161299e5760048083015460405163c13517e160e01b81526001600160a01b039091169163c13517e19184916127ac916000199160058901910161353c565b60206040518083038185885af1935050505080156127e7575060408051601f3d908101601f191682019092526127e4918101906134a6565b60015b61283157815460ff191660051782556040516001600160a01b0385169086907f9beda0c81abc1c65da7685f113195974dfddb781dfde6263e5a1b13a5356ffad90600090a3611cc7565b60048301546001600160a01b039081166000908152600960209081526040808320858452825280832088815560018181018054968c166001600160a01b031997881681179091558a8652600a8552838620805460ff1916909217909155600b8452828520805490961617909455848352600c909152812043905584546128c79085906001600160f81b03610100909104166133d3565b60028655600180870185905560038701805490910181556000529050801561290f576040516001600160a01b0388169082156108fc029083906000818181858888f150505050505b82876001600160a01b0316897fcf9bcbc2efae51060af73ef64119281007f63eada739f19c41c181d8350fa81460405160405180910390a460048501546006860154604080519182526020820189905285926001600160a01b0316917f74baab670a4015ab2f1b467c5252a96141a2573f2908e58a92081e80d3cfde3d910160405180910390a3505050611cc7565b815460ff191660051782556040516001600160a01b0385169086907f9beda0c81abc1c65da7685f113195974dfddb781dfde6263e5a1b13a5356ffad90600090a35050505050565b6000838152600b6020908152604080832054600883528184206001600160a01b03909116808552925282206003815460ff166005811115612a2957612a29613289565b1015612a36575050612c2c565b6002810154600382015460005b81811015612c26576000846003018281548110612a6257612a626133e6565b600091825260208083208b84526001600590930201918201905260409091205490915060ff16612ac3576001600160a01b038916600090815260028201602090815260408083208b8452909152902054612abc90886133fc565b9650612c1d565b600084815260018201602052604090205460ff16612bad576004810154600110612aee576000612ba3565b80600001600082600401600181548110612b0a57612b0a6133e6565b906000526020600020015481526020019081526020016000205481600001600083600401600081548110612b4057612b406133e6565b9060005260206000200154815260200190815260200160002054612b6491906133fc565b60038201546001600160a01b038b16600090815260028401602090815260408083208d8452909152902054612b99919061340f565b612ba39190613426565b612abc90886133fc565b878403612c1d5760008881526020829052604090205480612bcf576000612c0f565b60038201546001600160a01b038b16600090815260028401602090815260408083208d84529091529020548291612c059161340f565b612c0f9190613426565b612c1990896133fc565b9750505b50600101612a43565b50505050505b9392505050565b60086020908152600092835260408084209091529082529020805460018201546002830154600484015460058501805460ff8616966101009096046001600160f81b0316956001600160a01b03909316929190612c8f9061334c565b80601f0160208091040260200160405190810160405280929190818152602001828054612cbb9061334c565b8015612d085780601f10612cdd57610100808354040283529160200191612d08565b820191906000526020600020905b815481529060010190602001808311612ceb57829003601f168201915b5050505050908060060154905087565b6000838152600b6020908152604080832054600883528184206001600160a01b0390911680855292528220600381015491929091905b81811015612d6b57612d6287878388610c6a565b50600101612d4e565b50505050505050565b5080546000825560050290600052602060002090810190612d959190612dd2565b50565b508054612da49061334c565b6000825580601f10612db4575050565b601f016020900490600052602060002090810190612d959190612dfd565b80821115612df957600060038201819055612df06004830182612e12565b50600501612dd2565b5090565b5b80821115612df95760008155600101612dfe565b5080546000825590600052602060002090810190612d959190612dfd565b600060208284031215612e4257600080fd5b5035919050565b6000815180845260005b81811015612e6f57602081850181015186830182015201612e53565b506000602082860101526020601f19601f83011685010191505092915050565b602081526000612c2c6020830184612e49565b600060208284031215612eb457600080fd5b813563ffffffff81168114612c2c57600080fd5b60008060408385031215612edb57600080fd5b50508035926020909101359150565b6001600160a01b0381168114612d9557600080fd5b60008060008060808587031215612f1557600080fd5b843593506020850135612f2781612eea565b93969395505050506040820135916060013590565b60008060408385031215612f4f57600080fd5b823591506020830135612f6181612eea565b809150509250929050565b60008060408385031215612f7f57600080fd5b8235612f8a81612eea565b946020939093013593505050565b634e487b7160e01b600052604160045260246000fd5b600060208284031215612fc057600080fd5b813567ffffffffffffffff80821115612fd857600080fd5b818401915084601f830112612fec57600080fd5b813581811115612ffe57612ffe612f98565b604051601f8201601f19908116603f0116810190838211818310171561302657613026612f98565b8160405282815287602084870101111561303f57600080fd5b826020860160208301376000928101602001929092525095945050505050565b60008151808452602080850194506020840160005b8381101561309057815187529582019590820190600101613074565b509495945050505050565b6060815260006130ae606083018661305f565b84602084015282810360408401526130c6818561305f565b9695505050505050565b60008083601f8401126130e257600080fd5b50813567ffffffffffffffff8111156130fa57600080fd5b60208301915083602082850101111561311257600080fd5b9250929050565b60008060006040848603121561312e57600080fd5b83359250602084013567ffffffffffffffff81111561314c57600080fd5b613158868287016130d0565b9497909650939450505050565b60006020828403121561317757600080fd5b8135612c2c81612eea565b60008060006040848603121561319757600080fd5b83356131a281612eea565b9250602084013567ffffffffffffffff81111561314c57600080fd5b6000806000606084860312156131d357600080fd5b505081359360208301359350604090920135919050565b6000806000606084860312156131ff57600080fd5b8335925060208401359150604084013561321881612eea565b809150509250925092565b604081526000613236604083018561305f565b8281036020840152613248818561305f565b95945050505050565b60008060006060848603121561326657600080fd5b83359250602084013561327881612eea565b929592945050506040919091013590565b634e487b7160e01b600052602160045260246000fd5b6000600689106132bf57634e487b7160e01b600052602160045260246000fd5b8882526001600160f81b038816602083015260408201879052606082018690526001600160a01b038516608083015260e060a0830181905261330390830185612e49565b90508260c083015298975050505050505050565b6020808252818101527f5468652063616c6c6572206d7573742062652074686520676f7665726e6f722e604082015260600190565b600181811c9082168061336057607f821691505b60208210810361338057634e487b7160e01b600052602260045260246000fd5b50919050565b6020808252601a908201527f496e76616c6964206172626974726174696f6e20737461747573000000000000604082015260600190565b634e487b7160e01b600052601160045260246000fd5b818103818111156117af576117af6133bd565b634e487b7160e01b600052603260045260246000fd5b808201808211156117af576117af6133bd565b80820281158282048414176117af576117af6133bd565b60008261344357634e487b7160e01b600052601260045260246000fd5b500490565b6001600160a01b038416815260606020820181905260009061346c90830185612e49565b905063ffffffff83166040830152949350505050565b6000806040838503121561349557600080fd5b505080516020909101519092909150565b6000602082840312156134b857600080fd5b5051919050565b600081546134cc8161334c565b8085526020600183811680156134e9576001811461350357613531565b60ff1985168884015283151560051b880183019550613531565b866000528260002060005b858110156135295781548a820186015290830190840161350e565b890184019650505b505050505092915050565b82815260406020820152600061355560408301846134bf565b949350505050565b60006001820161356f5761356f6133bd565b5060010190565b602081526000612c2c60208301846134bf565b60208152816020820152818360408301376000818301604090810191909152601f909201601f19160101919050565b601f821115613604576000816000526020600020601f850160051c810160208610156135e15750805b601f850160051c820191505b81811015613600578281556001016135ed565b5050505b505050565b818103613614575050565b61361e825461334c565b67ffffffffffffffff81111561363657613636612f98565b61364a81613644845461334c565b846135b8565b6000601f82116001811461367e57600083156136665750848201545b600019600385901b1c1916600184901b178455611cc7565b600085815260209020601f19841690600086815260209020845b838110156136b85782860154825560019586019590910190602001613698565b50858310156136d65781850154600019600388901b60f8161c191681555b5050505050600190811b01905550565b67ffffffffffffffff8311156136fe576136fe612f98565b6137128361370c835461334c565b836135b8565b6000601f841160018114613746576000851561372e5750838201355b600019600387901b1c1916600186901b178355611cc7565b600083815260209020601f19861690835b828110156137775786850135825560209485019460019092019101613757565b50868210156137945760001960f88860031b161c19848701351681555b505060018560011b0183555050505050565b6000602082840312156137b857600080fd5b8151612c2c81612eea56fea2646970667358221220b01bd4721cb9b66fe81b5eb4ab6e87935b40d3023d8290f412a4c70ca7b9194464736f6c63430008190033", "devdoc": {"details": "https://docs.optimism.io/builders/app-developers/bridging/messaging", "events": {"ArbitrationCanceled(bytes32,address)": {"params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationCreated(bytes32,address,uint256)": {"params": {"_disputeID": "The ID of the dispute.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationFailed(bytes32,address)": {"details": "This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.", "params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationRequested(bytes32,address,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "Contribution(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contribution is made, inside fundAppeal function.", "params": {"_amount": "Contribution amount.", "_contributor": "Caller of fundAppeal function.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "The round number the contribution was made to.", "ruling": "Indicates the ruling option which got the contribution."}}, "Dispute(address,uint256,uint256,uint256)": {"details": "To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.", "params": {"_arbitrator": "The arbitrator of the contract.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_evidenceGroupID": "Unique identifier of the evidence group that is linked to this dispute.", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Evidence(address,uint256,address,string)": {"details": "To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).", "params": {"_arbitrator": "The arbitrator of the contract.", "_evidence": "IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'", "_evidenceGroupID": "Unique identifier of the evidence group the evidence belongs to.", "_party": "The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party."}}, "MetaEvidence(uint256,string)": {"details": "To be emitted when meta-evidence is submitted.", "params": {"_evidence": "IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Ruling(address,uint256,uint256)": {"details": "To be raised when a ruling is given.", "params": {"_arbitrator": "The arbitrator giving the ruling.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_ruling": "The ruling which was given."}}, "RulingFunded(uint256,uint256,uint256)": {"details": "To be raised when a ruling option is fully funded for appeal.", "params": {"_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "Number of the round this ruling option was fully funded in.", "_ruling": "The ruling option which just got fully funded."}}, "RulingRelayed(bytes32,bytes32)": {"params": {"_questionID": "The ID of the question with the ruling to relay.", "_ruling": "Ruling converted into Realitio format."}}, "Withdrawal(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contributor withdraws non-zero value.", "params": {"_contributor": "The beneficiary of withdrawal.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_reward": "Total amount of withdrawal, consists of reimbursed deposits plus rewards.", "_round": "The round number the withdrawal was made from.", "_ruling": "Indicates the ruling option which contributor gets rewards from."}}}, "kind": "dev", "methods": {"changeArbitrator(address,bytes)": {"params": {"_arbitrator": "New arbitrator address.", "_arbitratorExtraData": "Extradata for the arbitrator"}}, "changeGovernor(address)": {"params": {"_governor": "New governor address."}}, "changeLoserAppealPeriodMultiplier(uint256)": {"params": {"_loserAppealPeriodMultiplier": "New loser multiplier for appeal perido."}}, "changeLoserMultiplier(uint256)": {"params": {"_loserMultiplier": "New loser multiplier."}}, "changeMessenger(address)": {"params": {"_messenger": "New MESSENGER address."}}, "changeMetaevidence(string)": {"params": {"_metaEvidence": "Metaevidence URI."}}, "changeMinGasLimit(uint32)": {"params": {"_minGasLimit": "New minimum gas limit."}}, "changeWinnerMultiplier(uint256)": {"params": {"_winnerMultiplier": "New winner multiplier."}}, "constructor": {"params": {"_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_governor": "Governor of the contract.", "_homeProxy": "Proxy on L2.", "_messenger": "contract for L1 -> L2 tx", "_metaEvidence": "The URI of the meta evidence file.", "_multipliers": "Appeal multipliers:  - Multiplier for calculating the appeal cost of the winning answer.  - Multiplier for calculating the appeal cost of the losing answer.  - Multiplier for calculating the appeal period for the losing answer."}}, "externalIDtoLocalID(uint256)": {"params": {"_externalDisputeID": "Dispute id as in arbitrator side."}, "returns": {"_0": "localDisputeID Dispute id as in arbitrable contract."}}, "fundAppeal(uint256,uint256)": {"params": {"_answer": "One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.", "_arbitrationID": "The ID of the arbitration, which is questionID cast into uint256."}, "returns": {"_0": "Whether the answer was fully funded or not."}}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_contributor": "The address whose contributions to query.", "_round": "The round to query."}, "returns": {"contributions": "The amount contributed to each funded answer by the contributor.", "fundedAnswers": "IDs of the answers that are fully funded."}}, "getDisputeFee(bytes32)": {"returns": {"_0": "The fee to create a dispute."}}, "getFundingStatus(uint256,uint256,uint256)": {"params": {"_answer": "The answer choice to get funding status for.", "_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"fullyFunded": "Whether the answer is fully funded or not.", "raised": "The amount paid for this answer."}}, "getMultipliers()": {"returns": {"divisor": "Multiplier divisor.", "loser": "Losers stake multiplier.", "loserAppealPeriod": "Multiplier for calculating an appeal period duration for the losing side.", "winner": "Winners stake multiplier."}}, "getNumberOfRounds(uint256)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question."}, "returns": {"_0": "The number of rounds."}}, "getRoundInfo(uint256,uint256)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"feeRewards": "The amount of fees that will be used as rewards.", "fundedAnswers": "IDs of fully funded answers.", "paidFees": "The amount of fees paid for each fully funded answer."}}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The contributor for which to query.", "_contributedTo": "Answer that received contributions from contributor."}, "returns": {"sum": "The total amount available to withdraw."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "numberOfRulingOptions(uint256)": {"returns": {"_0": "count The number of ruling options."}}, "questionIDToArbitrationID(bytes32)": {"params": {"_questionID": "The ID of the question."}, "returns": {"_0": "The ID of the arbitration."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "relayRule(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}, "submitEvidence(uint256,string)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question.", "_evidenceURI": "Link to evidence."}}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"params": {"_answer": "The answer to query the reward from.", "_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address to send reward to.", "_round": "The round from which to withdraw."}, "returns": {"reward": "The withdrawn amount."}}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address that made contributions.", "_contributedTo": "Answer that received contributions from contributor."}}}, "title": "Arbitration proxy for Realitio on foreign chain (eg. mainnet).", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}, "RulingRelayed(bytes32,bytes32)": {"notice": "Should be emitted when the ruling is relayed to home proxy."}}, "kind": "user", "methods": {"changeArbitrator(address,bytes)": {"notice": "Changes the arbitrator and extradata. The arbitrator is trusted to support appeal period and not reenter. Note avoid changing arbitrator if there is an active arbitration request in Requested phase, otherwise evidence submitted during this phase will be submitted to the new arbitrator, while arbitration request will be processed by the old one."}, "changeGovernor(address)": {"notice": "Changes the governor of the contract."}, "changeLoserAppealPeriodMultiplier(uint256)": {"notice": "Changes loser multiplier for appeal period."}, "changeLoserMultiplier(uint256)": {"notice": "Changes loser multiplier value."}, "changeMessenger(address)": {"notice": "Changes the L1 -> L2 MESSENGER contract."}, "changeMetaevidence(string)": {"notice": "Updates the meta evidence used for disputes."}, "changeMinGasLimit(uint32)": {"notice": "Changes minimum gas limit for L1 -> L2 tx."}, "changeWinnerMultiplier(uint256)": {"notice": "Changes winner multiplier value."}, "constructor": {"notice": "Creates an arbitration proxy on the foreign chain (L1)."}, "externalIDtoLocalID(uint256)": {"notice": "Maps external (arbitrator side) dispute id to local (arbitrable) dispute id."}, "fundAppeal(uint256,uint256)": {"notice": "Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded."}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"notice": "Gets contributions to the answers that are fully funded."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "getFundingStatus(uint256,uint256,uint256)": {"notice": "Gets the information of a round of a question for a specific answer choice."}, "getMultipliers()": {"notice": "Returns stake multipliers."}, "getNumberOfRounds(uint256)": {"notice": "Gets the number of rounds of the specific question."}, "getRoundInfo(uint256,uint256)": {"notice": "Gets the information of a round of a question."}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"notice": "Returns the sum of withdrawable amount."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover RedStone fees."}, "numberOfRulingOptions(uint256)": {"notice": "Returns number of possible ruling options. Valid rulings are [0, return value]."}, "questionIDToArbitrationID(bytes32)": {"notice": "Casts question ID into uint256 thus returning the related arbitration ID."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "relayRule(bytes32,address)": {"notice": "Relays the ruling to home proxy. Requires a small deposit to cover RedStone fees."}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute. Can only be called by the arbitrator."}, "submitEvidence(uint256,string)": {"notice": "Allows to submit evidence for a particular question."}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"notice": "Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner."}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"notice": "Allows to withdraw any rewards or reimbursable fees for all rounds at once."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 373, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "messenger", "offset": 0, "slot": "0", "type": "t_contract(ICrossDomainMessenger)3081"}, {"astId": 376, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "minGasLimit", "offset": 20, "slot": "0", "type": "t_uint32"}, {"astId": 380, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "governor", "offset": 0, "slot": "1", "type": "t_address"}, {"astId": 383, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrator", "offset": 0, "slot": "2", "type": "t_contract(IArbitrator)249"}, {"astId": 385, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitratorExtraData", "offset": 0, "slot": "3", "type": "t_bytes_storage"}, {"astId": 387, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "metaEvidenceUpdates", "offset": 0, "slot": "4", "type": "t_uint256"}, {"astId": 389, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "winner<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "5", "type": "t_uint256"}, {"astId": 391, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "loserMultiplier", "offset": 0, "slot": "6", "type": "t_uint256"}, {"astId": 393, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "loserAppealPeriodMultiplier", "offset": 0, "slot": "7", "type": "t_uint256"}, {"astId": 400, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrationRequests", "offset": 0, "slot": "8", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)345_storage))"}, {"astId": 407, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitratorDisputeIDToDisputeDetails", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_mapping(t_uint256,t_struct(DisputeDetails)350_storage))"}, {"astId": 411, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrationIDToDisputeExists", "offset": 0, "slot": "10", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 415, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrationIDToRequester", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 419, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrationCreatedBlock", "offset": 0, "slot": "12", "type": "t_mapping(t_uint256,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_struct(Round)370_storage)dyn_storage": {"base": "t_struct(Round)370_storage", "encoding": "dynamic_array", "label": "struct RealitioForeignProxyRedStone.Round[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_contract(IArbitrator)249": {"encoding": "inplace", "label": "contract IArbitrator", "numberOfBytes": "20"}, "t_contract(ICrossDomainMessenger)3081": {"encoding": "inplace", "label": "contract ICrossDomainMessenger", "numberOfBytes": "20"}, "t_enum(Status)324": {"encoding": "inplace", "label": "enum RealitioForeignProxyRedStone.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_uint256,t_struct(DisputeDetails)350_storage))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => struct RealitioForeignProxyRedStone.DisputeDetails))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_struct(DisputeDetails)350_storage)"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_uint256)"}, "t_mapping(t_address,t_struct(ArbitrationRequest)345_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignProxyRedStone.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)345_storage"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_uint256,t_bool)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)345_storage))": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => mapping(address => struct RealitioForeignProxyRedStone.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)345_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)350_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignProxyRedStone.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)350_storage"}, "t_mapping(t_uint256,t_uint256)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(ArbitrationRequest)345_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyRedStone.ArbitrationRequest", "members": [{"astId": 327, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)324"}, {"astId": 329, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}, {"astId": 331, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "disputeID", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 333, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "answer", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 337, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "rounds", "offset": 0, "slot": "3", "type": "t_array(t_struct(Round)370_storage)dyn_storage"}, {"astId": 340, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrator", "offset": 0, "slot": "4", "type": "t_contract(IArbitrator)249"}, {"astId": 342, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitratorExtraData", "offset": 0, "slot": "5", "type": "t_bytes_storage"}, {"astId": 344, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "metaEvidenceID", "offset": 0, "slot": "6", "type": "t_uint256"}], "numberOfBytes": "224"}, "t_struct(DisputeDetails)350_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyRedStone.DisputeDetails", "members": [{"astId": 347, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "arbitrationID", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 349, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_struct(Round)370_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyRedStone.Round", "members": [{"astId": 354, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "paidFees", "offset": 0, "slot": "0", "type": "t_mapping(t_uint256,t_uint256)"}, {"astId": 358, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "hasPaid", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 364, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "contributions", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))"}, {"astId": 366, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "feeRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 369, "contract": "src/RealitioForeignProxyRedStone.sol:RealitioForeignProxyRedStone", "label": "fundedAnswers", "offset": 0, "slot": "4", "type": "t_array(t_uint256)dyn_storage"}], "numberOfBytes": "160"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint32": {"encoding": "inplace", "label": "uint32", "numberOfBytes": "4"}}}}
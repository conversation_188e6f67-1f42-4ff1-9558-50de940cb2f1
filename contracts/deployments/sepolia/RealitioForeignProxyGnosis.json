{"address": "0x781Bfb3A7179e27D78c6d18bCc54A2af61904aE0", "abi": [{"inputs": [{"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "uint256", "name": "_winnerMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "_loserAppealPeriodMultiplier", "type": "uint256"}, {"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "uint256", "name": "_homeChainId", "type": "uint256"}, {"internalType": "contract IAMB", "name": "_amb", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "Contribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "RulingFunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_ruling", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "META_EVIDENCE_ID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTIPLIER_DIVISOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFUSE_TO_ARBITRATE_REALITIO", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "amb", "outputs": [{"internalType": "contract IAMB", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationCreatedBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignProxyGnosis.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}, {"internalType": "uint256", "name": "disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "answer", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "disputeIDToDisputeDetails", "outputs": [{"internalType": "uint256", "name": "arbitrationID", "type": "uint256"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_externalDisputeID", "type": "uint256"}], "name": "externalIDtoLocalID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "fundAppeal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "address", "name": "_contributor", "type": "address"}], "name": "getContributionsToSuccessfulFundings", "outputs": [{"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "contributions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "getFundingStatus", "outputs": [{"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "bool", "name": "fullyFunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMultipliers", "outputs": [{"internalType": "uint256", "name": "winner", "type": "uint256"}, {"internalType": "uint256", "name": "loser", "type": "uint256"}, {"internalType": "uint256", "name": "loserAppealPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "divisor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}], "name": "getNumberOfRounds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256[]", "name": "paidFees", "type": "uint256[]"}, {"internalType": "uint256", "name": "feeRewards", "type": "uint256"}, {"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "getTotalWithdrawableAmount", "outputs": [{"internalType": "uint256", "name": "sum", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "homeChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserAppealPeriodMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "numberOfRulingOptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "questionIDToArbitrationID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "string", "name": "_evidenceURI", "type": "string"}], "name": "submitEvidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "winner<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "withdrawFeesAndRewards", "outputs": [{"internalType": "uint256", "name": "reward", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "withdrawFeesAndRewardsForAllRounds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0xad692668544510d566f50f6a86a7d9b92f548e3abfe6b89126632caab8cb41d2", "receipt": {"to": null, "from": "0x0efFC4A996045aff0489774051f94f42F2D6dfc9", "contractAddress": "0x781Bfb3A7179e27D78c6d18bCc54A2af61904aE0", "transactionIndex": 112, "gasUsed": "2982990", "logsBloom": "0x00000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000000000000000000000000000000000004008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000002000000000000000000800000000000000000", "blockHash": "0x69e2495f16a34c5e45f336cb7b67f4f61cccdd20a9086e9ea16ccedf7b5f1f17", "transactionHash": "0xad692668544510d566f50f6a86a7d9b92f548e3abfe6b89126632caab8cb41d2", "logs": [{"transactionIndex": 112, "blockNumber": 8027059, "transactionHash": "0xad692668544510d566f50f6a86a7d9b92f548e3abfe6b89126632caab8cb41d2", "address": "0x781Bfb3A7179e27D78c6d18bCc54A2af61904aE0", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000342f697066732f516d6268516b61516b4b444643656a7450594136416756316a384e554350477032543433626b5237414433335148000000000000000000000000", "logIndex": 185, "blockHash": "0x69e2495f16a34c5e45f336cb7b67f4f61cccdd20a9086e9ea16ccedf7b5f1f17"}], "blockNumber": 8027059, "cumulativeGasUsed": "13554219", "status": 1, "byzantium": true}, "args": ["0x90992fb4E15ce0C59aEFfb376460Fda4Ee19C879", "0x00000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000000000000000001", "/ipfs/QmbhQkaQkKDFCejtPYA6AgV1j8NUCPGp2T43bkR7AD33QH", 3000, 7000, 5000, "0x87f58F0dCF3c99BA2F3eB0604e5c335893e2EAf9", 10200, "0xf2546D6648BD2af6a008A7e7C1542BB240329E11"], "numDeployments": 1, "solcInputHash": "979e67f27cb7019418365d40baa47672", "metadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_winnerMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_loserAppealPeriodMultiplier\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_homeProxy\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_homeChainId\",\"type\":\"uint256\"},{\"internalType\":\"contract IAMB\",\"name\":\"_amb\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Contribution\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"RulingFunded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_ruling\",\"type\":\"bytes32\"}],\"name\":\"RulingRelayed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_reward\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"META_EVIDENCE_ID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MULTIPLIER_DIVISOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REFUSE_TO_ARBITRATE_REALITIO\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"amb\",\"outputs\":[{\"internalType\":\"contract IAMB\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationCreatedBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignProxyGnosis.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"},{\"internalType\":\"uint256\",\"name\":\"disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"answer\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"disputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_externalDisputeID\",\"type\":\"uint256\"}],\"name\":\"externalIDtoLocalID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"fundAppeal\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"}],\"name\":\"getContributionsToSuccessfulFundings\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"contributions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"getFundingStatus\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"fullyFunded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMultipliers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"winner\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loser\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loserAppealPeriod\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"divisor\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"}],\"name\":\"getNumberOfRounds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"paidFees\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"feeRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"getTotalWithdrawableAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sum\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeChainId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserAppealPeriodMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"numberOfRulingOptions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"questionIDToArbitrationID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_evidenceURI\",\"type\":\"string\"}],\"name\":\"submitEvidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"winnerMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewardsForAllRounds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\",\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationCreated(bytes32,address,uint256)\":{\"params\":{\"_disputeID\":\"The ID of the dispute.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationFailed(bytes32,address)\":{\"details\":\"This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.\",\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationRequested(bytes32,address,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"Contribution(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contribution is made, inside fundAppeal function.\",\"params\":{\"_amount\":\"Contribution amount.\",\"_contributor\":\"Caller of fundAppeal function.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"The round number the contribution was made to.\",\"ruling\":\"Indicates the ruling option which got the contribution.\"}},\"Dispute(address,uint256,uint256,uint256)\":{\"details\":\"To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_evidenceGroupID\":\"Unique identifier of the evidence group that is linked to this dispute.\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Evidence(address,uint256,address,string)\":{\"details\":\"To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_evidence\":\"A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\",\"_evidenceGroupID\":\"Unique identifier of the evidence group the evidence belongs to.\",\"_party\":\"The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\"}},\"MetaEvidence(uint256,string)\":{\"details\":\"To be emitted when meta-evidence is submitted.\",\"params\":{\"_evidence\":\"A link to the meta-evidence JSON.\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Ruling(address,uint256,uint256)\":{\"details\":\"To be raised when a ruling is given.\",\"params\":{\"_arbitrator\":\"The arbitrator giving the ruling.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_ruling\":\"The ruling which was given.\"}},\"RulingFunded(uint256,uint256,uint256)\":{\"details\":\"To be raised when a ruling option is fully funded for appeal.\",\"params\":{\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"Number of the round this ruling option was fully funded in.\",\"_ruling\":\"The ruling option which just got fully funded.\"}},\"RulingRelayed(bytes32,bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question with the ruling to relay.\",\"_ruling\":\"Ruling converted into Realitio format.\"}},\"Withdrawal(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contributor withdraws non-zero value.\",\"params\":{\"_contributor\":\"The beneficiary of withdrawal.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_reward\":\"Total amount of withdrawal, consists of reimbursed deposits plus rewards.\",\"_round\":\"The round number the withdrawal was made from.\",\"_ruling\":\"Indicates the ruling option which contributor gets rewards from.\"}}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_amb\":\"ArbitraryMessageBridge contract address.\",\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_homeChainId\":\"The chain ID where the home proxy is deployed.\",\"_homeProxy\":\"The address of the proxy.\",\"_loserAppealPeriodMultiplier\":\"Multiplier for calculating the appeal period for the losing answer.\",\"_loserMultiplier\":\"Multiplier for calculating the appeal cost of the losing answer.\",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_winnerMultiplier\":\"Multiplier for calculating the appeal cost of the winning answer.\"}},\"externalIDtoLocalID(uint256)\":{\"params\":{\"_externalDisputeID\":\"Dispute id as in arbitrator side.\"},\"returns\":{\"_0\":\"localDisputeID Dispute id as in arbitrable contract.\"}},\"fundAppeal(uint256,uint256)\":{\"params\":{\"_answer\":\"One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.\",\"_arbitrationID\":\"The ID of the arbitration, which is questionID cast into uint256.\"},\"returns\":{\"_0\":\"Whether the answer was fully funded or not.\"}},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_contributor\":\"The address whose contributions to query.\",\"_round\":\"The round to query.\"},\"returns\":{\"contributions\":\"The amount contributed to each funded answer by the contributor.\",\"fundedAnswers\":\"IDs of the answers that are fully funded.\"}},\"getDisputeFee(bytes32)\":{\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"getFundingStatus(uint256,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer choice to get funding status for.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"fullyFunded\":\"Whether the answer is fully funded or not.\",\"raised\":\"The amount paid for this answer.\"}},\"getMultipliers()\":{\"returns\":{\"divisor\":\"Multiplier divisor.\",\"loser\":\"Losers stake multiplier.\",\"loserAppealPeriod\":\"Multiplier for calculating an appeal period duration for the losing side.\",\"winner\":\"Winners stake multiplier.\"}},\"getNumberOfRounds(uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\"},\"returns\":{\"_0\":\"The number of rounds.\"}},\"getRoundInfo(uint256,uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"feeRewards\":\"The amount of fees that will be used as rewards.\",\"fundedAnswers\":\"IDs of fully funded answers.\",\"paidFees\":\"The amount of fees paid for each fully funded answer.\"}},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The contributor for which to query.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"},\"returns\":{\"sum\":\"The total amount available to withdraw.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"numberOfRulingOptions(uint256)\":{\"returns\":{\"_0\":\"count The number of ruling options.\"}},\"questionIDToArbitrationID(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"},\"returns\":{\"_0\":\"The ID of the arbitration.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}},\"submitEvidence(uint256,string)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\",\"_evidenceURI\":\"Link to evidence.\"}},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer to query the reward from.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address to send reward to.\",\"_round\":\"The round from which to withdraw.\"},\"returns\":{\"reward\":\"The withdrawn amount.\"}},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address that made contributions.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"}}},\"title\":\"Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"},\"RulingRelayed(bytes32,bytes32)\":{\"notice\":\"Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain.\"},\"externalIDtoLocalID(uint256)\":{\"notice\":\"Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\"},\"fundAppeal(uint256,uint256)\":{\"notice\":\"Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\"},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"notice\":\"Gets contributions to the answers that are fully funded.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"getFundingStatus(uint256,uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question for a specific answer choice.\"},\"getMultipliers()\":{\"notice\":\"Returns stake multipliers.\"},\"getNumberOfRounds(uint256)\":{\"notice\":\"Gets the number of rounds of the specific question.\"},\"getRoundInfo(uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question.\"},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"notice\":\"Returns the sum of withdrawable amount.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created.\"},\"numberOfRulingOptions(uint256)\":{\"notice\":\"Returns number of possible ruling options. Valid rulings are [0, return value].\"},\"questionIDToArbitrationID(bytes32)\":{\"notice\":\"Casts question ID into uint256 thus returning the related arbitration ID.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute. Can only be called by the arbitrator.\"},\"submitEvidence(uint256,string)\":{\"notice\":\"Allows to submit evidence for a particular question.\"},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"notice\":\"Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\"},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"notice\":\"Allows to withdraw any rewards or reimbursable fees for all rounds at once.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/0.8/RealitioForeignProxyGnosis.sol\":\"RealitioForeignProxyGnosis\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/dispute-resolver-interface-contract-0.8/contracts/IDisputeResolver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@ferittuncer]\\n *  @reviewers: [@mtsalenc*, @hbarcelos*, @unknownunknown1, @MerlinEgalite, @fnanni-0*, @shalzz]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\n\\n/**\\n *  @title This serves as a standard interface for crowdfunded appeals and evidence submission, which aren't a part of the arbitration (erc-792 and erc-1497) standard yet.\\n    This interface is used in Dispute Resolver (resolve.kleros.io).\\n */\\nabstract contract IDisputeResolver is IArbitrable, IEvidence {\\n    string public constant VERSION = \\\"2.0.0\\\"; // Can be used to distinguish between multiple deployed versions, if necessary.\\n\\n    /** @dev Raised when a contribution is made, inside fundAppeal function.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the contribution was made to.\\n     *  @param ruling Indicates the ruling option which got the contribution.\\n     *  @param _contributor Caller of fundAppeal function.\\n     *  @param _amount Contribution amount.\\n     */\\n    event Contribution(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 ruling, address indexed _contributor, uint256 _amount);\\n\\n    /** @dev Raised when a contributor withdraws non-zero value.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the withdrawal was made from.\\n     *  @param _ruling Indicates the ruling option which contributor gets rewards from.\\n     *  @param _contributor The beneficiary of withdrawal.\\n     *  @param _reward Total amount of withdrawal, consists of reimbursed deposits plus rewards.\\n     */\\n    event Withdrawal(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 _ruling, address indexed _contributor, uint256 _reward);\\n\\n    /** @dev To be raised when a ruling option is fully funded for appeal.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round Number of the round this ruling option was fully funded in.\\n     *  @param _ruling The ruling option which just got fully funded.\\n     */\\n    event RulingFunded(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 indexed _ruling);\\n\\n    /** @dev Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     *  @param _externalDisputeID Dispute id as in arbitrator contract.\\n     *  @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external virtual returns (uint256 localDisputeID);\\n\\n    /** @dev Returns number of possible ruling options. Valid rulings are [0, return value].\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 _localDisputeID) external view virtual returns (uint256 count);\\n\\n    /** @dev Allows to submit evidence for a given dispute.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _evidenceURI IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    function submitEvidence(uint256 _localDisputeID, string calldata _evidenceURI) external virtual;\\n\\n    /** @dev Manages contributions and calls appeal function of the specified arbitrator to appeal a dispute. This function lets appeals be crowdfunded.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _ruling The ruling option to which the caller wants to contribute.\\n     *  @return fullyFunded True if the ruling option got fully funded as a result of this contribution.\\n     */\\n    function fundAppeal(uint256 _localDisputeID, uint256 _ruling) external payable virtual returns (bool fullyFunded);\\n\\n    /** @dev Returns appeal multipliers.\\n     *  @return winnerStakeMultiplier Winners stake multiplier.\\n     *  @return loserStakeMultiplier Losers stake multiplier.\\n     *  @return loserAppealPeriodMultiplier Losers appeal period multiplier. The loser is given less time to fund its appeal to defend against last minute appeal funding attacks.\\n     *  @return denominator Multiplier denominator in basis points.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        virtual\\n        returns (\\n            uint256 winnerStakeMultiplier,\\n            uint256 loserStakeMultiplier,\\n            uint256 loserAppealPeriodMultiplier,\\n            uint256 denominator\\n        );\\n\\n    /** @dev Allows to withdraw any reimbursable fees or rewards after the dispute gets resolved.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _round Number of the round that caller wants to execute withdraw on.\\n     *  @param _ruling A ruling option that caller wants to execute withdraw on.\\n     *  @return sum The amount that is going to be transferred to contributor as a result of this function call.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _round,\\n        uint256 _ruling\\n    ) external virtual returns (uint256 sum);\\n\\n    /** @dev Allows to withdraw any rewards or reimbursable fees after the dispute gets resolved for all rounds at once.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to execute withdraw on.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external virtual;\\n\\n    /** @dev Returns the sum of withdrawable amount.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to get withdrawable amount from.\\n     *  @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external view virtual returns (uint256 sum);\\n}\\n\",\"keccak256\":\"0x9174a37ba69e682381a3ae6e14582a17d69f29be879ff27433fce2b971f871ae\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0x1803a3433a78c509b20bd9477a2c60a71b2ce1ee7e17eb0ef0601618a8a72526\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {Waiting, Appealable, Solved}\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0x240a4142f9ec379da0333dfc82409b7b058cff9ea118368eb5e8f15447996c1e\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence A link to the meta-evidence JSON.\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0x1ccedf5213730632540c748486637d7b1977ee73375818bf498a8276ca49dd13\",\"license\":\"MIT\"},\"src/0.8/RealitioForeignProxyGnosis.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@hbarcelos*, @unknownunknown1]\\n *  @reviewers: [@jaybuidl]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity 0.8.25;\\n\\nimport {IDisputeResolver, IArbitrator} from \\\"@kleros/dispute-resolver-interface-contract-0.8/contracts/IDisputeResolver.sol\\\";\\nimport {IAMB} from \\\"./interfaces/gnosis/IAMB.sol\\\";\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./interfaces/IArbitrationProxies.sol\\\";\\n\\n/**\\n * @title Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\\n * @dev This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\\n */\\ncontract RealitioForeignProxyGnosis is IForeignArbitrationProxy, IDisputeResolver {\\n    /* Constants */\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max; // The number of choices for the arbitrator.\\n    uint256 public constant REFUSE_TO_ARBITRATE_REALITIO = type(uint256).max; // Constant that represents \\\"Refuse to rule\\\" in realitio format.\\n    uint256 public constant MULTIPLIER_DIVISOR = 10000; // Divisor parameter for multipliers.\\n    uint256 public constant META_EVIDENCE_ID = 0; // The ID of the MetaEvidence for disputes.\\n\\n    /* Storage */\\n\\n    enum Status {\\n        None,\\n        Requested,\\n        Created,\\n        Ruled,\\n        Failed\\n    }\\n\\n    struct ArbitrationRequest {\\n        Status status; // Status of the arbitration.\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\n        uint256 disputeID; // The ID of the dispute in arbitrator contract.\\n        uint256 answer; // The answer given by the arbitrator shifted by -1 to match Realitio format.\\n        Round[] rounds; // Tracks each appeal round of a dispute.\\n    }\\n\\n    struct DisputeDetails {\\n        uint256 arbitrationID; // The ID of the arbitration.\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\n    }\\n\\n    // Round struct stores the contributions made to particular answers.\\n    struct Round {\\n        mapping(uint256 => uint256) paidFees; // Tracks the fees paid in this round in the form paidFees[answer].\\n        mapping(uint256 => bool) hasPaid; // True if the fees for this particular answer have been fully paid in the form hasPaid[answer].\\n        mapping(address => mapping(uint256 => uint256)) contributions; // Maps contributors to their contributions for each answer in the form contributions[address][answer].\\n        uint256 feeRewards; // Sum of reimbursable appeal fees available to the parties that made contributions to the answer that ultimately wins a dispute.\\n        uint256[] fundedAnswers; // Stores the answer choices that are fully funded.\\n    }\\n\\n    IArbitrator public immutable arbitrator; // The address of the arbitrator. TRUSTED.\\n    bytes public arbitratorExtraData; // The extra data used to raise a dispute in the arbitrator.\\n\\n    IAMB public immutable amb; // ArbitraryMessageBridge contract address. TRUSTED.\\n    address public immutable homeProxy; // Address of the counter-party proxy on the Home Chain. TRUSTED.\\n    bytes32 public immutable homeChainId; // The chain ID where the home proxy is deployed.\\n\\n    // Multipliers are in basis points.\\n    uint256 public immutable winnerMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that was chosen by the arbitrator in the previous round.\\n    uint256 public immutable loserMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that the arbitrator didn't rule for in the previous round.\\n    uint256 public immutable loserAppealPeriodMultiplier; // Multiplier for calculating the duration of the appeal period for the loser, in basis points.\\n\\n    mapping(uint256 => mapping(address => ArbitrationRequest)) public arbitrationRequests; // Maps arbitration ID to its data. arbitrationRequests[uint(questionID)][requester].\\n    mapping(uint256 => DisputeDetails) public disputeIDToDisputeDetails; // Maps external dispute ids to local arbitration ID and requester who was able to complete the arbitration request.\\n    mapping(uint256 => bool) public arbitrationIDToDisputeExists; // Whether a dispute has already been created for the given arbitration ID or not.\\n    mapping(uint256 => address) public arbitrationIDToRequester; // Maps arbitration ID to the requester who was able to complete the arbitration request.\\n    mapping(uint256 => uint256) public arbitrationCreatedBlock; // Block of dispute creation. arbitrationCreatedBlock[disputeID]\\n\\n    /* Modifiers */\\n\\n    modifier onlyHomeProxy() {\\n        require(msg.sender == address(amb), \\\"Only AMB allowed\\\");\\n        require(amb.messageSourceChainId() == homeChainId, \\\"Only home chain allowed\\\");\\n        require(amb.messageSender() == homeProxy, \\\"Only home proxy allowed\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @notice Creates an arbitration proxy on the foreign chain.\\n     * @param _arbitrator Arbitrator contract address.\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\n     * @param _metaEvidence The URI of the meta evidence file.\\n     * @param _winnerMultiplier Multiplier for calculating the appeal cost of the winning answer.\\n     * @param _loserMultiplier Multiplier for calculating the appeal cost of the losing answer.\\n     * @param _loserAppealPeriodMultiplier Multiplier for calculating the appeal period for the losing answer.\\n     * @param _homeProxy The address of the proxy.\\n     * @param _homeChainId The chain ID where the home proxy is deployed.\\n     * @param _amb ArbitraryMessageBridge contract address.\\n     */\\n    constructor(\\n        IArbitrator _arbitrator,\\n        bytes memory _arbitratorExtraData,\\n        string memory _metaEvidence,\\n        uint256 _winnerMultiplier,\\n        uint256 _loserMultiplier,\\n        uint256 _loserAppealPeriodMultiplier,\\n        address _homeProxy,\\n        uint256 _homeChainId,\\n        IAMB _amb\\n    ) {\\n        arbitrator = _arbitrator;\\n        arbitratorExtraData = _arbitratorExtraData;\\n        winnerMultiplier = _winnerMultiplier;\\n        loserMultiplier = _loserMultiplier;\\n        loserAppealPeriodMultiplier = _loserAppealPeriodMultiplier;\\n        homeProxy = _homeProxy;\\n        homeChainId = bytes32(_homeChainId);\\n        amb = _amb;\\n\\n        emit MetaEvidence(META_EVIDENCE_ID, _metaEvidence);\\n    }\\n\\n    /* External and public */\\n\\n    // ************************ //\\n    // *    Realitio logic    * //\\n    // ************************ //\\n\\n    /**\\n     * @notice Requests arbitration for the given question and contested answer.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\n        require(!arbitrationIDToDisputeExists[uint256(_questionID)], \\\"Dispute already created\\\");\\n\\n        ArbitrationRequest storage arbitration = arbitrationRequests[uint256(_questionID)][msg.sender];\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\n\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\n        require(msg.value >= arbitrationCost, \\\"Deposit value too low\\\");\\n\\n        arbitration.status = Status.Requested;\\n        arbitration.deposit = uint248(msg.value);\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationRequest.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\n\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\n    }\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The requester.\\n     */\\n    function receiveArbitrationAcknowledgement(\\n        bytes32 _questionID,\\n        address _requester\\n    ) external override onlyHomeProxy {\\n        uint256 arbitrationID = uint256(_questionID);\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\n\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\n\\n        if (arbitration.deposit >= arbitrationCost) {\\n            try\\n                arbitrator.createDispute{value: arbitrationCost}(NUMBER_OF_CHOICES_FOR_ARBITRATOR, arbitratorExtraData)\\n            returns (uint256 disputeID) {\\n                DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[disputeID];\\n                disputeDetails.arbitrationID = arbitrationID;\\n                disputeDetails.requester = _requester;\\n\\n                arbitrationIDToDisputeExists[arbitrationID] = true;\\n                arbitrationIDToRequester[arbitrationID] = _requester;\\n                arbitrationCreatedBlock[disputeID] = block.number;\\n\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\n\\n                arbitration.status = Status.Created;\\n                arbitration.deposit = 0;\\n                arbitration.disputeID = disputeID;\\n                arbitration.rounds.push();\\n\\n                if (remainder > 0) {\\n                    payable(_requester).send(remainder);\\n                }\\n\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\n                emit Dispute(arbitrator, disputeID, META_EVIDENCE_ID, arbitrationID);\\n            } catch {\\n                arbitration.status = Status.Failed;\\n                emit ArbitrationFailed(_questionID, _requester);\\n            }\\n        } else {\\n            arbitration.status = Status.Failed;\\n            emit ArbitrationFailed(_questionID, _requester);\\n        }\\n    }\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external override onlyHomeProxy {\\n        uint256 arbitrationID = uint256(_questionID);\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\n        uint256 deposit = arbitration.deposit;\\n\\n        delete arbitrationRequests[arbitrationID][_requester];\\n        payable(_requester).send(deposit);\\n\\n        emit ArbitrationCanceled(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable override {\\n        uint256 arbitrationID = uint256(_questionID);\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\n        uint256 deposit = arbitration.deposit;\\n\\n        delete arbitrationRequests[arbitrationID][_requester];\\n        payable(_requester).send(deposit);\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationFailure.selector;\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\n\\n        emit ArbitrationCanceled(_questionID, _requester);\\n    }\\n\\n    // ********************************* //\\n    // *    Appeals and arbitration    * //\\n    // ********************************* //\\n\\n    /**\\n     * @notice Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\\n     * @param _arbitrationID The ID of the arbitration, which is questionID cast into uint256.\\n     * @param _answer One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question.\\n     * Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format.\\n     * Also note that '0' answer can be funded.\\n     * @return Whether the answer was fully funded or not.\\n     */\\n    function fundAppeal(uint256 _arbitrationID, uint256 _answer) external payable override returns (bool) {\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][\\n            arbitrationIDToRequester[_arbitrationID]\\n        ];\\n        require(arbitration.status == Status.Created, \\\"No dispute to appeal.\\\");\\n\\n        uint256 disputeID = arbitration.disputeID;\\n        (uint256 appealPeriodStart, uint256 appealPeriodEnd) = arbitrator.appealPeriod(disputeID);\\n        require(block.timestamp >= appealPeriodStart && block.timestamp < appealPeriodEnd, \\\"Appeal period is over.\\\");\\n\\n        uint256 multiplier;\\n        {\\n            uint256 winner = arbitrator.currentRuling(disputeID);\\n            if (winner == _answer) {\\n                multiplier = winnerMultiplier;\\n            } else {\\n                require(\\n                    block.timestamp - appealPeriodStart <\\n                        ((appealPeriodEnd - appealPeriodStart) * loserAppealPeriodMultiplier) / MULTIPLIER_DIVISOR,\\n                    \\\"Appeal period is over for loser\\\"\\n                );\\n                multiplier = loserMultiplier;\\n            }\\n        }\\n\\n        uint256 lastRoundID = arbitration.rounds.length - 1;\\n        Round storage round = arbitration.rounds[lastRoundID];\\n        require(!round.hasPaid[_answer], \\\"Appeal fee is already paid.\\\");\\n        uint256 appealCost = arbitrator.appealCost(disputeID, arbitratorExtraData);\\n        uint256 totalCost = appealCost + ((appealCost * multiplier) / MULTIPLIER_DIVISOR);\\n\\n        // Take up to the amount necessary to fund the current round at the current costs.\\n        uint256 contribution = totalCost - round.paidFees[_answer] > msg.value\\n            ? msg.value\\n            : totalCost - round.paidFees[_answer];\\n        emit Contribution(_arbitrationID, lastRoundID, _answer, msg.sender, contribution);\\n\\n        round.contributions[msg.sender][_answer] += contribution;\\n        round.paidFees[_answer] += contribution;\\n        if (round.paidFees[_answer] >= totalCost) {\\n            round.feeRewards += round.paidFees[_answer];\\n            round.fundedAnswers.push(_answer);\\n            round.hasPaid[_answer] = true;\\n            emit RulingFunded(_arbitrationID, lastRoundID, _answer);\\n        }\\n\\n        if (round.fundedAnswers.length > 1) {\\n            // At least two sides are fully funded.\\n            arbitration.rounds.push();\\n\\n            round.feeRewards = round.feeRewards - appealCost;\\n            arbitrator.appeal{value: appealCost}(disputeID, arbitratorExtraData);\\n        }\\n\\n        if (msg.value - contribution > 0) payable(msg.sender).send(msg.value - contribution); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\n        return round.hasPaid[_answer];\\n    }\\n\\n    /**\\n     * @notice Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _beneficiary The address to send reward to.\\n     * @param _round The round from which to withdraw.\\n     * @param _answer The answer to query the reward from.\\n     * @return reward The withdrawn amount.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _arbitrationID,\\n        address payable _beneficiary,\\n        uint256 _round,\\n        uint256 _answer\\n    ) public override returns (uint256 reward) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\n        // Allow to reimburse if funding of the round was unsuccessful.\\n        if (!round.hasPaid[_answer]) {\\n            reward = round.contributions[_beneficiary][_answer];\\n        } else if (!round.hasPaid[arbitration.answer]) {\\n            // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\n            // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\n            reward = round.fundedAnswers.length > 1\\n                ? (round.contributions[_beneficiary][_answer] * round.feeRewards) /\\n                    (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\n                : 0;\\n        } else if (arbitration.answer == _answer) {\\n            uint256 paidFees = round.paidFees[_answer];\\n            // Reward the winner.\\n            reward = paidFees > 0 ? (round.contributions[_beneficiary][_answer] * round.feeRewards) / paidFees : 0;\\n        }\\n\\n        if (reward != 0) {\\n            round.contributions[_beneficiary][_answer] = 0;\\n            _beneficiary.send(reward); // It is the user's responsibility to accept ETH.\\n            emit Withdrawal(_arbitrationID, _round, _answer, _beneficiary, reward);\\n        }\\n    }\\n\\n    /**\\n     * @notice Allows to withdraw any rewards or reimbursable fees for all rounds at once.\\n     * @dev This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.\\n     *      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _beneficiary The address that made contributions.\\n     * @param _contributedTo Answer that received contributions from contributor.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _arbitrationID,\\n        address payable _beneficiary,\\n        uint256 _contributedTo\\n    ) external override {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n\\n        uint256 numberOfRounds = arbitration.rounds.length;\\n        for (uint256 roundNumber = 0; roundNumber < numberOfRounds; roundNumber++) {\\n            withdrawFeesAndRewards(_arbitrationID, _beneficiary, roundNumber, _contributedTo);\\n        }\\n    }\\n\\n    /**\\n     * @notice Allows to submit evidence for a particular question.\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\n     * @param _evidenceURI Link to evidence.\\n     */\\n    function submitEvidence(uint256 _arbitrationID, string calldata _evidenceURI) external override {\\n        emit Evidence(arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\n    }\\n\\n    /**\\n     * @notice Rules a specified dispute. Can only be called by the arbitrator.\\n     * @dev Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\n     * @param _ruling The ruling given by the arbitrator.\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external override {\\n        DisputeDetails storage disputeDetails = disputeIDToDisputeDetails[_disputeID];\\n        uint256 arbitrationID = disputeDetails.arbitrationID;\\n        address requester = disputeDetails.requester;\\n\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][requester];\\n        require(msg.sender == address(arbitrator), \\\"Only arbitrator allowed\\\");\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\n        uint256 finalRuling = _ruling;\\n\\n        // If one side paid its fees, the ruling is in its favor. Note that if the other side had also paid, an appeal would have been created.\\n        Round storage round = arbitration.rounds[arbitration.rounds.length - 1];\\n        if (round.fundedAnswers.length == 1) finalRuling = round.fundedAnswers[0];\\n\\n        arbitration.answer = finalRuling;\\n        arbitration.status = Status.Ruled;\\n\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationAnswer.selector;\\n        // Realitio ruling is shifted by 1 compared to Kleros.\\n        uint256 realitioRuling = finalRuling != 0 ? finalRuling - 1 : REFUSE_TO_ARBITRATE_REALITIO;\\n\\n        bytes memory data = abi.encodeWithSelector(methodSelector, bytes32(arbitrationID), bytes32(realitioRuling));\\n        amb.requireToPassMessage(homeProxy, data, amb.maxGasPerTx());\\n\\n        emit Ruling(arbitrator, _disputeID, finalRuling);\\n    }\\n\\n    /* External Views */\\n\\n    /**\\n     * @notice Returns stake multipliers.\\n     * @return winner Winners stake multiplier.\\n     * @return loser Losers stake multiplier.\\n     * @return loserAppealPeriod Multiplier for calculating an appeal period duration for the losing side.\\n     * @return divisor Multiplier divisor.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        override\\n        returns (uint256 winner, uint256 loser, uint256 loserAppealPeriod, uint256 divisor)\\n    {\\n        return (winnerMultiplier, loserMultiplier, loserAppealPeriodMultiplier, MULTIPLIER_DIVISOR);\\n    }\\n\\n    /**\\n     * @notice Returns number of possible ruling options. Valid rulings are [0, return value].\\n     * @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 /* _arbitrationID */) external pure override returns (uint256) {\\n        return NUMBER_OF_CHOICES_FOR_ARBITRATOR;\\n    }\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 /* _questionID */) external view override returns (uint256) {\\n        return arbitrator.arbitrationCost(arbitratorExtraData);\\n    }\\n\\n    /**\\n     * @notice Gets the number of rounds of the specific question.\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\n     * @return The number of rounds.\\n     */\\n    function getNumberOfRounds(uint256 _arbitrationID) external view returns (uint256) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        return arbitration.rounds.length;\\n    }\\n\\n    /**\\n     * @notice Gets the information of a round of a question.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _round The round to query.\\n     * @return paidFees The amount of fees paid for each fully funded answer.\\n     * @return feeRewards The amount of fees that will be used as rewards.\\n     * @return fundedAnswers IDs of fully funded answers.\\n     */\\n    function getRoundInfo(\\n        uint256 _arbitrationID,\\n        uint256 _round\\n    ) external view returns (uint256[] memory paidFees, uint256 feeRewards, uint256[] memory fundedAnswers) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n        fundedAnswers = round.fundedAnswers;\\n\\n        paidFees = new uint256[](round.fundedAnswers.length);\\n\\n        for (uint256 i = 0; i < round.fundedAnswers.length; i++) {\\n            paidFees[i] = round.paidFees[round.fundedAnswers[i]];\\n        }\\n\\n        feeRewards = round.feeRewards;\\n    }\\n\\n    /**\\n     * @notice Gets the information of a round of a question for a specific answer choice.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _round The round to query.\\n     * @param _answer The answer choice to get funding status for.\\n     * @return raised The amount paid for this answer.\\n     * @return fullyFunded Whether the answer is fully funded or not.\\n     */\\n    function getFundingStatus(\\n        uint256 _arbitrationID,\\n        uint256 _round,\\n        uint256 _answer\\n    ) external view returns (uint256 raised, bool fullyFunded) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n\\n        raised = round.paidFees[_answer];\\n        fullyFunded = round.hasPaid[_answer];\\n    }\\n\\n    /**\\n     * @notice Gets contributions to the answers that are fully funded.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _round The round to query.\\n     * @param _contributor The address whose contributions to query.\\n     * @return fundedAnswers IDs of the answers that are fully funded.\\n     * @return contributions The amount contributed to each funded answer by the contributor.\\n     */\\n    function getContributionsToSuccessfulFundings(\\n        uint256 _arbitrationID,\\n        uint256 _round,\\n        address _contributor\\n    ) external view returns (uint256[] memory fundedAnswers, uint256[] memory contributions) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        Round storage round = arbitration.rounds[_round];\\n\\n        fundedAnswers = round.fundedAnswers;\\n        contributions = new uint256[](round.fundedAnswers.length);\\n\\n        for (uint256 i = 0; i < contributions.length; i++) {\\n            contributions[i] = round.contributions[_contributor][fundedAnswers[i]];\\n        }\\n    }\\n\\n    /**\\n     * @notice Returns the sum of withdrawable amount.\\n     * @dev This function is O(n) where n is the total number of rounds.\\n     * @dev This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\\n     * @param _arbitrationID The ID of the arbitration.\\n     * @param _beneficiary The contributor for which to query.\\n     * @param _contributedTo Answer that received contributions from contributor.\\n     * @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _arbitrationID,\\n        address payable _beneficiary,\\n        uint256 _contributedTo\\n    ) external view override returns (uint256 sum) {\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\n        if (arbitration.status < Status.Ruled) return sum;\\n\\n        uint256 finalAnswer = arbitration.answer;\\n        uint256 noOfRounds = arbitration.rounds.length;\\n        for (uint256 roundNumber = 0; roundNumber < noOfRounds; roundNumber++) {\\n            Round storage round = arbitration.rounds[roundNumber];\\n\\n            if (!round.hasPaid[_contributedTo]) {\\n                // Allow to reimburse if funding was unsuccessful for this answer option.\\n                sum += round.contributions[_beneficiary][_contributedTo];\\n            } else if (!round.hasPaid[finalAnswer]) {\\n                // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\n                // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\n                sum += round.fundedAnswers.length > 1\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) /\\n                        (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\n                    : 0;\\n            } else if (finalAnswer == _contributedTo) {\\n                uint256 paidFees = round.paidFees[_contributedTo];\\n                // Reward the winner.\\n                sum += paidFees > 0\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) / paidFees\\n                    : 0;\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @notice Casts question ID into uint256 thus returning the related arbitration ID.\\n     * @param _questionID The ID of the question.\\n     * @return The ID of the arbitration.\\n     */\\n    function questionIDToArbitrationID(bytes32 _questionID) external pure returns (uint256) {\\n        return uint256(_questionID);\\n    }\\n\\n    /**\\n     * @notice Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     * @param _externalDisputeID Dispute id as in arbitrator side.\\n     * @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external view override returns (uint256) {\\n        return disputeIDToDisputeDetails[_externalDisputeID].arbitrationID;\\n    }\\n}\\n\",\"keccak256\":\"0x24debc46881e45325f342113554422a53692cc8d91a5c76a93249cf402f922fc\",\"license\":\"MIT\"},\"src/0.8/interfaces/IArbitrationProxies.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity 0.8.25;\\n\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\n\\ninterface IHomeArbitrationProxy {\\n    /**\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\n     */\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice To be emitted when arbitration request is rejected.\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\n     * or if the question is already finalized.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\n     * @param _reason The reason why the request was rejected.\\n     */\\n    event RequestRejected(\\n        bytes32 indexed _questionID,\\n        address indexed _requester,\\n        uint256 _maxPrevious,\\n        string _reason\\n    );\\n\\n    /**\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the arbitration request is canceled.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\n\\n    /**\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\n     * @param _questionID The ID of the question.\\n     */\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\n\\n    /**\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function receiveArbitrationRequest(bytes32 _questionID, address _requester, uint256 _maxPrevious) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\n     * the bridge and cannot send messages back to it.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been rejected.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\n     * the bridge and cannot send messages back to it.\\n     * Reasons why the request might be rejected:\\n     *  - The question does not exist\\n     *  - The question was not answered yet\\n     *  - The quesiton bond value changed while the arbitration was being requested\\n     *  - Another request was already accepted\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\n     * @dev Currently this can happen only if the arbitration cost increased.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the answer to a specified question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\n\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\n     *  @dev Template_hashes won't be used by this home proxy. \\n     */\\n    function metadata() external view returns (string calldata);\\n}\\n\\ninterface IForeignArbitrationProxy {\\n    /**\\n     * @notice Should be emitted when the arbitration is requested.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice Should be emitted when the dispute is created.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _disputeID The ID of the dispute.\\n     */\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\n\\n    /**\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the dispute could not be created.\\n     * @dev This will happen if there is an increase in the arbitration fees\\n     * between the time the arbitration is made and the time it is acknowledged.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event.\\n     * @param _questionID The ID of the question with the ruling to relay.\\n     * @param _ruling Ruling converted into Realitio format.\\n     */\\n    event RulingRelayed(bytes32 _questionID, bytes32 _ruling);\\n\\n    /**\\n     * @notice Requests arbitration for the given question and contested answer.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable;\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @param _questionID the ID of the question.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\n}\\n\\n\",\"keccak256\":\"0x4f2dd0c7372274a2382fad70cc8165c7c970e4d859f604b8441ebfe091fa62e3\",\"license\":\"MIT\"},\"src/0.8/interfaces/gnosis/IAMB.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity 0.8.25;\\n\\ninterface IAMB {\\n    function requireToPassMessage(\\n        address _contract,\\n        bytes memory _data,\\n        uint256 _gas\\n    ) external returns (bytes32);\\n\\n    function maxGasPerTx() external view returns (uint256);\\n\\n    function messageSender() external view returns (address);\\n\\n    function messageSourceChainId() external view returns (bytes32);\\n\\n    function messageId() external view returns (bytes32);\\n}\\n\",\"keccak256\":\"0xcd491a322de6f7c33ba93ee2fc78c640d1c971610acf5102e5026d659c1e742f\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.", "events": {"ArbitrationCanceled(bytes32,address)": {"params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationCreated(bytes32,address,uint256)": {"params": {"_disputeID": "The ID of the dispute.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationFailed(bytes32,address)": {"details": "This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.", "params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationRequested(bytes32,address,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "Contribution(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contribution is made, inside fundAppeal function.", "params": {"_amount": "Contribution amount.", "_contributor": "Caller of fundAppeal function.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "The round number the contribution was made to.", "ruling": "Indicates the ruling option which got the contribution."}}, "Dispute(address,uint256,uint256,uint256)": {"details": "To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.", "params": {"_arbitrator": "The arbitrator of the contract.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_evidenceGroupID": "Unique identifier of the evidence group that is linked to this dispute.", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Evidence(address,uint256,address,string)": {"details": "To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).", "params": {"_arbitrator": "The arbitrator of the contract.", "_evidence": "A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.", "_evidenceGroupID": "Unique identifier of the evidence group the evidence belongs to.", "_party": "The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party."}}, "MetaEvidence(uint256,string)": {"details": "To be emitted when meta-evidence is submitted.", "params": {"_evidence": "A link to the meta-evidence JSON.", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Ruling(address,uint256,uint256)": {"details": "To be raised when a ruling is given.", "params": {"_arbitrator": "The arbitrator giving the ruling.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_ruling": "The ruling which was given."}}, "RulingFunded(uint256,uint256,uint256)": {"details": "To be raised when a ruling option is fully funded for appeal.", "params": {"_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "Number of the round this ruling option was fully funded in.", "_ruling": "The ruling option which just got fully funded."}}, "RulingRelayed(bytes32,bytes32)": {"params": {"_questionID": "The ID of the question with the ruling to relay.", "_ruling": "Ruling converted into Realitio format."}}, "Withdrawal(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contributor withdraws non-zero value.", "params": {"_contributor": "The beneficiary of withdrawal.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_reward": "Total amount of withdrawal, consists of reimbursed deposits plus rewards.", "_round": "The round number the withdrawal was made from.", "_ruling": "Indicates the ruling option which contributor gets rewards from."}}}, "kind": "dev", "methods": {"constructor": {"params": {"_amb": "ArbitraryMessageBridge contract address.", "_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_homeChainId": "The chain ID where the home proxy is deployed.", "_homeProxy": "The address of the proxy.", "_loserAppealPeriodMultiplier": "Multiplier for calculating the appeal period for the losing answer.", "_loserMultiplier": "Multiplier for calculating the appeal cost of the losing answer.", "_metaEvidence": "The URI of the meta evidence file.", "_winnerMultiplier": "Multiplier for calculating the appeal cost of the winning answer."}}, "externalIDtoLocalID(uint256)": {"params": {"_externalDisputeID": "Dispute id as in arbitrator side."}, "returns": {"_0": "localDisputeID Dispute id as in arbitrable contract."}}, "fundAppeal(uint256,uint256)": {"params": {"_answer": "One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.", "_arbitrationID": "The ID of the arbitration, which is questionID cast into uint256."}, "returns": {"_0": "Whether the answer was fully funded or not."}}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_contributor": "The address whose contributions to query.", "_round": "The round to query."}, "returns": {"contributions": "The amount contributed to each funded answer by the contributor.", "fundedAnswers": "IDs of the answers that are fully funded."}}, "getDisputeFee(bytes32)": {"returns": {"_0": "The fee to create a dispute."}}, "getFundingStatus(uint256,uint256,uint256)": {"params": {"_answer": "The answer choice to get funding status for.", "_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"fullyFunded": "Whether the answer is fully funded or not.", "raised": "The amount paid for this answer."}}, "getMultipliers()": {"returns": {"divisor": "Multiplier divisor.", "loser": "Losers stake multiplier.", "loserAppealPeriod": "Multiplier for calculating an appeal period duration for the losing side.", "winner": "Winners stake multiplier."}}, "getNumberOfRounds(uint256)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question."}, "returns": {"_0": "The number of rounds."}}, "getRoundInfo(uint256,uint256)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"feeRewards": "The amount of fees that will be used as rewards.", "fundedAnswers": "IDs of fully funded answers.", "paidFees": "The amount of fees paid for each fully funded answer."}}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The contributor for which to query.", "_contributedTo": "Answer that received contributions from contributor."}, "returns": {"sum": "The total amount available to withdraw."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "numberOfRulingOptions(uint256)": {"returns": {"_0": "count The number of ruling options."}}, "questionIDToArbitrationID(bytes32)": {"params": {"_questionID": "The ID of the question."}, "returns": {"_0": "The ID of the arbitration."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}, "submitEvidence(uint256,string)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question.", "_evidenceURI": "Link to evidence."}}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"params": {"_answer": "The answer to query the reward from.", "_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address to send reward to.", "_round": "The round from which to withdraw."}, "returns": {"reward": "The withdrawn amount."}}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address that made contributions.", "_contributedTo": "Answer that received contributions from contributor."}}}, "title": "Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}, "RulingRelayed(bytes32,bytes32)": {"notice": "Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the foreign chain."}, "externalIDtoLocalID(uint256)": {"notice": "Maps external (arbitrator side) dispute id to local (arbitrable) dispute id."}, "fundAppeal(uint256,uint256)": {"notice": "Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded."}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"notice": "Gets contributions to the answers that are fully funded."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "getFundingStatus(uint256,uint256,uint256)": {"notice": "Gets the information of a round of a question for a specific answer choice."}, "getMultipliers()": {"notice": "Returns stake multipliers."}, "getNumberOfRounds(uint256)": {"notice": "Gets the number of rounds of the specific question."}, "getRoundInfo(uint256,uint256)": {"notice": "Gets the information of a round of a question."}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"notice": "Returns the sum of withdrawable amount."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created."}, "numberOfRulingOptions(uint256)": {"notice": "Returns number of possible ruling options. Valid rulings are [0, return value]."}, "questionIDToArbitrationID(bytes32)": {"notice": "Casts question ID into uint256 thus returning the related arbitration ID."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute. Can only be called by the arbitrator."}, "submitEvidence(uint256,string)": {"notice": "Allows to submit evidence for a particular question."}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"notice": "Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner."}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"notice": "Allows to withdraw any rewards or reimbursable fees for all rounds at once."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 12219, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "arbitratorExtraData", "offset": 0, "slot": "0", "type": "t_bytes_storage"}, {"astId": 12239, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "arbitrationRequests", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)12189_storage))"}, {"astId": 12244, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "disputeIDToDisputeDetails", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_struct(DisputeDetails)12194_storage)"}, {"astId": 12248, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "arbitrationIDToDisputeExists", "offset": 0, "slot": "3", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 12252, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "arbitrationIDToRequester", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 12256, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "arbitrationCreatedBlock", "offset": 0, "slot": "5", "type": "t_mapping(t_uint256,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_struct(Round)12214_storage)dyn_storage": {"base": "t_struct(Round)12214_storage", "encoding": "dynamic_array", "label": "struct RealitioForeignProxyGnosis.Round[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_enum(Status)12175": {"encoding": "inplace", "label": "enum RealitioForeignProxyGnosis.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_uint256)"}, "t_mapping(t_address,t_struct(ArbitrationRequest)12189_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignProxyGnosis.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)12189_storage"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_uint256,t_bool)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)12189_storage))": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => mapping(address => struct RealitioForeignProxyGnosis.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)12189_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)12194_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignProxyGnosis.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)12194_storage"}, "t_mapping(t_uint256,t_uint256)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(ArbitrationRequest)12189_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyGnosis.ArbitrationRequest", "members": [{"astId": 12178, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)12175"}, {"astId": 12180, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}, {"astId": 12182, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "disputeID", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 12184, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "answer", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 12188, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "rounds", "offset": 0, "slot": "3", "type": "t_array(t_struct(Round)12214_storage)dyn_storage"}], "numberOfBytes": "128"}, "t_struct(DisputeDetails)12194_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyGnosis.DisputeDetails", "members": [{"astId": 12191, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "arbitrationID", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 12193, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_struct(Round)12214_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyGnosis.Round", "members": [{"astId": 12198, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "paidFees", "offset": 0, "slot": "0", "type": "t_mapping(t_uint256,t_uint256)"}, {"astId": 12202, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "hasPaid", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 12208, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "contributions", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))"}, {"astId": 12210, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "feeRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 12213, "contract": "src/0.8/RealitioForeignProxyGnosis.sol:RealitioForeignProxyGnosis", "label": "fundedAnswers", "offset": 0, "slot": "4", "type": "t_array(t_uint256)dyn_storage"}], "numberOfBytes": "160"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}
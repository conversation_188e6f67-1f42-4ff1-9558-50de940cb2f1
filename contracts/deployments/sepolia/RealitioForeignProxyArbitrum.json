{"address": "0x26222Ec1F548953a4fEaE4C5A216337E26A821F9", "abi": [{"inputs": [{"internalType": "address", "name": "_homeProxy", "type": "address"}, {"internalType": "address", "name": "_governor", "type": "address"}, {"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}, {"internalType": "address", "name": "_inbox", "type": "address"}, {"internalType": "uint256", "name": "_surplusAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_l2GasLimit", "type": "uint256"}, {"internalType": "uint256", "name": "_gasPriceBid", "type": "uint256"}, {"internalType": "string", "name": "_metaEvidence", "type": "string"}, {"internalType": "uint256[3]", "name": "_multipliers", "type": "uint256[3]"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}], "name": "ArbitrationCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "ArbitrationRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "Contribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}], "name": "Dispute", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_evidenceGroupID", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_party", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "Evidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_metaEvidenceID", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_evidence", "type": "string"}], "name": "MetaEvidence", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "ticketId", "type": "uint256"}], "name": "RetryableTicketCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "<PERSON>uling", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "RulingFunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_ruling", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "_localDisputeID", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "_round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_ruling", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "_contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "MULTIPLIER_DIVISOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "NUMBER_OF_CHOICES_FOR_ARBITRATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFUSE_TO_ARBITRATE_REALITIO", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationCreatedBlock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToDisputeExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitrationIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "arbitrationRequests", "outputs": [{"internalType": "enum RealitioForeignProxyArb.Status", "name": "status", "type": "uint8"}, {"internalType": "uint248", "name": "deposit", "type": "uint248"}, {"internalType": "uint256", "name": "disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "answer", "type": "uint256"}, {"internalType": "contract IArbitrator", "name": "arbitrator", "type": "address"}, {"internalType": "bytes", "name": "arbitratorExtraData", "type": "bytes"}, {"internalType": "uint256", "name": "metaEvidenceID", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitrator", "outputs": [{"internalType": "contract IArbitrator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arbitratorDisputeIDToDisputeDetails", "outputs": [{"internalType": "uint256", "name": "arbitrationID", "type": "uint256"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "arbitratorExtraData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IArbitrator", "name": "_arbitrator", "type": "address"}, {"internalType": "bytes", "name": "_arbitratorExtraData", "type": "bytes"}], "name": "changeArbitrator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_gasPriceBid", "type": "uint256"}], "name": "changeGasPriceBid", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_governor", "type": "address"}], "name": "changeGovernor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IInbox", "name": "_inbox", "type": "address"}], "name": "changeInbox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_l2GasLimit", "type": "uint256"}], "name": "changeL2GasLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_loserAppealPeriodMultiplier", "type": "uint256"}], "name": "changeLoserAppealPeriodMultiplier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_loserMultiplier", "type": "uint256"}], "name": "changeLoserMultiplier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_metaEvidence", "type": "string"}], "name": "changeMetaevidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_surplus", "type": "uint256"}], "name": "changeSurplus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_winnerMultiplier", "type": "uint256"}], "name": "changeWinnerMultiplier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_externalDisputeID", "type": "uint256"}], "name": "externalIDtoLocalID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "fundAppeal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "gasPriceBid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "address", "name": "_contributor", "type": "address"}], "name": "getContributionsToSuccessfulFundings", "outputs": [{"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "contributions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "getDisputeFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "getFundingStatus", "outputs": [{"internalType": "uint256", "name": "raised", "type": "uint256"}, {"internalType": "bool", "name": "fullyFunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMultipliers", "outputs": [{"internalType": "uint256", "name": "winner", "type": "uint256"}, {"internalType": "uint256", "name": "loser", "type": "uint256"}, {"internalType": "uint256", "name": "loserAppealPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "divisor", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}], "name": "getNumberOfRounds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256[]", "name": "paidFees", "type": "uint256[]"}, {"internalType": "uint256", "name": "feeRewards", "type": "uint256"}, {"internalType": "uint256[]", "name": "fundedAnswers", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "getTotalWithdrawableAmount", "outputs": [{"internalType": "uint256", "name": "sum", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleFailedDisputeCreation", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "homeProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "inbox", "outputs": [{"internalType": "contract IInbox", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "l2GasLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserAppealPeriodMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "loserMultiplier", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "metaEvidenceUpdates", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "numberOfRulingOptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "questionIDToArbitrationID", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationAcknowledgement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationCancelation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "relayRule", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "requestArbitration", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_disputeID", "type": "uint256"}, {"internalType": "uint256", "name": "_ruling", "type": "uint256"}], "name": "rule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "string", "name": "_evidenceURI", "type": "string"}], "name": "submitEvidence", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "surplusAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "winner<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_round", "type": "uint256"}, {"internalType": "uint256", "name": "_answer", "type": "uint256"}], "name": "withdrawFeesAndRewards", "outputs": [{"internalType": "uint256", "name": "reward", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_arbitrationID", "type": "uint256"}, {"internalType": "address payable", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_contributedTo", "type": "uint256"}], "name": "withdrawFeesAndRewardsForAllRounds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0xfa7278531bf2b43eb206730283ff66bde242023c8dd6d79a587ce4d5958e1394", "receipt": {"to": null, "from": "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "contractAddress": "0x26222Ec1F548953a4fEaE4C5A216337E26A821F9", "transactionIndex": 62, "gasUsed": "3763734", "logsBloom": "0x00000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000040000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000020000000000000000000000000000000000000000000000000000400000000000000", "blockHash": "0xd6b48eee44c878700b615a43a89400a2dd83145a7d16022dda45369462ee5a2f", "transactionHash": "0xfa7278531bf2b43eb206730283ff66bde242023c8dd6d79a587ce4d5958e1394", "logs": [{"transactionIndex": 62, "blockNumber": 7049606, "transactionHash": "0xfa7278531bf2b43eb206730283ff66bde242023c8dd6d79a587ce4d5958e1394", "address": "0x26222Ec1F548953a4fEaE4C5A216337E26A821F9", "topics": ["0x61606860eb6c87306811e2695215385101daab53bd6ab4e9f9049aead9363c7d", "0x0000000000000000000000000000000000000000000000000000000000000000"], "data": "0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000342f697066732f516d583475416763584a644c6966416d5a6a7436565950324c776a39317a5a334836444c46363859743164377072000000000000000000000000", "logIndex": 88, "blockHash": "0xd6b48eee44c878700b615a43a89400a2dd83145a7d16022dda45369462ee5a2f"}], "blockNumber": 7049606, "cumulativeGasUsed": "11126155", "status": 1, "byzantium": true}, "args": ["0x890deB4111F92fE9447e83aBEF1b754372d6770e", "0xbDFd060ac349aC8b041D89aC491c89A78b4930E4", "0x90992fb4E15ce0C59aEFfb376460Fda4Ee19C879", "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "0xaAe29B0366299461418F5324a79Afc425BE5ae21", "30000000000000000", 1500000, 1000000000, "/ipfs/QmX4uAgcXJdLifAmZjt6VYP2Lwj91zZ3H6DLF68Yt1d7pr", [3000, 7000, 5000]], "numDeployments": 5, "solcInputHash": "e6111e706c213639299ba0c5a2a55593", "metadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_homeProxy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_governor\",\"type\":\"address\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"_inbox\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_surplusAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_l2GasLimit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_gasPriceBid\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"},{\"internalType\":\"uint256[3]\",\"name\":\"_multipliers\",\"type\":\"uint256[3]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"}],\"name\":\"ArbitrationCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"ArbitrationRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Contribution\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"}],\"name\":\"Dispute\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_evidenceGroupID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_party\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"Evidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_metaEvidenceID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_evidence\",\"type\":\"string\"}],\"name\":\"MetaEvidence\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"ticketId\",\"type\":\"uint256\"}],\"name\":\"RetryableTicketCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"Ruling\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"RulingFunded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_ruling\",\"type\":\"bytes32\"}],\"name\":\"RulingRelayed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_localDisputeID\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_reward\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"MULTIPLIER_DIVISOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NUMBER_OF_CHOICES_FOR_ARBITRATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REFUSE_TO_ARBITRATE_REALITIO\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationCreatedBlock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToDisputeExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitrationIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"arbitrationRequests\",\"outputs\":[{\"internalType\":\"enum RealitioForeignProxyArb.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"uint248\",\"name\":\"deposit\",\"type\":\"uint248\"},{\"internalType\":\"uint256\",\"name\":\"disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"answer\",\"type\":\"uint256\"},{\"internalType\":\"contract IArbitrator\",\"name\":\"arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"arbitratorExtraData\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"metaEvidenceID\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitrator\",\"outputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"arbitratorDisputeIDToDisputeDetails\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"requester\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"arbitratorExtraData\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IArbitrator\",\"name\":\"_arbitrator\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_arbitratorExtraData\",\"type\":\"bytes\"}],\"name\":\"changeArbitrator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_gasPriceBid\",\"type\":\"uint256\"}],\"name\":\"changeGasPriceBid\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_governor\",\"type\":\"address\"}],\"name\":\"changeGovernor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IInbox\",\"name\":\"_inbox\",\"type\":\"address\"}],\"name\":\"changeInbox\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_l2GasLimit\",\"type\":\"uint256\"}],\"name\":\"changeL2GasLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_loserAppealPeriodMultiplier\",\"type\":\"uint256\"}],\"name\":\"changeLoserAppealPeriodMultiplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_loserMultiplier\",\"type\":\"uint256\"}],\"name\":\"changeLoserMultiplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_metaEvidence\",\"type\":\"string\"}],\"name\":\"changeMetaevidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_surplus\",\"type\":\"uint256\"}],\"name\":\"changeSurplus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_winnerMultiplier\",\"type\":\"uint256\"}],\"name\":\"changeWinnerMultiplier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_externalDisputeID\",\"type\":\"uint256\"}],\"name\":\"externalIDtoLocalID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"fundAppeal\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"gasPriceBid\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_contributor\",\"type\":\"address\"}],\"name\":\"getContributionsToSuccessfulFundings\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"contributions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"getDisputeFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"getFundingStatus\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"raised\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"fullyFunded\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMultipliers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"winner\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loser\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"loserAppealPeriod\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"divisor\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"}],\"name\":\"getNumberOfRounds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"paidFees\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"feeRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"fundedAnswers\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"getTotalWithdrawableAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sum\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleFailedDisputeCreation\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"homeProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"inbox\",\"outputs\":[{\"internalType\":\"contract IInbox\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"l2GasLimit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserAppealPeriodMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"loserMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"metaEvidenceUpdates\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"numberOfRulingOptions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"questionIDToArbitrationID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationAcknowledgement\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationCancelation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"relayRule\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"requestArbitration\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_disputeID\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_ruling\",\"type\":\"uint256\"}],\"name\":\"rule\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_evidenceURI\",\"type\":\"string\"}],\"name\":\"submitEvidence\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"surplusAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"winnerMultiplier\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_round\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_answer\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_arbitrationID\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"_beneficiary\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_contributedTo\",\"type\":\"uint256\"}],\"name\":\"withdrawFeesAndRewardsForAllRounds\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract is meant to be deployed to the Ethereum chains where Kleros is deployed. Example https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/ethereum/GreeterL1.sol\",\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationCreated(bytes32,address,uint256)\":{\"params\":{\"_disputeID\":\"The ID of the dispute.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationFailed(bytes32,address)\":{\"details\":\"This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.\",\"params\":{\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationRequested(bytes32,address,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question with the request for arbitration.\",\"_requester\":\"The address of the arbitration requester.\"}},\"Contribution(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contribution is made, inside fundAppeal function.\",\"params\":{\"_amount\":\"Contribution amount.\",\"_contributor\":\"Caller of fundAppeal function.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"The round number the contribution was made to.\",\"ruling\":\"Indicates the ruling option which got the contribution.\"}},\"Dispute(address,uint256,uint256,uint256)\":{\"details\":\"To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_evidenceGroupID\":\"Unique identifier of the evidence group that is linked to this dispute.\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Evidence(address,uint256,address,string)\":{\"details\":\"To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\",\"params\":{\"_arbitrator\":\"The arbitrator of the contract.\",\"_evidence\":\"IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\",\"_evidenceGroupID\":\"Unique identifier of the evidence group the evidence belongs to.\",\"_party\":\"The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\"}},\"MetaEvidence(uint256,string)\":{\"details\":\"To be emitted when meta-evidence is submitted.\",\"params\":{\"_evidence\":\"IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'\",\"_metaEvidenceID\":\"Unique identifier of meta-evidence.\"}},\"Ruling(address,uint256,uint256)\":{\"details\":\"To be raised when a ruling is given.\",\"params\":{\"_arbitrator\":\"The arbitrator giving the ruling.\",\"_disputeID\":\"ID of the dispute in the Arbitrator contract.\",\"_ruling\":\"The ruling which was given.\"}},\"RulingFunded(uint256,uint256,uint256)\":{\"details\":\"To be raised when a ruling option is fully funded for appeal.\",\"params\":{\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_round\":\"Number of the round this ruling option was fully funded in.\",\"_ruling\":\"The ruling option which just got fully funded.\"}},\"RulingRelayed(bytes32,bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question with the ruling to relay.\",\"_ruling\":\"Ruling converted into Realitio format.\"}},\"Withdrawal(uint256,uint256,uint256,address,uint256)\":{\"details\":\"Raised when a contributor withdraws non-zero value.\",\"params\":{\"_contributor\":\"The beneficiary of withdrawal.\",\"_localDisputeID\":\"Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\",\"_reward\":\"Total amount of withdrawal, consists of reimbursed deposits plus rewards.\",\"_round\":\"The round number the withdrawal was made from.\",\"_ruling\":\"Indicates the ruling option which contributor gets rewards from.\"}}},\"kind\":\"dev\",\"methods\":{\"changeArbitrator(address,bytes)\":{\"params\":{\"_arbitrator\":\"New arbitrator address.\",\"_arbitratorExtraData\":\"Extradata for the arbitrator \"}},\"changeGasPriceBid(uint256)\":{\"params\":{\"_gasPriceBid\":\"New L2 gas price bid.\"}},\"changeGovernor(address)\":{\"params\":{\"_governor\":\"New governor address.\"}},\"changeInbox(address)\":{\"params\":{\"_inbox\":\"New inbox address.\"}},\"changeL2GasLimit(uint256)\":{\"params\":{\"_l2GasLimit\":\"New L2 gas limit.\"}},\"changeLoserAppealPeriodMultiplier(uint256)\":{\"params\":{\"_loserAppealPeriodMultiplier\":\"New loser multiplier for appeal period.\"}},\"changeLoserMultiplier(uint256)\":{\"params\":{\"_loserMultiplier\":\"New loser multiplier.\"}},\"changeMetaevidence(string)\":{\"params\":{\"_metaEvidence\":\"Metaevidence URI.\"}},\"changeSurplus(uint256)\":{\"params\":{\"_surplus\":\"New surplus value.\"}},\"changeWinnerMultiplier(uint256)\":{\"params\":{\"_winnerMultiplier\":\"New winner multiplier.\"}},\"constructor\":{\"params\":{\"_arbitrator\":\"Arbitrator contract address.\",\"_arbitratorExtraData\":\"The extra data used to raise a dispute in the arbitrator.\",\"_gasPriceBid\":\"Max gas price L2.\",\"_governor\":\"Governor of the contract.\",\"_homeProxy\":\"Proxy on L2.\",\"_inbox\":\"L2 inbox.\",\"_l2GasLimit\":\"L2 gas limit \",\"_metaEvidence\":\"The URI of the meta evidence file.\",\"_multipliers\":\"Appeal multipliers:  - Multiplier for calculating the appeal cost of the winning answer.  - Multiplier for calculating the appeal cost of the losing answer.  - Multiplier for calculating the appeal period for the losing answer.\",\"_surplusAmount\":\"The surplus amount to cover Arbitrum fees.\"}},\"externalIDtoLocalID(uint256)\":{\"params\":{\"_externalDisputeID\":\"Dispute id as in arbitrator side.\"},\"returns\":{\"_0\":\"localDisputeID Dispute id as in arbitrable contract.\"}},\"fundAppeal(uint256,uint256)\":{\"params\":{\"_answer\":\"One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.\",\"_arbitrationID\":\"The ID of the arbitration, which is questionID cast into uint256.\"},\"returns\":{\"_0\":\"Whether the answer was fully funded or not.\"}},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_contributor\":\"The address whose contributions to query.\",\"_round\":\"The round to query.\"},\"returns\":{\"contributions\":\"The amount contributed to each funded answer by the contributor.\",\"fundedAnswers\":\"IDs of the answers that are fully funded.\"}},\"getDisputeFee(bytes32)\":{\"returns\":{\"_0\":\"The fee to create a dispute.\"}},\"getFundingStatus(uint256,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer choice to get funding status for.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"fullyFunded\":\"Whether the answer is fully funded or not.\",\"raised\":\"The amount paid for this answer.\"}},\"getMultipliers()\":{\"returns\":{\"divisor\":\"Multiplier divisor.\",\"loser\":\"Losers stake multiplier.\",\"loserAppealPeriod\":\"Multiplier for calculating an appeal period duration for the losing side.\",\"winner\":\"Winners stake multiplier.\"}},\"getNumberOfRounds(uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\"},\"returns\":{\"_0\":\"The number of rounds.\"}},\"getRoundInfo(uint256,uint256)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_round\":\"The round to query.\"},\"returns\":{\"feeRewards\":\"The amount of fees that will be used as rewards.\",\"fundedAnswers\":\"IDs of fully funded answers.\",\"paidFees\":\"The amount of fees paid for each fully funded answer.\"}},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The contributor for which to query.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"},\"returns\":{\"sum\":\"The total amount available to withdraw.\"}},\"handleFailedDisputeCreation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"numberOfRulingOptions(uint256)\":{\"returns\":{\"_0\":\"count The number of ruling options.\"}},\"questionIDToArbitrationID(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"},\"returns\":{\"_0\":\"The ID of the arbitration.\"}},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"receiveArbitrationCancelation(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The requester.\"}},\"relayRule(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"requestArbitration(bytes32,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\",\"_questionID\":\"The ID of the question.\"}},\"rule(uint256,uint256)\":{\"details\":\"Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\",\"params\":{\"_disputeID\":\"The ID of the dispute in the ERC792 arbitrator.\",\"_ruling\":\"The ruling given by the arbitrator.\"}},\"submitEvidence(uint256,string)\":{\"params\":{\"_arbitrationID\":\"The ID of the arbitration related to the question.\",\"_evidenceURI\":\"Link to evidence.\"}},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"params\":{\"_answer\":\"The answer to query the reward from.\",\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address to send reward to.\",\"_round\":\"The round from which to withdraw.\"},\"returns\":{\"reward\":\"The withdrawn amount.\"}},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"details\":\"This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\",\"params\":{\"_arbitrationID\":\"The ID of the arbitration.\",\"_beneficiary\":\"The address that made contributions.\",\"_contributedTo\":\"Answer that received contributions from contributor.\"}}},\"title\":\"Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationCanceled(bytes32,address)\":{\"notice\":\"Should be emitted when the arbitration is canceled by the Home Chain.\"},\"ArbitrationCreated(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the dispute is created.\"},\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"Should be emitted when the dispute could not be created.\"},\"ArbitrationRequested(bytes32,address,uint256)\":{\"notice\":\"Should be emitted when the arbitration is requested.\"},\"RulingRelayed(bytes32,bytes32)\":{\"notice\":\"Should be emitted when the ruling is relayed to home proxy.\"}},\"kind\":\"user\",\"methods\":{\"changeArbitrator(address,bytes)\":{\"notice\":\"Changes the arbitrator and extradata. The arbitrator is trusted to support appeal period and not reenter. Note avoid changing arbitrator if there is an active arbitration request in Requested phase, otherwise evidence submitted during this phase will be submitted to the new arbitrator, while arbitration request will be processed by the old one. \"},\"changeGasPriceBid(uint256)\":{\"notice\":\"Changes the L2 gas price bid value.\"},\"changeGovernor(address)\":{\"notice\":\"Changes the governor of the contract.\"},\"changeInbox(address)\":{\"notice\":\"Changes the address of Arbitrum inbox contract. Note avoid changing the address if there is an active L2->L1 message being processed since the new inbox will most likely reference a new bridge address thus making onlyL2Bridge modifier fail.\"},\"changeL2GasLimit(uint256)\":{\"notice\":\"Changes the L2 gas limit value.\"},\"changeLoserAppealPeriodMultiplier(uint256)\":{\"notice\":\"Changes loser multiplier for appeal period.\"},\"changeLoserMultiplier(uint256)\":{\"notice\":\"Changes loser multiplier value.\"},\"changeMetaevidence(string)\":{\"notice\":\"Updates the meta evidence used for disputes.\"},\"changeSurplus(uint256)\":{\"notice\":\"Changes the surplus amount to cover the arbitrum fees.\"},\"changeWinnerMultiplier(uint256)\":{\"notice\":\"Changes winner multiplier value.\"},\"constructor\":{\"notice\":\"Creates an arbitration proxy on the foreign chain (L1).\"},\"externalIDtoLocalID(uint256)\":{\"notice\":\"Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\"},\"fundAppeal(uint256,uint256)\":{\"notice\":\"Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\"},\"getContributionsToSuccessfulFundings(uint256,uint256,address)\":{\"notice\":\"Gets contributions to the answers that are fully funded.\"},\"getDisputeFee(bytes32)\":{\"notice\":\"Gets the fee to create a dispute.\"},\"getFundingStatus(uint256,uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question for a specific answer choice.\"},\"getMultipliers()\":{\"notice\":\"Returns stake multipliers.\"},\"getNumberOfRounds(uint256)\":{\"notice\":\"Gets the number of rounds of the specific question.\"},\"getRoundInfo(uint256,uint256)\":{\"notice\":\"Gets the information of a round of a question.\"},\"getTotalWithdrawableAmount(uint256,address,uint256)\":{\"notice\":\"Returns the sum of withdrawable amount.\"},\"handleFailedDisputeCreation(bytes32,address)\":{\"notice\":\"Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover arbitrum fees.\"},\"numberOfRulingOptions(uint256)\":{\"notice\":\"Returns number of possible ruling options. Valid rulings are [0, return value].\"},\"questionIDToArbitrationID(bytes32)\":{\"notice\":\"Casts question ID into uint256 thus returning the related arbitration ID.\"},\"receiveArbitrationAcknowledgement(bytes32,address)\":{\"notice\":\"Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\"},\"receiveArbitrationCancelation(bytes32,address)\":{\"notice\":\"Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\"},\"relayRule(bytes32,address)\":{\"notice\":\"Relays the ruling to home proxy. Requires a small deposit to cover arbitrum fees.\"},\"requestArbitration(bytes32,uint256)\":{\"notice\":\"Requests arbitration for the given question and contested answer.\"},\"rule(uint256,uint256)\":{\"notice\":\"Rules a specified dispute. Can only be called by the arbitrator.\"},\"submitEvidence(uint256,string)\":{\"notice\":\"Allows to submit evidence for a particular question.\"},\"withdrawFeesAndRewards(uint256,address,uint256,uint256)\":{\"notice\":\"Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\"},\"withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)\":{\"notice\":\"Allows to withdraw any rewards or reimbursable fees for all rounds at once.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/RealitioForeignProxyArb.sol\":\"RealitioForeignProxyArb\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/dispute-resolver-interface-contract/contracts/IDisputeResolver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@ferittuncer]\\n *  @reviewers: [@mtsalenc*, @hbarcelos*, @unknownunknown1, @MerlinEgalite, @fnanni-0*, @shalzz]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\nimport \\\"@kleros/erc-792/contracts/IArbitrator.sol\\\";\\n\\n/**\\n *  @title This serves as a standard interface for crowdfunded appeals and evidence submission, which aren't a part of the arbitration (erc-792 and erc-1497) standard yet.\\n    This interface is used in Dispute Resolver (resolve.kleros.io).\\n */\\nabstract contract IDisputeResolver is IArbitrable, IEvidence {\\n    string public constant VERSION = \\\"2.0.0\\\"; // Can be used to distinguish between multiple deployed versions, if necessary.\\n\\n    /** @dev Raised when a contribution is made, inside fundAppeal function.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the contribution was made to.\\n     *  @param ruling Indicates the ruling option which got the contribution.\\n     *  @param _contributor Caller of fundAppeal function.\\n     *  @param _amount Contribution amount.\\n     */\\n    event Contribution(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 ruling, address indexed _contributor, uint256 _amount);\\n\\n    /** @dev Raised when a contributor withdraws non-zero value.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round The round number the withdrawal was made from.\\n     *  @param _ruling Indicates the ruling option which contributor gets rewards from.\\n     *  @param _contributor The beneficiary of withdrawal.\\n     *  @param _reward Total amount of withdrawal, consists of reimbursed deposits plus rewards.\\n     */\\n    event Withdrawal(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 _ruling, address indexed _contributor, uint256 _reward);\\n\\n    /** @dev To be raised when a ruling option is fully funded for appeal.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _round Number of the round this ruling option was fully funded in.\\n     *  @param _ruling The ruling option which just got fully funded.\\n     */\\n    event RulingFunded(uint256 indexed _localDisputeID, uint256 indexed _round, uint256 indexed _ruling);\\n\\n    /** @dev Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\n     *  @param _externalDisputeID Dispute id as in arbitrator contract.\\n     *  @return localDisputeID Dispute id as in arbitrable contract.\\n     */\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external virtual returns (uint256 localDisputeID);\\n\\n    /** @dev Returns number of possible ruling options. Valid rulings are [0, return value].\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @return count The number of ruling options.\\n     */\\n    function numberOfRulingOptions(uint256 _localDisputeID) external view virtual returns (uint256 count);\\n\\n    /** @dev Allows to submit evidence for a given dispute.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _evidenceURI IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    function submitEvidence(uint256 _localDisputeID, string calldata _evidenceURI) external virtual;\\n\\n    /** @dev Manages contributions and calls appeal function of the specified arbitrator to appeal a dispute. This function lets appeals be crowdfunded.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _ruling The ruling option to which the caller wants to contribute.\\n     *  @return fullyFunded True if the ruling option got fully funded as a result of this contribution.\\n     */\\n    function fundAppeal(uint256 _localDisputeID, uint256 _ruling) external payable virtual returns (bool fullyFunded);\\n\\n    /** @dev Returns appeal multipliers.\\n     *  @return winnerStakeMultiplier Winners stake multiplier.\\n     *  @return loserStakeMultiplier Losers stake multiplier.\\n     *  @return loserAppealPeriodMultiplier Losers appeal period multiplier. The loser is given less time to fund its appeal to defend against last minute appeal funding attacks.\\n     *  @return denominator Multiplier denominator in basis points.\\n     */\\n    function getMultipliers()\\n        external\\n        view\\n        virtual\\n        returns (\\n            uint256 winnerStakeMultiplier,\\n            uint256 loserStakeMultiplier,\\n            uint256 loserAppealPeriodMultiplier,\\n            uint256 denominator\\n        );\\n\\n    /** @dev Allows to withdraw any reimbursable fees or rewards after the dispute gets resolved.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _round Number of the round that caller wants to execute withdraw on.\\n     *  @param _ruling A ruling option that caller wants to execute withdraw on.\\n     *  @return sum The amount that is going to be transferred to contributor as a result of this function call.\\n     */\\n    function withdrawFeesAndRewards(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _round,\\n        uint256 _ruling\\n    ) external virtual returns (uint256 sum);\\n\\n    /** @dev Allows to withdraw any rewards or reimbursable fees after the dispute gets resolved for all rounds at once.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to execute withdraw on.\\n     */\\n    function withdrawFeesAndRewardsForAllRounds(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external virtual;\\n\\n    /** @dev Returns the sum of withdrawable amount.\\n     *  @param _localDisputeID Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.\\n     *  @param _contributor Beneficiary of withdraw operation.\\n     *  @param _ruling Ruling option that caller wants to get withdrawable amount from.\\n     *  @return sum The total amount available to withdraw.\\n     */\\n    function getTotalWithdrawableAmount(\\n        uint256 _localDisputeID,\\n        address payable _contributor,\\n        uint256 _ruling\\n    ) external view virtual returns (uint256 sum);\\n}\\n\",\"keccak256\":\"0x9174a37ba69e682381a3ae6e14582a17d69f29be879ff27433fce2b971f871ae\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0xf1a2c2d7ec1237ef8d3c5f580ac73f56ed58fe4d023817a188363885b3eeb9f2\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {\\n        Waiting,\\n        Appealable,\\n        Solved\\n    }\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0xfd19582446ef635cfb02a035a18efae3bc242ccf1472bb9949cad3d291306333\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0xf9f105a2cbf5e34cdc5ce71d877cded1b502437f1cd6d28173898f88542418af\",\"license\":\"MIT\"},\"src/RealitioForeignProxyArb.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\n\\r\\n/**\\r\\n *  @authors: [@unknownunknown1]\\r\\n *  @reviewers: []\\r\\n *  @auditors: []\\r\\n *  @bounties: []\\r\\n *  @deployments: []\\r\\n */\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\nimport {IDisputeResolver, IArbitrator} from \\\"@kleros/dispute-resolver-interface-contract/contracts/IDisputeResolver.sol\\\";\\r\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./interfaces/ArbitrationProxyInterfaces.sol\\\";\\r\\nimport \\\"./interfaces/IInbox.sol\\\";\\r\\nimport \\\"./interfaces/IOutbox.sol\\\";\\r\\n\\r\\n/**\\r\\n * @title Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).\\r\\n * @dev This contract is meant to be deployed to the Ethereum chains where Kleros is deployed.\\r\\n * Example https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/ethereum/GreeterL1.sol\\r\\n */\\r\\ncontract RealitioForeignProxyArb is IForeignArbitrationProxy, IDisputeResolver {\\r\\n    /* Constants */\\r\\n    uint256 public constant NUMBER_OF_CHOICES_FOR_ARBITRATOR = type(uint256).max; // The number of choices for the arbitrator.\\r\\n    uint256 public constant REFUSE_TO_ARBITRATE_REALITIO = type(uint256).max; // Constant that represents \\\"Refuse to rule\\\" in realitio format.\\r\\n    uint256 public constant MULTIPLIER_DIVISOR = 10000; // Divisor parameter for multipliers.\\r\\n    uint256 private constant L2_CALL_VALUE = 0; // The msg.value for L2 tx. Always 0.\\r\\n    uint256 private constant BLOCK_BASE_FEE = 0; // Block baseFee is set to 0 to use current block's baseFee.\\r\\n\\r\\n    /* Storage */\\r\\n\\r\\n    enum Status {\\r\\n        None,\\r\\n        Requested,\\r\\n        Created,\\r\\n        Ruled,\\r\\n        Relayed,\\r\\n        Failed\\r\\n    }\\r\\n\\r\\n    struct ArbitrationRequest {\\r\\n        Status status; // Status of the arbitration.\\r\\n        uint248 deposit; // The deposit paid by the requester at the time of the arbitration.\\r\\n        uint256 disputeID; // The ID of the dispute in arbitrator contract.\\r\\n        uint256 answer; // The answer given by the arbitrator.\\r\\n        Round[] rounds; // Tracks each appeal round of a dispute.\\r\\n        IArbitrator arbitrator; // The arbitrator trusted to solve disputes for this request.\\r\\n        bytes arbitratorExtraData; // The extra data for the trusted arbitrator of this request.\\r\\n        uint256 metaEvidenceID; // The meta evidence to be used in a dispute for this case.\\r\\n    }\\r\\n\\r\\n    struct DisputeDetails {\\r\\n        uint256 arbitrationID; // The ID of the arbitration.\\r\\n        address requester; // The address of the requester who managed to go through with the arbitration request.\\r\\n    }\\r\\n\\r\\n    // Round struct stores the contributions made to particular answers.\\r\\n    struct Round {\\r\\n        mapping(uint256 => uint256) paidFees; // Tracks the fees paid in this round in the form paidFees[answer].\\r\\n        mapping(uint256 => bool) hasPaid; // True if the fees for this particular answer have been fully paid in the form hasPaid[answer].\\r\\n        mapping(address => mapping(uint256 => uint256)) contributions; // Maps contributors to their contributions for each answer in the form contributions[address][answer].\\r\\n        uint256 feeRewards; // Sum of reimbursable appeal fees available to the parties that made contributions to the answer that ultimately wins a dispute.\\r\\n        uint256[] fundedAnswers; // Stores the answer choices that are fully funded.\\r\\n    }\\r\\n\\r\\n    address public immutable homeProxy; // Proxy on L2.\\r\\n\\r\\n    address public governor; // Governor of the contract (e.g KlerosGovernor).\\r\\n    IArbitrator public arbitrator; // The address of the arbitrator. TRUSTED.\\r\\n    bytes public arbitratorExtraData; // The extra data used to raise a dispute in the arbitrator.\\r\\n    uint256 public metaEvidenceUpdates; // The number of times the meta evidence has been updated. Used to track the latest meta evidence ID.\\r\\n    \\r\\n    IInbox public inbox; // Arbitrum inbox contract.\\r\\n    // Note that setting gasPriceBid to 0 will result in immediate revert on L1.\\r\\n    // If the values are set too low the tx won't redeed itself automatically on L2. The deposit will be reimbursed and manual redeem will be activated.\\r\\n    // It can be done here https://retryable-dashboard.arbitrum.io/tx\\r\\n    // Preferred values for gasLimit and gasPriceBid are 1500000 and 1000000000 respectively. This values are greatly higher than required amount to ensure automatic redeem.\\r\\n    uint256 public l2GasLimit; // Gas limit for tx on L2.\\r\\n    uint256 public gasPriceBid; // Gas price bid for tx on L2.\\r\\n\\r\\n    // The amount to add to arbitration fees to cover for Arbitrum fees. The leftover will be reimbursed. This is required for Realtio UI.\\r\\n    // Surplus amount covers submission cost for retryable ticket on L1 + gasLimit * gasPriceBid.\\r\\n    // Submission cost is based on the length of the passed message and current gas fees. It's usually greatly lower than 0.05 but it's preferred to use this value\\r\\n    // to account for potential gas fee spikes. It shouldn't be an issue since 0.05 is a relatively low value compared to Kleros arbitration cost\\r\\n    // and the leftover will be reimbursed anyway.\\r\\n    uint256 public surplusAmount; \\r\\n\\r\\n    // Multipliers are in basis points.\\r\\n    uint256 public winnerMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that was chosen by the arbitrator in the previous round.\\r\\n    uint256 public loserMultiplier; // Multiplier for calculating the appeal fee that must be paid for the answer that the arbitrator didn't rule for in the previous round.\\r\\n    uint256 public loserAppealPeriodMultiplier; // Multiplier for calculating the duration of the appeal period for the loser, in basis points.\\r\\n\\r\\n    mapping(uint256 => mapping(address => ArbitrationRequest)) public arbitrationRequests; // Maps arbitration ID to its data. arbitrationRequests[uint(questionID)][requester].\\r\\n    mapping(address => mapping(uint256 => DisputeDetails)) public arbitratorDisputeIDToDisputeDetails; // Maps external dispute ids from a particular arbitrator to local arbitration ID and requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => bool) public arbitrationIDToDisputeExists; // Whether a dispute has already been created for the given arbitration ID or not.\\r\\n    mapping(uint256 => address) public arbitrationIDToRequester; // Maps arbitration ID to the requester who was able to complete the arbitration request.\\r\\n    mapping(uint256 => uint256) public arbitrationCreatedBlock; // Block of dispute creation.\\r\\n\\r\\n    event RetryableTicketCreated(uint256 indexed ticketId);\\r\\n\\r\\n    /// @dev https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/ethereum/GreeterL1.sol#L50\\r\\n    modifier onlyL2Bridge() {\\r\\n        IBridge bridge = inbox.bridge();\\r\\n        require(msg.sender == address(bridge), \\\"NOT_BRIDGE\\\");\\r\\n        IOutbox outbox = IOutbox(bridge.activeOutbox());\\r\\n        address l2Sender = outbox.l2ToL1Sender();\\r\\n        require(l2Sender == homeProxy, \\\"Can only be called by Home proxy\\\");\\r\\n        _;\\r\\n    }\\r\\n\\r\\n    modifier onlyGovernor() {\\r\\n        require(msg.sender == governor, \\\"The caller must be the governor.\\\"); \\r\\n        _;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Creates an arbitration proxy on the foreign chain (L1).\\r\\n     * @param _homeProxy Proxy on L2.\\r\\n     * @param _governor Governor of the contract.\\r\\n     * @param _arbitrator Arbitrator contract address.\\r\\n     * @param _arbitratorExtraData The extra data used to raise a dispute in the arbitrator.\\r\\n     * @param _inbox L2 inbox.\\r\\n     * @param _surplusAmount The surplus amount to cover Arbitrum fees.\\r\\n     * @param _l2GasLimit L2 gas limit \\r\\n     * @param _gasPriceBid Max gas price L2.\\r\\n     * @param _metaEvidence The URI of the meta evidence file.\\r\\n     * @param _multipliers Appeal multipliers:\\r\\n     *  - Multiplier for calculating the appeal cost of the winning answer.\\r\\n     *  - Multiplier for calculating the appeal cost of the losing answer.\\r\\n     *  - Multiplier for calculating the appeal period for the losing answer.\\r\\n     */\\r\\n    constructor(\\r\\n        address _homeProxy,\\r\\n        address _governor,\\r\\n        IArbitrator _arbitrator,\\r\\n        bytes memory _arbitratorExtraData,\\r\\n        address _inbox,\\r\\n        uint256 _surplusAmount,\\r\\n        uint256 _l2GasLimit,\\r\\n        uint256 _gasPriceBid,\\r\\n        string memory _metaEvidence,\\r\\n        uint256[3] memory _multipliers\\r\\n    ) {\\r\\n        homeProxy = _homeProxy;\\r\\n        governor = _governor;\\r\\n        arbitrator = _arbitrator;\\r\\n        arbitratorExtraData = _arbitratorExtraData;\\r\\n        inbox = IInbox(_inbox);\\r\\n        surplusAmount = _surplusAmount;\\r\\n        l2GasLimit = _l2GasLimit;\\r\\n        gasPriceBid = _gasPriceBid;\\r\\n        winnerMultiplier = _multipliers[0];\\r\\n        loserMultiplier = _multipliers[1];\\r\\n        loserAppealPeriodMultiplier = _multipliers[2];\\r\\n\\r\\n        emit MetaEvidence(metaEvidenceUpdates, _metaEvidence);\\r\\n    }\\r\\n\\r\\n    /* External and public */\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the governor of the contract.\\r\\n     * @param _governor New governor address.\\r\\n     */\\r\\n    function changeGovernor(address _governor) external onlyGovernor {\\r\\n        governor = _governor;\\r\\n    }\\r\\n    \\r\\n    /**\\r\\n     * @notice Changes the arbitrator and extradata. The arbitrator is trusted to support appeal period and not reenter.\\r\\n     * Note avoid changing arbitrator if there is an active arbitration request in Requested phase, otherwise evidence submitted during this phase\\r\\n     * will be submitted to the new arbitrator, while arbitration request will be processed by the old one. \\r\\n     * @param _arbitrator New arbitrator address.\\r\\n     * @param _arbitratorExtraData Extradata for the arbitrator \\r\\n     */\\r\\n    function changeArbitrator(IArbitrator _arbitrator, bytes calldata _arbitratorExtraData) external onlyGovernor {\\r\\n        arbitrator = _arbitrator;\\r\\n        arbitratorExtraData = _arbitratorExtraData;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Updates the meta evidence used for disputes.\\r\\n     * @param _metaEvidence Metaevidence URI.\\r\\n     */\\r\\n    function changeMetaevidence(string memory _metaEvidence) external onlyGovernor {\\r\\n        metaEvidenceUpdates++;\\r\\n        emit MetaEvidence(metaEvidenceUpdates, _metaEvidence);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the address of Arbitrum inbox contract.\\r\\n     * Note avoid changing the address if there is an active L2->L1 message being processed since the new inbox will most likely reference a new bridge address\\r\\n     * thus making onlyL2Bridge modifier fail.\\r\\n     * @param _inbox New inbox address.\\r\\n     */\\r\\n    function changeInbox(IInbox _inbox) external onlyGovernor {\\r\\n        inbox = _inbox;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the L2 gas limit value.\\r\\n     * @param _l2GasLimit New L2 gas limit.\\r\\n     */\\r\\n    function changeL2GasLimit(uint256 _l2GasLimit) external onlyGovernor {\\r\\n        l2GasLimit = _l2GasLimit;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the L2 gas price bid value.\\r\\n     * @param _gasPriceBid New L2 gas price bid.\\r\\n     */\\r\\n    function changeGasPriceBid(uint256 _gasPriceBid) external onlyGovernor {\\r\\n        gasPriceBid = _gasPriceBid;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes the surplus amount to cover the arbitrum fees.\\r\\n     * @param _surplus New surplus value.\\r\\n     */\\r\\n    function changeSurplus(uint256 _surplus) external onlyGovernor {\\r\\n        surplusAmount = _surplus;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes winner multiplier value.\\r\\n     * @param _winnerMultiplier New winner multiplier.\\r\\n     */\\r\\n    function changeWinnerMultiplier(uint256 _winnerMultiplier) external onlyGovernor {\\r\\n        winnerMultiplier = _winnerMultiplier;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes loser multiplier value.\\r\\n     * @param _loserMultiplier New loser multiplier.\\r\\n     */\\r\\n    function changeLoserMultiplier(uint256 _loserMultiplier) external onlyGovernor {\\r\\n        loserMultiplier = _loserMultiplier;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Changes loser multiplier for appeal period.\\r\\n     * @param _loserAppealPeriodMultiplier New loser multiplier for appeal period.\\r\\n     */\\r\\n    function changeLoserAppealPeriodMultiplier(uint256 _loserAppealPeriodMultiplier) external onlyGovernor {\\r\\n        loserAppealPeriodMultiplier = _loserAppealPeriodMultiplier;\\r\\n    }\\r\\n\\r\\n    // ************************ //\\r\\n    // *    Realitio logic    * //\\r\\n    // ************************ //\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable override {\\r\\n        require(!arbitrationIDToDisputeExists[uint256(_questionID)], \\\"Dispute already created\\\");\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[uint256(_questionID)][msg.sender];\\r\\n        require(arbitration.status == Status.None, \\\"Arbitration already requested\\\");\\r\\n\\r\\n        arbitration.arbitrator = arbitrator;\\r\\n        arbitration.arbitratorExtraData = arbitratorExtraData;\\r\\n        arbitration.metaEvidenceID = metaEvidenceUpdates;\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationRequest.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, msg.sender, _maxPrevious);\\r\\n        \\r\\n        // Taken from here https://docs.arbitrum.io/for-devs/troubleshooting-building#what-is-a-retryable-tickets-submission-fee-how-can-i-calculate-it-what-happens-if-i-the-fee-i-provide-is-insufficient\\r\\n        uint256 maxSubmissionCost = inbox.calculateRetryableSubmissionFee(data.length, BLOCK_BASE_FEE);\\r\\n        uint256 arbitrumFee = arbitrumGasFee(maxSubmissionCost);\\r\\n        uint256 arbitrationCost = arbitrator.arbitrationCost(arbitratorExtraData);\\r\\n        require(msg.value >= arbitrationCost + arbitrumFee, \\\"Deposit value too low\\\");\\r\\n\\r\\n        arbitration.status = Status.Requested;\\r\\n        arbitration.deposit = uint248(msg.value - arbitrumFee);\\r\\n\\r\\n        uint256 ticketID = inbox.createRetryableTicket{value: arbitrumFee}(\\r\\n            homeProxy,\\r\\n            L2_CALL_VALUE,\\r\\n            maxSubmissionCost,\\r\\n            msg.sender, // excessFeeRefundAddress\\r\\n            msg.sender, // callValueRefundAddress\\r\\n            l2GasLimit,\\r\\n            gasPriceBid,\\r\\n            data\\r\\n        );\\r\\n\\r\\n        emit RetryableTicketCreated(ticketID);\\r\\n\\r\\n        emit ArbitrationRequested(_questionID, msg.sender, _maxPrevious);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) public override onlyL2Bridge {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n\\r\\n        // Arbitration cost can possibly change between when the request has been made and received, so evaluate once more.\\r\\n        uint256 arbitrationCost = arbitration.arbitrator.arbitrationCost(arbitration.arbitratorExtraData);\\r\\n        if (arbitration.deposit >= arbitrationCost) {\\r\\n            try\\r\\n                arbitration.arbitrator.createDispute{value: arbitrationCost}(NUMBER_OF_CHOICES_FOR_ARBITRATOR, arbitration.arbitratorExtraData)\\r\\n            returns (uint256 disputeID) {\\r\\n                DisputeDetails storage disputeDetails = arbitratorDisputeIDToDisputeDetails[address(arbitration.arbitrator)][disputeID];\\r\\n                disputeDetails.arbitrationID = arbitrationID;\\r\\n                disputeDetails.requester = _requester;\\r\\n\\r\\n                arbitrationIDToDisputeExists[arbitrationID] = true;\\r\\n                arbitrationIDToRequester[arbitrationID] = _requester;\\r\\n                arbitrationCreatedBlock[disputeID] = block.number;\\r\\n\\r\\n                // At this point, arbitration.deposit is guaranteed to be greater than or equal to the arbitration cost.\\r\\n                uint256 remainder = arbitration.deposit - arbitrationCost;\\r\\n\\r\\n                arbitration.status = Status.Created;\\r\\n                arbitration.deposit = 0;\\r\\n                arbitration.disputeID = disputeID;\\r\\n                arbitration.rounds.push();\\r\\n\\r\\n                if (remainder > 0) {\\r\\n                    payable(_requester).send(remainder);\\r\\n                }\\r\\n\\r\\n                emit ArbitrationCreated(_questionID, _requester, disputeID);\\r\\n                emit Dispute(arbitration.arbitrator, disputeID, arbitration.metaEvidenceID, arbitrationID);\\r\\n            } catch {\\r\\n                arbitration.status = Status.Failed;\\r\\n                emit ArbitrationFailed(_questionID, _requester);\\r\\n            }\\r\\n        } else {\\r\\n            arbitration.status = Status.Failed;\\r\\n            emit ArbitrationFailed(_questionID, _requester);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) public override onlyL2Bridge {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Requested, \\\"Invalid arbitration status\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover arbitrum fees.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable override {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Failed, \\\"Invalid arbitration status\\\");\\r\\n            \\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationFailure.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, _requester);\\r\\n\\r\\n        uint256 maxSubmissionCost = inbox.calculateRetryableSubmissionFee(data.length, BLOCK_BASE_FEE);\\r\\n        uint256 arbitrumFee = arbitrumGasFee(maxSubmissionCost);\\r\\n        require(msg.value >= arbitrumFee, \\\"Should cover arbitrum fee\\\");\\r\\n        uint256 deposit = arbitration.deposit;\\r\\n\\r\\n        delete arbitrationRequests[arbitrationID][_requester];\\r\\n        uint256 surplusValue = msg.value - arbitrumFee;\\r\\n        payable(msg.sender).send(surplusValue);\\r\\n        payable(_requester).send(deposit);\\r\\n\\r\\n        uint256 ticketID = inbox.createRetryableTicket{value: arbitrumFee}(\\r\\n            homeProxy,\\r\\n            L2_CALL_VALUE,\\r\\n            maxSubmissionCost,\\r\\n            msg.sender, // excessFeeRefundAddress\\r\\n            msg.sender, // callValueRefundAddress\\r\\n            l2GasLimit,\\r\\n            gasPriceBid,\\r\\n            data\\r\\n        );\\r\\n\\r\\n        emit RetryableTicketCreated(ticketID);\\r\\n\\r\\n        emit ArbitrationCanceled(_questionID, _requester);\\r\\n    }\\r\\n\\r\\n    // ********************************* //\\r\\n    // *    Appeals and arbitration    * //\\r\\n    // ********************************* //\\r\\n\\r\\n    /**\\r\\n     * @notice Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded.\\r\\n     * @param _arbitrationID The ID of the arbitration, which is questionID cast into uint256.\\r\\n     * @param _answer One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question.\\r\\n     * Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format.\\r\\n     * Also note that '0' answer can be funded.\\r\\n     * @return Whether the answer was fully funded or not.\\r\\n     */\\r\\n    function fundAppeal(uint256 _arbitrationID, uint256 _answer) external payable override returns (bool) {\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][\\r\\n            arbitrationIDToRequester[_arbitrationID]\\r\\n        ];\\r\\n        require(arbitration.status == Status.Created, \\\"No dispute to appeal.\\\");\\r\\n\\r\\n        uint256 disputeID = arbitration.disputeID;\\r\\n        (uint256 appealPeriodStart, uint256 appealPeriodEnd) = arbitration.arbitrator.appealPeriod(disputeID);\\r\\n        require(block.timestamp >= appealPeriodStart && block.timestamp < appealPeriodEnd, \\\"Appeal period is over.\\\");\\r\\n\\r\\n        uint256 multiplier;\\r\\n        {\\r\\n            uint256 winner = arbitration.arbitrator.currentRuling(disputeID);\\r\\n            if (winner == _answer) {\\r\\n                multiplier = winnerMultiplier;\\r\\n            } else {\\r\\n                require(\\r\\n                    block.timestamp - appealPeriodStart <\\r\\n                        ((appealPeriodEnd - appealPeriodStart) * (loserAppealPeriodMultiplier)) / MULTIPLIER_DIVISOR,\\r\\n                    \\\"Appeal period is over for loser\\\"\\r\\n                );\\r\\n                multiplier = loserMultiplier;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        uint256 lastRoundID = arbitration.rounds.length - 1;\\r\\n        Round storage round = arbitration.rounds[lastRoundID];\\r\\n        require(!round.hasPaid[_answer], \\\"Appeal fee is already paid.\\\");\\r\\n        uint256 appealCost = arbitration.arbitrator.appealCost(disputeID, arbitration.arbitratorExtraData);\\r\\n        uint256 totalCost = appealCost + ((appealCost * multiplier) / MULTIPLIER_DIVISOR);\\r\\n\\r\\n        // Take up to the amount necessary to fund the current round at the current costs.\\r\\n        uint256 contribution = totalCost - (round.paidFees[_answer]) > msg.value\\r\\n            ? msg.value\\r\\n            : totalCost - (round.paidFees[_answer]);\\r\\n        emit Contribution(_arbitrationID, lastRoundID, _answer, msg.sender, contribution);\\r\\n\\r\\n        round.contributions[msg.sender][_answer] += contribution;\\r\\n        round.paidFees[_answer] += contribution;\\r\\n        if (round.paidFees[_answer] >= totalCost) {\\r\\n            round.feeRewards += round.paidFees[_answer];\\r\\n            round.fundedAnswers.push(_answer);\\r\\n            round.hasPaid[_answer] = true;\\r\\n            emit RulingFunded(_arbitrationID, lastRoundID, _answer);\\r\\n        }\\r\\n\\r\\n        if (round.fundedAnswers.length > 1) {\\r\\n            // At least two sides are fully funded.\\r\\n            arbitration.rounds.push();\\r\\n\\r\\n            round.feeRewards = round.feeRewards - appealCost;\\r\\n            arbitration.arbitrator.appeal{value: appealCost}(disputeID, arbitration.arbitratorExtraData);\\r\\n        }\\r\\n\\r\\n        if (msg.value - contribution > 0) payable(msg.sender).send(msg.value - contribution); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\r\\n        return round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address to send reward to.\\r\\n     * @param _round The round from which to withdraw.\\r\\n     * @param _answer The answer to query the reward from.\\r\\n     * @return reward The withdrawn amount.\\r\\n     */\\r\\n    function withdrawFeesAndRewards(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) public override returns (uint256 reward) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\r\\n        // Allow to reimburse if funding of the round was unsuccessful.\\r\\n        if (!round.hasPaid[_answer]) {\\r\\n            reward = round.contributions[_beneficiary][_answer];\\r\\n        } else if (!round.hasPaid[arbitration.answer]) {\\r\\n            // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n            // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n            reward = round.fundedAnswers.length > 1\\r\\n                ? (round.contributions[_beneficiary][_answer] * round.feeRewards) /\\r\\n                    (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                : 0;\\r\\n        } else if (arbitration.answer == _answer) {\\r\\n            uint256 paidFees = round.paidFees[_answer];\\r\\n            // Reward the winner.\\r\\n            reward = paidFees > 0 ? (round.contributions[_beneficiary][_answer] * round.feeRewards) / paidFees : 0;\\r\\n        }\\r\\n\\r\\n        if (reward != 0) {\\r\\n            round.contributions[_beneficiary][_answer] = 0;\\r\\n            _beneficiary.send(reward); // It is the user's responsibility to accept ETH.\\r\\n            emit Withdrawal(_arbitrationID, _round, _answer, _beneficiary, reward);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to withdraw any rewards or reimbursable fees for all rounds at once.\\r\\n     * @dev This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.\\r\\n     *      So because of this exponential growth of costs, you can assume n is less than 10 at all times.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The address that made contributions.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     */\\r\\n    function withdrawFeesAndRewardsForAllRounds(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external override {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n\\r\\n        uint256 numberOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < numberOfRounds; roundNumber++) {\\r\\n            withdrawFeesAndRewards(_arbitrationID, _beneficiary, roundNumber, _contributedTo);\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Allows to submit evidence for a particular question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @param _evidenceURI Link to evidence.\\r\\n     */\\r\\n    function submitEvidence(uint256 _arbitrationID, string calldata _evidenceURI) external override {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        if (address(arbitration.arbitrator) == address(0)) { //None or Requested status.\\r\\n            // Note that arbitrator set during requestArbitration might differ from default arbitrator, if default arbitrator was changed during Requested status.\\r\\n            emit Evidence(arbitrator, _arbitrationID, msg.sender, _evidenceURI);\\r\\n        } else {\\r\\n            emit Evidence(arbitration.arbitrator, _arbitrationID, msg.sender, _evidenceURI);     \\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Rules a specified dispute. Can only be called by the arbitrator.\\r\\n     * @dev Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.\\r\\n     * @param _disputeID The ID of the dispute in the ERC792 arbitrator.\\r\\n     * @param _ruling The ruling given by the arbitrator.\\r\\n     */\\r\\n    function rule(uint256 _disputeID, uint256 _ruling) external override {\\r\\n        DisputeDetails storage disputeDetails = arbitratorDisputeIDToDisputeDetails[msg.sender][_disputeID];\\r\\n        uint256 arbitrationID = disputeDetails.arbitrationID;\\r\\n        address requester = disputeDetails.requester;\\r\\n\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][requester];\\r\\n        require(msg.sender == address(arbitration.arbitrator), \\\"Only arbitrator allowed\\\");\\r\\n        require(arbitration.status == Status.Created, \\\"Invalid arbitration status\\\");\\r\\n        uint256 finalRuling = _ruling;\\r\\n\\r\\n        // If one side paid its fees, the ruling is in its favor. Note that if the other side had also paid, an appeal would have been created.\\r\\n        Round storage round = arbitration.rounds[arbitration.rounds.length - 1];\\r\\n        if (round.fundedAnswers.length == 1) finalRuling = round.fundedAnswers[0];\\r\\n\\r\\n        arbitration.answer = finalRuling;\\r\\n        arbitration.status = Status.Ruled;\\r\\n        emit Ruling(IArbitrator(msg.sender), _disputeID, finalRuling);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Relays the ruling to home proxy. Requires a small deposit to cover arbitrum fees.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function relayRule(bytes32 _questionID, address _requester) external payable {\\r\\n        uint256 arbitrationID = uint256(_questionID);\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[arbitrationID][_requester];\\r\\n        require(arbitration.status == Status.Ruled, \\\"Dispute not resolved\\\");\\r\\n        \\r\\n        // Realitio ruling is shifted by 1 compared to Kleros.\\r\\n        uint256 realitioRuling = arbitration.answer != 0 ? arbitration.answer - 1 : REFUSE_TO_ARBITRATE_REALITIO;\\r\\n\\r\\n        bytes4 methodSelector = IHomeArbitrationProxy.receiveArbitrationAnswer.selector;\\r\\n        bytes memory data = abi.encodeWithSelector(methodSelector, _questionID, bytes32(realitioRuling));\\r\\n\\r\\n        uint256 maxSubmissionCost = inbox.calculateRetryableSubmissionFee(data.length, BLOCK_BASE_FEE);\\r\\n        uint256 arbitrumFee = arbitrumGasFee(maxSubmissionCost);\\r\\n        require(msg.value >= arbitrumFee, \\\"Should cover arbitrum fee\\\");\\r\\n\\r\\n        arbitration.status = Status.Relayed;\\r\\n\\r\\n        uint256 ticketID = inbox.createRetryableTicket{value: arbitrumFee}(\\r\\n            homeProxy,\\r\\n            L2_CALL_VALUE,\\r\\n            maxSubmissionCost,\\r\\n            msg.sender, // excessFeeRefundAddress\\r\\n            msg.sender, // callValueRefundAddress\\r\\n            l2GasLimit,\\r\\n            gasPriceBid,\\r\\n            data\\r\\n        );\\r\\n\\r\\n        emit RetryableTicketCreated(ticketID);\\r\\n        emit RulingRelayed(_questionID, bytes32(realitioRuling));\\r\\n\\r\\n        if (msg.value - arbitrumFee > 0) payable(msg.sender).send(msg.value - arbitrumFee); // Sending extra value back to contributor. It is the user's responsibility to accept ETH.\\r\\n    }\\r\\n\\r\\n    /* External Views */\\r\\n\\r\\n    /**\\r\\n     * @notice Returns stake multipliers.\\r\\n     * @return winner Winners stake multiplier.\\r\\n     * @return loser Losers stake multiplier.\\r\\n     * @return loserAppealPeriod Multiplier for calculating an appeal period duration for the losing side.\\r\\n     * @return divisor Multiplier divisor.\\r\\n     */\\r\\n    function getMultipliers()\\r\\n        external\\r\\n        view\\r\\n        override\\r\\n        returns (uint256 winner, uint256 loser, uint256 loserAppealPeriod, uint256 divisor)\\r\\n    {\\r\\n        return (winnerMultiplier, loserMultiplier, loserAppealPeriodMultiplier, MULTIPLIER_DIVISOR);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns number of possible ruling options. Valid rulings are [0, return value].\\r\\n     * @return count The number of ruling options.\\r\\n     */\\r\\n    function numberOfRulingOptions(uint256 /* _arbitrationID */) external pure override returns (uint256) {\\r\\n        return NUMBER_OF_CHOICES_FOR_ARBITRATOR;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 /* _questionID */) external view override returns (uint256) {\\r\\n        return arbitrator.arbitrationCost(arbitratorExtraData) + surplusAmount;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the number of rounds of the specific question.\\r\\n     * @param _arbitrationID The ID of the arbitration related to the question.\\r\\n     * @return The number of rounds.\\r\\n     */\\r\\n    function getNumberOfRounds(uint256 _arbitrationID) external view returns (uint256) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        return arbitration.rounds.length;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @return paidFees The amount of fees paid for each fully funded answer.\\r\\n     * @return feeRewards The amount of fees that will be used as rewards.\\r\\n     * @return fundedAnswers IDs of fully funded answers.\\r\\n     */\\r\\n    function getRoundInfo(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round\\r\\n    ) external view returns (uint256[] memory paidFees, uint256 feeRewards, uint256[] memory fundedAnswers) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n\\r\\n        paidFees = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < round.fundedAnswers.length; i++) {\\r\\n            paidFees[i] = round.paidFees[round.fundedAnswers[i]];\\r\\n        }\\r\\n\\r\\n        feeRewards = round.feeRewards;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the information of a round of a question for a specific answer choice.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _answer The answer choice to get funding status for.\\r\\n     * @return raised The amount paid for this answer.\\r\\n     * @return fullyFunded Whether the answer is fully funded or not.\\r\\n     */\\r\\n    function getFundingStatus(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        uint256 _answer\\r\\n    ) external view returns (uint256 raised, bool fullyFunded) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        raised = round.paidFees[_answer];\\r\\n        fullyFunded = round.hasPaid[_answer];\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets contributions to the answers that are fully funded.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _round The round to query.\\r\\n     * @param _contributor The address whose contributions to query.\\r\\n     * @return fundedAnswers IDs of the answers that are fully funded.\\r\\n     * @return contributions The amount contributed to each funded answer by the contributor.\\r\\n     */\\r\\n    function getContributionsToSuccessfulFundings(\\r\\n        uint256 _arbitrationID,\\r\\n        uint256 _round,\\r\\n        address _contributor\\r\\n    ) external view returns (uint256[] memory fundedAnswers, uint256[] memory contributions) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        Round storage round = arbitration.rounds[_round];\\r\\n\\r\\n        fundedAnswers = round.fundedAnswers;\\r\\n        contributions = new uint256[](round.fundedAnswers.length);\\r\\n\\r\\n        for (uint256 i = 0; i < contributions.length; i++) {\\r\\n            contributions[i] = round.contributions[_contributor][fundedAnswers[i]];\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Returns the sum of withdrawable amount.\\r\\n     * @dev This function is O(n) where n is the total number of rounds.\\r\\n     * @dev This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.\\r\\n     * @param _arbitrationID The ID of the arbitration.\\r\\n     * @param _beneficiary The contributor for which to query.\\r\\n     * @param _contributedTo Answer that received contributions from contributor.\\r\\n     * @return sum The total amount available to withdraw.\\r\\n     */\\r\\n    function getTotalWithdrawableAmount(\\r\\n        uint256 _arbitrationID,\\r\\n        address payable _beneficiary,\\r\\n        uint256 _contributedTo\\r\\n    ) external view override returns (uint256 sum) {\\r\\n        address requester = arbitrationIDToRequester[_arbitrationID];\\r\\n        ArbitrationRequest storage arbitration = arbitrationRequests[_arbitrationID][requester];\\r\\n        if (arbitration.status < Status.Ruled) return sum;\\r\\n\\r\\n        uint256 finalAnswer = arbitration.answer;\\r\\n        uint256 noOfRounds = arbitration.rounds.length;\\r\\n        for (uint256 roundNumber = 0; roundNumber < noOfRounds; roundNumber++) {\\r\\n            Round storage round = arbitration.rounds[roundNumber];\\r\\n\\r\\n            if (!round.hasPaid[_contributedTo]) {\\r\\n                // Allow to reimburse if funding was unsuccessful for this answer option.\\r\\n                sum += round.contributions[_beneficiary][_contributedTo];\\r\\n            } else if (!round.hasPaid[finalAnswer]) {\\r\\n                // Reimburse unspent fees proportionally if the ultimate winner didn't pay appeal fees fully.\\r\\n                // Note that if only one side is funded it will become a winner and this part of the condition won't be reached.\\r\\n                sum += round.fundedAnswers.length > 1\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) /\\r\\n                        (round.paidFees[round.fundedAnswers[0]] + round.paidFees[round.fundedAnswers[1]])\\r\\n                    : 0;\\r\\n            } else if (finalAnswer == _contributedTo) {\\r\\n                uint256 paidFees = round.paidFees[_contributedTo];\\r\\n                // Reward the winner.\\r\\n                sum += paidFees > 0\\r\\n                    ? (round.contributions[_beneficiary][_contributedTo] * round.feeRewards) / paidFees\\r\\n                    : 0;\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Casts question ID into uint256 thus returning the related arbitration ID.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @return The ID of the arbitration.\\r\\n     */\\r\\n    function questionIDToArbitrationID(bytes32 _questionID) external pure returns (uint256) {\\r\\n        return uint256(_questionID);\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Maps external (arbitrator side) dispute id to local (arbitrable) dispute id.\\r\\n     * @param _externalDisputeID Dispute id as in arbitrator side.\\r\\n     * @return localDisputeID Dispute id as in arbitrable contract.\\r\\n     */\\r\\n    function externalIDtoLocalID(uint256 _externalDisputeID) external view override returns (uint256) {\\r\\n        // Note that in case of arbitrator's change external dispute from the new arbitrator\\r\\n        // will overwrite the external dispute with the same ID from the old arbitrator,\\r\\n        // which will make the data related to the old arbitrator's dispute unaccessible in DisputeResolver's UI.\\r\\n        // It should be fine since the dispute will be closed anyway.\\r\\n        // Ideally we would want to have arbitrator's address as one of the parameters, but we can't break the interface.\\r\\n        return arbitratorDisputeIDToDisputeDetails[address(arbitrator)][_externalDisputeID].arbitrationID;\\r\\n    }\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the required fee to process the message on L2.\\r\\n     * @dev Logic that checks if the user have enough funds to create a ticket. This is done by checking if the msg.value provided by the user \\r\\n     * is greater than or equal to maxSubmissionCost + l2CallValue + gasLimit * maxFeePerGas.\\r\\n     * https://docs.arbitrum.io/how-arbitrum-works/arbos/l1-l2-messaging\\r\\n     * @param _maxSubmissionCost Cost to calculate a retryable ticket on L1.\\r\\n     * @return arbitrumFee Total arbitrum fee required to pass a message L1->L2.\\r\\n     */\\r\\n    function arbitrumGasFee(uint256 _maxSubmissionCost) private view returns (uint256 arbitrumFee) {\\r\\n        arbitrumFee = _maxSubmissionCost + L2_CALL_VALUE + l2GasLimit * gasPriceBid;\\r\\n    }\\r\\n}\\r\\n\",\"keccak256\":\"0x965beead34454e2959e0db93189939de7426e2a774fa18528127e83a13867eaa\",\"license\":\"MIT\"},\"src/interfaces/ArbitrationProxyInterfaces.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\r\\npragma solidity 0.8.25;\\r\\n\\r\\ninterface IHomeArbitrationProxy {\\r\\n    /**\\r\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\r\\n     */\\r\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when arbitration request is rejected.\\r\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\r\\n     * or if the question is already finalized.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\r\\n     * @param _reason The reason why the request was rejected.\\r\\n     */\\r\\n    event RequestRejected(\\r\\n        bytes32 indexed _questionID,\\r\\n        address indexed _requester,\\r\\n        uint256 _maxPrevious,\\r\\n        string _reason\\r\\n    );\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the arbitration request is canceled.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\r\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\r\\n\\r\\n    /**\\r\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\r\\n     * @param _questionID The ID of the question.\\r\\n     */\\r\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\r\\n\\r\\n    /**\\r\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function receiveArbitrationRequest(bytes32 _questionID, address _requester, uint256 _maxPrevious) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Handles arbitration request after it has been rejected.\\r\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\r\\n     * the Polygon Bridge and cannot send messages back to it.\\r\\n     * Reasons why the request might be rejected:\\r\\n     *  - The question does not exist\\r\\n     *  - The question was not answered yet\\r\\n     *  - The quesiton bond value changed while the arbitration was being requested\\r\\n     *  - Another request was already accepted\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\r\\n     * @dev Currently this can happen only if the arbitration cost increased.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the answer to a specified question. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _answer The answer from the arbitrator.\\r\\n     */\\r\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\r\\n\\r\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\r\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\r\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\r\\n     *  @dev Template_hashes won't be used by this home proxy. \\r\\n     */\\r\\n    function metadata() external view returns (string calldata);\\r\\n}\\r\\n\\r\\ninterface IForeignArbitrationProxy {\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is requested.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute is created.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     * @param _disputeID The ID of the dispute.\\r\\n     */\\r\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the dispute could not be created.\\r\\n     * @dev This will happen if there is an increase in the arbitration fees\\r\\n     * between the time the arbitration is made and the time it is acknowledged.\\r\\n     * @param _questionID The ID of the question with the request for arbitration.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\r\\n\\r\\n    /**\\r\\n     * @notice Should be emitted when the ruling is relayed to home proxy.\\r\\n     * @param _questionID The ID of the question with the ruling to relay.\\r\\n     * @param _ruling Ruling converted into Realitio format.\\r\\n     */\\r\\n    event RulingRelayed(bytes32 _questionID, bytes32 _ruling);\\r\\n\\r\\n    /**\\r\\n     * @notice Requests arbitration for the given question and contested answer.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\r\\n     */\\r\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\r\\n\\r\\n    /**\\r\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\r\\n     * @param _questionID The ID of the question.\\r\\n     * @param _requester The address of the arbitration requester.\\r\\n     */\\r\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable;\\r\\n\\r\\n    /**\\r\\n     * @notice Gets the fee to create a dispute.\\r\\n     * @param _questionID the ID of the question.\\r\\n     * @return The fee to create a dispute.\\r\\n     */\\r\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\r\\n}\\r\\n\",\"keccak256\":\"0x64699ba74323bd9c3c5cf00d97c4d3004d5dffe60fedd4113d3344a560bf2aee\",\"license\":\"MIT\"},\"src/interfaces/IBridge.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\r\\n// https://github.com/OffchainLabs/nitro-contracts/blob/08ac127e966fa87a4d5ba3d23cd3132b57701132/src/bridge/IBridge.sol\\r\\n// interface is pruned for relevant function stubs\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\ninterface IBridge {\\r\\n    function activeOutbox() external view returns (address);\\r\\n}\",\"keccak256\":\"0x97157d3158bc517aefc70a1474e9ea00abbe1235a2ab40c159a16d83fa37469d\",\"license\":\"BUSL-1.1\"},\"src/interfaces/IInbox.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\r\\n// https://github.com/OffchainLabs/nitro-contracts/blob/08ac127e966fa87a4d5ba3d23cd3132b57701132/src/bridge/IInbox.sol\\r\\n// interface is pruned for relevant function stubs\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\nimport \\\"./IBridge.sol\\\";\\r\\n\\r\\ninterface IInbox {\\r\\n    function bridge() external view returns (IBridge);\\r\\n\\r\\n    /**\\r\\n     * @notice Get the L1 fee for submitting a retryable\\r\\n     * @dev This fee can be paid by funds already in the L2 aliased address or by the current message value\\r\\n     * @dev This formula may change in the future, to future proof your code query this method instead of inlining!!\\r\\n     * @param dataLength The length of the retryable's calldata, in bytes\\r\\n     * @param baseFee The block basefee when the retryable is included in the chain, if 0 current block.basefee will be used\\r\\n     */\\r\\n    function calculateRetryableSubmissionFee(uint256 dataLength, uint256 baseFee)\\r\\n        external\\r\\n        view\\r\\n        returns (uint256);\\r\\n\\r\\n    /**\\r\\n     * @notice Put a message in the L2 inbox that can be reexecuted for some fixed amount of time if it reverts\\r\\n     * @dev all msg.value will deposited to callValueRefundAddress on L2\\r\\n     * @dev Gas limit and maxFeePerGas should not be set to 1 as that is used to trigger the RetryableData error\\r\\n     * @param to destination L2 contract address\\r\\n     * @param l2CallValue call value for retryable L2 message\\r\\n     * @param maxSubmissionCost Max gas deducted from user's L2 balance to cover base submission fee\\r\\n     * @param excessFeeRefundAddress gasLimit x maxFeePerGas - execution cost gets credited here on L2 balance\\r\\n     * @param callValueRefundAddress l2Callvalue gets credited here on L2 if retryable txn times out or gets cancelled\\r\\n     * @param gasLimit Max gas deducted from user's L2 balance to cover L2 execution. Should not be set to 1 (magic value used to trigger the RetryableData error)\\r\\n     * @param maxFeePerGas price bid for L2 execution. Should not be set to 1 (magic value used to trigger the RetryableData error)\\r\\n     * @param data ABI encoded data of L2 message\\r\\n     * @return unique message number of the retryable transaction\\r\\n     */\\r\\n    function createRetryableTicket(\\r\\n        address to,\\r\\n        uint256 l2CallValue,\\r\\n        uint256 maxSubmissionCost,\\r\\n        address excessFeeRefundAddress,\\r\\n        address callValueRefundAddress,\\r\\n        uint256 gasLimit,\\r\\n        uint256 maxFeePerGas,\\r\\n        bytes calldata data\\r\\n    ) external payable returns (uint256);\\r\\n}\",\"keccak256\":\"0x6e6ea0921ccbb1bac5f5e85072d08cb2b880d99bed31f12eb42ef9cd126addde\",\"license\":\"BUSL-1.1\"},\"src/interfaces/IOutbox.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\r\\n// https://github.com/OffchainLabs/nitro-contracts/blob/08ac127e966fa87a4d5ba3d23cd3132b57701132/src/bridge/IOutbox.sol\\r\\n// interface is pruned for relevant function stubs\\r\\n\\r\\npragma solidity 0.8.25;\\r\\n\\r\\ninterface IOutbox {\\r\\n    /// @notice When l2ToL1Sender returns a nonzero address, the message was originated by an L2 account\\r\\n    ///         When the return value is zero, that means this is a system message\\r\\n    /// @dev the l2ToL1Sender behaves as the tx.origin, the msg.sender should be validated to protect against reentrancies\\r\\n    function l2ToL1Sender() external view returns (address);\\r\\n}\",\"keccak256\":\"0x300d36472709b99dd1bd62395b5751863a0af7f95cb7dc89bcc4b5e250c7e4a0\",\"license\":\"BUSL-1.1\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract is meant to be deployed to the Ethereum chains where Kleros is deployed. Example https://github.com/OffchainLabs/arbitrum-tutorials/blob/2c1b7d2db8f36efa496e35b561864c0f94123a5f/packages/greeter/contracts/ethereum/GreeterL1.sol", "events": {"ArbitrationCanceled(bytes32,address)": {"params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationCreated(bytes32,address,uint256)": {"params": {"_disputeID": "The ID of the dispute.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationFailed(bytes32,address)": {"details": "This will happen if there is an increase in the arbitration fees between the time the arbitration is made and the time it is acknowledged.", "params": {"_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "ArbitrationRequested(bytes32,address,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question with the request for arbitration.", "_requester": "The address of the arbitration requester."}}, "Contribution(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contribution is made, inside fundAppeal function.", "params": {"_amount": "Contribution amount.", "_contributor": "Caller of fundAppeal function.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "The round number the contribution was made to.", "ruling": "Indicates the ruling option which got the contribution."}}, "Dispute(address,uint256,uint256,uint256)": {"details": "To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.", "params": {"_arbitrator": "The arbitrator of the contract.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_evidenceGroupID": "Unique identifier of the evidence group that is linked to this dispute.", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Evidence(address,uint256,address,string)": {"details": "To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).", "params": {"_arbitrator": "The arbitrator of the contract.", "_evidence": "IPFS path to evidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/evidence.json'", "_evidenceGroupID": "Unique identifier of the evidence group the evidence belongs to.", "_party": "The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party."}}, "MetaEvidence(uint256,string)": {"details": "To be emitted when meta-evidence is submitted.", "params": {"_evidence": "IPFS path to metaevidence, example: '/ipfs/Qmarwkf7C9RuzDEJNnarT3WZ7kem5bk8DZAzx78acJjMFH/metaevidence.json'", "_metaEvidenceID": "Unique identifier of meta-evidence."}}, "Ruling(address,uint256,uint256)": {"details": "To be raised when a ruling is given.", "params": {"_arbitrator": "The arbitrator giving the ruling.", "_disputeID": "ID of the dispute in the Arbitrator contract.", "_ruling": "The ruling which was given."}}, "RulingFunded(uint256,uint256,uint256)": {"details": "To be raised when a ruling option is fully funded for appeal.", "params": {"_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_round": "Number of the round this ruling option was fully funded in.", "_ruling": "The ruling option which just got fully funded."}}, "RulingRelayed(bytes32,bytes32)": {"params": {"_questionID": "The ID of the question with the ruling to relay.", "_ruling": "Ruling converted into Realitio format."}}, "Withdrawal(uint256,uint256,uint256,address,uint256)": {"details": "Raised when a contributor withdraws non-zero value.", "params": {"_contributor": "The beneficiary of withdrawal.", "_localDisputeID": "Identifier of a dispute in scope of arbitrable contract. Arbitrator ids can be translated to local ids via externalIDtoLocalID.", "_reward": "Total amount of withdrawal, consists of reimbursed deposits plus rewards.", "_round": "The round number the withdrawal was made from.", "_ruling": "Indicates the ruling option which contributor gets rewards from."}}}, "kind": "dev", "methods": {"changeArbitrator(address,bytes)": {"params": {"_arbitrator": "New arbitrator address.", "_arbitratorExtraData": "Extradata for the arbitrator "}}, "changeGasPriceBid(uint256)": {"params": {"_gasPriceBid": "New L2 gas price bid."}}, "changeGovernor(address)": {"params": {"_governor": "New governor address."}}, "changeInbox(address)": {"params": {"_inbox": "New inbox address."}}, "changeL2GasLimit(uint256)": {"params": {"_l2GasLimit": "New L2 gas limit."}}, "changeLoserAppealPeriodMultiplier(uint256)": {"params": {"_loserAppealPeriodMultiplier": "New loser multiplier for appeal period."}}, "changeLoserMultiplier(uint256)": {"params": {"_loserMultiplier": "New loser multiplier."}}, "changeMetaevidence(string)": {"params": {"_metaEvidence": "Metaevidence URI."}}, "changeSurplus(uint256)": {"params": {"_surplus": "New surplus value."}}, "changeWinnerMultiplier(uint256)": {"params": {"_winnerMultiplier": "New winner multiplier."}}, "constructor": {"params": {"_arbitrator": "Arbitrator contract address.", "_arbitratorExtraData": "The extra data used to raise a dispute in the arbitrator.", "_gasPriceBid": "Max gas price L2.", "_governor": "Governor of the contract.", "_homeProxy": "Proxy on L2.", "_inbox": "L2 inbox.", "_l2GasLimit": "L2 gas limit ", "_metaEvidence": "The URI of the meta evidence file.", "_multipliers": "Appeal multipliers:  - Multiplier for calculating the appeal cost of the winning answer.  - Multiplier for calculating the appeal cost of the losing answer.  - Multiplier for calculating the appeal period for the losing answer.", "_surplusAmount": "The surplus amount to cover Arbitrum fees."}}, "externalIDtoLocalID(uint256)": {"params": {"_externalDisputeID": "Dispute id as in arbitrator side."}, "returns": {"_0": "localDisputeID Dispute id as in arbitrable contract."}}, "fundAppeal(uint256,uint256)": {"params": {"_answer": "One of the possible rulings the arbitrator can give that the funder considers to be the correct answer to the question. Note that the answer has Kleros denomination, meaning that it has '+1' offset compared to Realitio format. Also note that '0' answer can be funded.", "_arbitrationID": "The ID of the arbitration, which is questionID cast into uint256."}, "returns": {"_0": "Whether the answer was fully funded or not."}}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_contributor": "The address whose contributions to query.", "_round": "The round to query."}, "returns": {"contributions": "The amount contributed to each funded answer by the contributor.", "fundedAnswers": "IDs of the answers that are fully funded."}}, "getDisputeFee(bytes32)": {"returns": {"_0": "The fee to create a dispute."}}, "getFundingStatus(uint256,uint256,uint256)": {"params": {"_answer": "The answer choice to get funding status for.", "_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"fullyFunded": "Whether the answer is fully funded or not.", "raised": "The amount paid for this answer."}}, "getMultipliers()": {"returns": {"divisor": "Multiplier divisor.", "loser": "Losers stake multiplier.", "loserAppealPeriod": "Multiplier for calculating an appeal period duration for the losing side.", "winner": "Winners stake multiplier."}}, "getNumberOfRounds(uint256)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question."}, "returns": {"_0": "The number of rounds."}}, "getRoundInfo(uint256,uint256)": {"params": {"_arbitrationID": "The ID of the arbitration.", "_round": "The round to query."}, "returns": {"feeRewards": "The amount of fees that will be used as rewards.", "fundedAnswers": "IDs of fully funded answers.", "paidFees": "The amount of fees paid for each fully funded answer."}}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds.This could exceed the gas limit, therefore this function should be used only as a utility and not be relied upon by other contracts.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The contributor for which to query.", "_contributedTo": "Answer that received contributions from contributor."}, "returns": {"sum": "The total amount available to withdraw."}}, "handleFailedDisputeCreation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "numberOfRulingOptions(uint256)": {"returns": {"_0": "count The number of ruling options."}}, "questionIDToArbitrationID(bytes32)": {"params": {"_questionID": "The ID of the question."}, "returns": {"_0": "The ID of the arbitration."}}, "receiveArbitrationAcknowledgement(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "receiveArbitrationCancelation(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The requester."}}, "relayRule(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "requestArbitration(bytes32,uint256)": {"params": {"_maxPrevious": "The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.", "_questionID": "The ID of the question."}}, "rule(uint256,uint256)": {"details": "Accounts for the situation where the winner loses a case due to paying less appeal fees than expected.", "params": {"_disputeID": "The ID of the dispute in the ERC792 arbitrator.", "_ruling": "The ruling given by the arbitrator."}}, "submitEvidence(uint256,string)": {"params": {"_arbitrationID": "The ID of the arbitration related to the question.", "_evidenceURI": "Link to evidence."}}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"params": {"_answer": "The answer to query the reward from.", "_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address to send reward to.", "_round": "The round from which to withdraw."}, "returns": {"reward": "The withdrawn amount."}}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"details": "This function is O(n) where n is the total number of rounds. Arbitration cost of subsequent rounds is `A(n) = 2A(n-1) + 1`.      So because of this exponential growth of costs, you can assume n is less than 10 at all times.", "params": {"_arbitrationID": "The ID of the arbitration.", "_beneficiary": "The address that made contributions.", "_contributedTo": "Answer that received contributions from contributor."}}}, "title": "Arbitration proxy for Realitio on Ethereum side (A.K.A. the Foreign Chain).", "version": 1}, "userdoc": {"events": {"ArbitrationCanceled(bytes32,address)": {"notice": "Should be emitted when the arbitration is canceled by the Home Chain."}, "ArbitrationCreated(bytes32,address,uint256)": {"notice": "Should be emitted when the dispute is created."}, "ArbitrationFailed(bytes32,address)": {"notice": "Should be emitted when the dispute could not be created."}, "ArbitrationRequested(bytes32,address,uint256)": {"notice": "Should be emitted when the arbitration is requested."}, "RulingRelayed(bytes32,bytes32)": {"notice": "Should be emitted when the ruling is relayed to home proxy."}}, "kind": "user", "methods": {"changeArbitrator(address,bytes)": {"notice": "Changes the arbitrator and extradata. The arbitrator is trusted to support appeal period and not reenter. Note avoid changing arbitrator if there is an active arbitration request in Requested phase, otherwise evidence submitted during this phase will be submitted to the new arbitrator, while arbitration request will be processed by the old one. "}, "changeGasPriceBid(uint256)": {"notice": "Changes the L2 gas price bid value."}, "changeGovernor(address)": {"notice": "Changes the governor of the contract."}, "changeInbox(address)": {"notice": "Changes the address of Arbitrum inbox contract. Note avoid changing the address if there is an active L2->L1 message being processed since the new inbox will most likely reference a new bridge address thus making onlyL2Bridge modifier fail."}, "changeL2GasLimit(uint256)": {"notice": "Changes the L2 gas limit value."}, "changeLoserAppealPeriodMultiplier(uint256)": {"notice": "Changes loser multiplier for appeal period."}, "changeLoserMultiplier(uint256)": {"notice": "Changes loser multiplier value."}, "changeMetaevidence(string)": {"notice": "Updates the meta evidence used for disputes."}, "changeSurplus(uint256)": {"notice": "Changes the surplus amount to cover the arbitrum fees."}, "changeWinnerMultiplier(uint256)": {"notice": "Changes winner multiplier value."}, "constructor": {"notice": "Creates an arbitration proxy on the foreign chain (L1)."}, "externalIDtoLocalID(uint256)": {"notice": "Maps external (arbitrator side) dispute id to local (arbitrable) dispute id."}, "fundAppeal(uint256,uint256)": {"notice": "Takes up to the total amount required to fund an answer. Reimburses the rest. Creates an appeal if at least two answers are funded."}, "getContributionsToSuccessfulFundings(uint256,uint256,address)": {"notice": "Gets contributions to the answers that are fully funded."}, "getDisputeFee(bytes32)": {"notice": "Gets the fee to create a dispute."}, "getFundingStatus(uint256,uint256,uint256)": {"notice": "Gets the information of a round of a question for a specific answer choice."}, "getMultipliers()": {"notice": "Returns stake multipliers."}, "getNumberOfRounds(uint256)": {"notice": "Gets the number of rounds of the specific question."}, "getRoundInfo(uint256,uint256)": {"notice": "Gets the information of a round of a question."}, "getTotalWithdrawableAmount(uint256,address,uint256)": {"notice": "Returns the sum of withdrawable amount."}, "handleFailedDisputeCreation(bytes32,address)": {"notice": "Cancels the arbitration in case the dispute could not be created. Requires a small deposit to cover arbitrum fees."}, "numberOfRulingOptions(uint256)": {"notice": "Returns number of possible ruling options. Valid rulings are [0, return value]."}, "questionIDToArbitrationID(bytes32)": {"notice": "Casts question ID into uint256 thus returning the related arbitration ID."}, "receiveArbitrationAcknowledgement(bytes32,address)": {"notice": "Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED."}, "receiveArbitrationCancelation(bytes32,address)": {"notice": "Receives the cancelation of the arbitration request for the given question and requester. TRUSTED."}, "relayRule(bytes32,address)": {"notice": "Relays the ruling to home proxy. Requires a small deposit to cover arbitrum fees."}, "requestArbitration(bytes32,uint256)": {"notice": "Requests arbitration for the given question and contested answer."}, "rule(uint256,uint256)": {"notice": "Rules a specified dispute. Can only be called by the arbitrator."}, "submitEvidence(uint256,string)": {"notice": "Allows to submit evidence for a particular question."}, "withdrawFeesAndRewards(uint256,address,uint256,uint256)": {"notice": "Sends the fee stake rewards and reimbursements proportional to the contributions made to the winner of a dispute. Reimburses contributions if there is no winner."}, "withdrawFeesAndRewardsForAllRounds(uint256,address,uint256)": {"notice": "Allows to withdraw any rewards or reimbursable fees for all rounds at once."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 380, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "governor", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 383, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrator", "offset": 0, "slot": "1", "type": "t_contract(IArbitrator)249"}, {"astId": 385, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitratorExtraData", "offset": 0, "slot": "2", "type": "t_bytes_storage"}, {"astId": 387, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "metaEvidenceUpdates", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 390, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "inbox", "offset": 0, "slot": "4", "type": "t_contract(IInbox)2849"}, {"astId": 392, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "l2GasLimit", "offset": 0, "slot": "5", "type": "t_uint256"}, {"astId": 394, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "gasPriceBid", "offset": 0, "slot": "6", "type": "t_uint256"}, {"astId": 396, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "surplusAmount", "offset": 0, "slot": "7", "type": "t_uint256"}, {"astId": 398, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "winner<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_uint256"}, {"astId": 400, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "loserMultiplier", "offset": 0, "slot": "9", "type": "t_uint256"}, {"astId": 402, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "loserAppealPeriodMultiplier", "offset": 0, "slot": "10", "type": "t_uint256"}, {"astId": 409, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrationRequests", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)351_storage))"}, {"astId": 416, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitratorDisputeIDToDisputeDetails", "offset": 0, "slot": "12", "type": "t_mapping(t_address,t_mapping(t_uint256,t_struct(DisputeDetails)356_storage))"}, {"astId": 420, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrationIDToDisputeExists", "offset": 0, "slot": "13", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 424, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrationIDToRequester", "offset": 0, "slot": "14", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 428, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrationCreatedBlock", "offset": 0, "slot": "15", "type": "t_mapping(t_uint256,t_uint256)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_struct(Round)376_storage)dyn_storage": {"base": "t_struct(Round)376_storage", "encoding": "dynamic_array", "label": "struct RealitioForeignProxyArb.Round[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_contract(IArbitrator)249": {"encoding": "inplace", "label": "contract IArbitrator", "numberOfBytes": "20"}, "t_contract(IInbox)2849": {"encoding": "inplace", "label": "contract IInbox", "numberOfBytes": "20"}, "t_enum(Status)330": {"encoding": "inplace", "label": "enum RealitioForeignProxyArb.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_uint256,t_struct(DisputeDetails)356_storage))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => struct RealitioForeignProxyArb.DisputeDetails))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_struct(DisputeDetails)356_storage)"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_uint256)"}, "t_mapping(t_address,t_struct(ArbitrationRequest)351_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioForeignProxyArb.ArbitrationRequest)", "numberOfBytes": "32", "value": "t_struct(ArbitrationRequest)351_storage"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_uint256,t_bool)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(ArbitrationRequest)351_storage))": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => mapping(address => struct RealitioForeignProxyArb.ArbitrationRequest))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ArbitrationRequest)351_storage)"}, "t_mapping(t_uint256,t_struct(DisputeDetails)356_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct RealitioForeignProxyArb.DisputeDetails)", "numberOfBytes": "32", "value": "t_struct(DisputeDetails)356_storage"}, "t_mapping(t_uint256,t_uint256)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(ArbitrationRequest)351_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyArb.ArbitrationRequest", "members": [{"astId": 333, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)330"}, {"astId": 335, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "deposit", "offset": 1, "slot": "0", "type": "t_uint248"}, {"astId": 337, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "disputeID", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 339, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "answer", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 343, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "rounds", "offset": 0, "slot": "3", "type": "t_array(t_struct(Round)376_storage)dyn_storage"}, {"astId": 346, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrator", "offset": 0, "slot": "4", "type": "t_contract(IArbitrator)249"}, {"astId": 348, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitratorExtraData", "offset": 0, "slot": "5", "type": "t_bytes_storage"}, {"astId": 350, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "metaEvidenceID", "offset": 0, "slot": "6", "type": "t_uint256"}], "numberOfBytes": "224"}, "t_struct(DisputeDetails)356_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyArb.DisputeDetails", "members": [{"astId": 353, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "arbitrationID", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 355, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "requester", "offset": 0, "slot": "1", "type": "t_address"}], "numberOfBytes": "64"}, "t_struct(Round)376_storage": {"encoding": "inplace", "label": "struct RealitioForeignProxyArb.Round", "members": [{"astId": 360, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "paidFees", "offset": 0, "slot": "0", "type": "t_mapping(t_uint256,t_uint256)"}, {"astId": 364, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "hasPaid", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)"}, {"astId": 370, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "contributions", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))"}, {"astId": 372, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "feeRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 375, "contract": "src/RealitioForeignProxyArb.sol:RealitioForeignProxyArb", "label": "fundedAnswers", "offset": 0, "slot": "4", "type": "t_array(t_uint256)dyn_storage"}], "numberOfBytes": "160"}, "t_uint248": {"encoding": "inplace", "label": "uint248", "numberOfBytes": "31"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}
{"address": "0xcB4B48d2A7a44247A00048963F169d2b4Ab045a6", "abi": [{"inputs": [{"internalType": "contract IRealitio", "name": "_realitio", "type": "address"}, {"internalType": "string", "name": "_metadata", "type": "string"}, {"internalType": "address", "name": "_foreignProxy", "type": "address"}, {"internalType": "uint256", "name": "_foreignChainId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "ArbitrationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}], "name": "ArbitrationFinished", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "ArbitratorAnswered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestAcknowledged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}], "name": "RequestCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "RequestNotified", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "_requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "_reason", "type": "string"}], "name": "RequestRejected", "type": "event"}, {"inputs": [], "name": "MIN_GAS_LIMIT", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignChainId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "foreignProxy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleNotifiedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "handleRejectedRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "messenger", "outputs": [{"internalType": "contract ICrossDomainMessenger", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "metadata", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "questionIDToRequester", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "realitio", "outputs": [{"internalType": "contract IRealitio", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_answer", "type": "bytes32"}], "name": "receiveArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}], "name": "receiveArbitrationFailure", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "address", "name": "_requester", "type": "address"}, {"internalType": "uint256", "name": "_maxPrevious", "type": "uint256"}], "name": "receiveArbitrationRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_questionID", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastHistoryHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "_lastAnswerOrCommitmentID", "type": "bytes32"}, {"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "reportArbitrationAnswer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "requests", "outputs": [{"internalType": "enum RealitioHomeProxyOptimism.Status", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "arbitratorAnswer", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0x47431a79e14eeae4a20f1a48353ebaa8a1595b2581af9ace36e5b301b2c86e3d", "receipt": {"to": null, "from": "0x0efFC4A996045aff0489774051f94f42F2D6dfc9", "contractAddress": "0xcB4B48d2A7a44247A00048963F169d2b4Ab045a6", "transactionIndex": 3, "gasUsed": "1188508", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x929919743d96cfe27b9cdfe1f5c0f9bc0ab51e2dfedb1c5610cc8d54ccc34b20", "transactionHash": "0x47431a79e14eeae4a20f1a48353ebaa8a1595b2581af9ace36e5b301b2c86e3d", "logs": [], "blockNumber": 9331200, "cumulativeGasUsed": "1347754", "status": 1, "byzantium": true}, "args": ["0xB920dBedE88B42aA77eE55ebcE3671132ee856fC", "{\"tos\":\"ipfs://QmNV5NWwCudYKfiHuhdWxccrPyxs4DnbLGQace2oMKHkZv/Question_Resolution_Policy.pdf\", \"foreignProxy\":true}", "0x122D6B4197531bF4e9314fD00259b1dc1Db7954D", 1], "numDeployments": 1, "solcInputHash": "df6967adfff140ad55877edd79fe5918", "metadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IRealitio\",\"name\":\"_realitio\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"_metadata\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"_foreignProxy\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_foreignChainId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"ArbitrationFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"}],\"name\":\"ArbitrationFinished\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"_answer\",\"type\":\"bytes32\"}],\"name\":\"ArbitratorAnswered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"RequestAcknowledged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"RequestCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"RequestNotified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_reason\",\"type\":\"string\"}],\"name\":\"RequestRejected\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"MIN_GAS_LIMIT\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"foreignChainId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"foreignProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleNotifiedRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"handleRejectedRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"messenger\",\"outputs\":[{\"internalType\":\"contract ICrossDomainMessenger\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"metadata\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"questionIDToRequester\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"realitio\",\"outputs\":[{\"internalType\":\"contract IRealitio\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_answer\",\"type\":\"bytes32\"}],\"name\":\"receiveArbitrationAnswer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"}],\"name\":\"receiveArbitrationFailure\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_requester\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_maxPrevious\",\"type\":\"uint256\"}],\"name\":\"receiveArbitrationRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_questionID\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_lastHistoryHash\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"_lastAnswerOrCommitmentID\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_lastAnswerer\",\"type\":\"address\"}],\"name\":\"reportArbitrationAnswer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"requests\",\"outputs\":[{\"internalType\":\"enum RealitioHomeProxyOptimism.Status\",\"name\":\"status\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"arbitratorAnswer\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"https://docs.optimism.io/builders/app-developers/bridging/messaging\",\"events\":{\"ArbitrationFailed(bytes32,address)\":{\"details\":\"This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"ArbitrationFinished(bytes32)\":{\"params\":{\"_questionID\":\"The ID of the question.\"}},\"ArbitratorAnswered(bytes32,bytes32)\":{\"params\":{\"_answer\":\"The answer from the arbitrator.\",\"_questionID\":\"The ID of the question.\"}},\"RequestAcknowledged(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"RequestCanceled(bytes32,address)\":{\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"RequestNotified(bytes32,address,uint256)\":{\"params\":{\"_maxPrevious\":\"The maximum value of the previous bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the arbitration requester.\"}},\"RequestRejected(bytes32,address,uint256,string)\":{\"details\":\"This can happen if the current bond for the question is higher than maxPrevious or if the question is already finalized.\",\"params\":{\"_maxPrevious\":\"The maximum value of the current bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_reason\":\"The reason why the request was rejected.\",\"_requester\":\"The address of the arbitration requester.\"}}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"params\":{\"_foreignChainId\":\"The ID of foreign chain (Sepolia/Mainnet).\",\"_foreignProxy\":\"Address of the proxy on L1. Note that it needs to be precomputed before deployment by using deployer's address and tx nonce.\",\"_metadata\":\"Metadata for Realitio.\",\"_realitio\":\"Realitio contract address.\"}},\"handleNotifiedRequest(bytes32,address)\":{\"details\":\"Relays arbitration request back to L1 after it has been notified by Realitio for a given question.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"handleRejectedRequest(bytes32,address)\":{\"details\":\"Relays arbitration request back to L1 after it has been rejected. Reasons why the request might be rejected:  - The question does not exist  - The question was not answered yet  - The quesiton bond value changed while the arbitration was being requested  - Another request was already accepted\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"receiveArbitrationAnswer(bytes32,bytes32)\":{\"params\":{\"_answer\":\"The answer from the arbitrator.\",\"_questionID\":\"The ID of the question.\"}},\"receiveArbitrationFailure(bytes32,address)\":{\"details\":\"Currently this can happen only if the arbitration cost increased.\",\"params\":{\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"receiveArbitrationRequest(bytes32,address,uint256)\":{\"details\":\"Receives the requested arbitration for a question. TRUSTED.\",\"params\":{\"_maxPrevious\":\"The maximum value of the previous bond for the question.\",\"_questionID\":\"The ID of the question.\",\"_requester\":\"The address of the user that requested arbitration.\"}},\"reportArbitrationAnswer(bytes32,bytes32,bytes32,address)\":{\"details\":\"The Realitio contract validates the input parameters passed to this method, so making this publicly accessible is safe.\",\"params\":{\"_lastAnswerOrCommitmentID\":\"The last answer given, or its commitment ID if it was a commitment, to the question in the Realitio contract.\",\"_lastAnswerer\":\"The last answerer to the question in the Realitio contract.\",\"_lastHistoryHash\":\"The history hash given with the last answer to the question in the Realitio contract.\",\"_questionID\":\"The ID of the question.\"}}},\"stateVariables\":{\"foreignChainId\":{\"details\":\"ID of the foreign chain, required for Realitio.\"},\"foreignProxy\":{\"details\":\"Address of the proxy on L1. Note that it needs to be precomputed before deployment by using deployer's address and tx nonce.\"},\"metadata\":{\"details\":\"Metadata for Realitio interface.\"},\"questionIDToRequester\":{\"details\":\"Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]\"},\"realitio\":{\"details\":\"The address of the Realitio contract (v3.0 required). TRUSTED.\"},\"requests\":{\"details\":\"Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]\"}},\"title\":\"Arbitration proxy for Realitio on home chain (eg. Optimism, Unichain, Redstone etc).\",\"version\":1},\"userdoc\":{\"events\":{\"ArbitrationFailed(bytes32,address)\":{\"notice\":\"To be emitted when the dispute could not be created on the Foreign Chain.\"},\"ArbitrationFinished(bytes32)\":{\"notice\":\"To be emitted when reporting the arbitrator answer to Realitio.\"},\"ArbitratorAnswered(bytes32,bytes32)\":{\"notice\":\"To be emitted when receiving the answer from the arbitrator.\"},\"RequestAcknowledged(bytes32,address)\":{\"notice\":\"To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\"},\"RequestCanceled(bytes32,address)\":{\"notice\":\"To be emitted when the arbitration request is canceled.\"},\"RequestNotified(bytes32,address,uint256)\":{\"notice\":\"To be emitted when the Realitio contract has been notified of an arbitration request.\"},\"RequestRejected(bytes32,address,uint256,string)\":{\"notice\":\"To be emitted when arbitration request is rejected.\"}},\"kind\":\"user\",\"methods\":{\"constructor\":{\"notice\":\"Creates an arbitration proxy on the home chain.\"},\"receiveArbitrationAnswer(bytes32,bytes32)\":{\"notice\":\"Receives an answer to a specified question. TRUSTED.\"},\"receiveArbitrationFailure(bytes32,address)\":{\"notice\":\"Receives a failed attempt to request arbitration. TRUSTED.\"},\"reportArbitrationAnswer(bytes32,bytes32,bytes32,address)\":{\"notice\":\"Reports the answer provided by the arbitrator to a specified question.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/0.8/RealitioHomeProxyOptimism.sol\":\"RealitioHomeProxyOptimism\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@kleros/erc-792/contracts/IArbitrable.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrator.sol\\\";\\n\\n/**\\n * @title IArbitrable\\n * Arbitrable interface.\\n * When developing arbitrable contracts, we need to:\\n * - Define the action taken when a ruling is received by the contract.\\n * - Allow dispute creation. For this a function must call arbitrator.createDispute{value: _fee}(_choices,_extraData);\\n */\\ninterface IArbitrable {\\n    /**\\n     * @dev To be raised when a ruling is given.\\n     * @param _arbitrator The arbitrator giving the ruling.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling The ruling which was given.\\n     */\\n    event Ruling(IArbitrator indexed _arbitrator, uint256 indexed _disputeID, uint256 _ruling);\\n\\n    /**\\n     * @dev Give a ruling for a dispute. Must be called by the arbitrator.\\n     * The purpose of this function is to ensure that the address calling it has the right to rule on the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _ruling Ruling given by the arbitrator. Note that 0 is reserved for \\\"Not able/wanting to make a decision\\\".\\n     */\\n    function rule(uint256 _disputeID, uint256 _ruling) external;\\n}\\n\",\"keccak256\":\"0x1803a3433a78c509b20bd9477a2c60a71b2ce1ee7e17eb0ef0601618a8a72526\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/IArbitrator.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: [@remedcu*]\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\n\\npragma solidity >=0.7;\\n\\nimport \\\"./IArbitrable.sol\\\";\\n\\n/**\\n * @title Arbitrator\\n * Arbitrator abstract contract.\\n * When developing arbitrator contracts we need to:\\n * - Define the functions for dispute creation (createDispute) and appeal (appeal). Don't forget to store the arbitrated contract and the disputeID (which should be unique, may nbDisputes).\\n * - Define the functions for cost display (arbitrationCost and appealCost).\\n * - Allow giving rulings. For this a function must call arbitrable.rule(disputeID, ruling).\\n */\\ninterface IArbitrator {\\n    enum DisputeStatus {Waiting, Appealable, Solved}\\n\\n    /**\\n     * @dev To be emitted when a dispute is created.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event DisputeCreation(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when a dispute can be appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealPossible(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev To be emitted when the current ruling is appealed.\\n     * @param _disputeID ID of the dispute.\\n     * @param _arbitrable The contract which created the dispute.\\n     */\\n    event AppealDecision(uint256 indexed _disputeID, IArbitrable indexed _arbitrable);\\n\\n    /**\\n     * @dev Create a dispute. Must be called by the arbitrable contract.\\n     * Must be paid at least arbitrationCost(_extraData).\\n     * @param _choices Amount of choices the arbitrator can make in this dispute.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return disputeID ID of the dispute created.\\n     */\\n    function createDispute(uint256 _choices, bytes calldata _extraData) external payable returns (uint256 disputeID);\\n\\n    /**\\n     * @dev Compute the cost of arbitration. It is recommended not to increase it often, as it can be highly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function arbitrationCost(bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Appeal a ruling. Note that it has to be called before the arbitrator contract calls rule.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give extra info on the appeal.\\n     */\\n    function appeal(uint256 _disputeID, bytes calldata _extraData) external payable;\\n\\n    /**\\n     * @dev Compute the cost of appeal. It is recommended not to increase it often, as it can be higly time and gas consuming for the arbitrated contracts to cope with fee augmentation.\\n     * @param _disputeID ID of the dispute to be appealed.\\n     * @param _extraData Can be used to give additional info on the dispute to be created.\\n     * @return cost Amount to be paid.\\n     */\\n    function appealCost(uint256 _disputeID, bytes calldata _extraData) external view returns (uint256 cost);\\n\\n    /**\\n     * @dev Compute the start and end of the dispute's current or next appeal period, if possible. If not known or appeal is impossible: should return (0, 0).\\n     * @param _disputeID ID of the dispute.\\n     * @return start The start of the period.\\n     * @return end The end of the period.\\n     */\\n    function appealPeriod(uint256 _disputeID) external view returns (uint256 start, uint256 end);\\n\\n    /**\\n     * @dev Return the status of a dispute.\\n     * @param _disputeID ID of the dispute to rule.\\n     * @return status The status of the dispute.\\n     */\\n    function disputeStatus(uint256 _disputeID) external view returns (DisputeStatus status);\\n\\n    /**\\n     * @dev Return the current ruling of a dispute. This is useful for parties to know if they should appeal.\\n     * @param _disputeID ID of the dispute.\\n     * @return ruling The ruling which has been given or the one which will be given if there is no appeal.\\n     */\\n    function currentRuling(uint256 _disputeID) external view returns (uint256 ruling);\\n}\\n\",\"keccak256\":\"0x240a4142f9ec379da0333dfc82409b7b058cff9ea118368eb5e8f15447996c1e\",\"license\":\"MIT\"},\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\":{\"content\":\"/**\\n * @authors: [@ferittuncer, @hbarcelos]\\n * @reviewers: []\\n * @auditors: []\\n * @bounties: []\\n * @deployments: []\\n * SPDX-License-Identifier: MIT\\n */\\npragma solidity >=0.7;\\n\\nimport \\\"../IArbitrator.sol\\\";\\n\\n/** @title IEvidence\\n *  ERC-1497: Evidence Standard\\n */\\ninterface IEvidence {\\n    /**\\n     * @dev To be emitted when meta-evidence is submitted.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidence A link to the meta-evidence JSON.\\n     */\\n    event MetaEvidence(uint256 indexed _metaEvidenceID, string _evidence);\\n\\n    /**\\n     * @dev To be raised when evidence is submitted. Should point to the resource (evidences are not to be stored on chain due to gas considerations).\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _evidenceGroupID Unique identifier of the evidence group the evidence belongs to.\\n     * @param _party The address of the party submiting the evidence. Note that 0x0 refers to evidence not submitted by any party.\\n     * @param _evidence A URI to the evidence JSON file whose name should be its keccak256 hash followed by .json.\\n     */\\n    event Evidence(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _evidenceGroupID,\\n        address indexed _party,\\n        string _evidence\\n    );\\n\\n    /**\\n     * @dev To be emitted when a dispute is created to link the correct meta-evidence to the disputeID.\\n     * @param _arbitrator The arbitrator of the contract.\\n     * @param _disputeID ID of the dispute in the Arbitrator contract.\\n     * @param _metaEvidenceID Unique identifier of meta-evidence.\\n     * @param _evidenceGroupID Unique identifier of the evidence group that is linked to this dispute.\\n     */\\n    event Dispute(\\n        IArbitrator indexed _arbitrator,\\n        uint256 indexed _disputeID,\\n        uint256 _metaEvidenceID,\\n        uint256 _evidenceGroupID\\n    );\\n}\\n\",\"keccak256\":\"0x1ccedf5213730632540c748486637d7b1977ee73375818bf498a8276ca49dd13\",\"license\":\"MIT\"},\"src/0.8/RealitioHomeProxyOptimism.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\n/**\\n *  @authors: [@anmol-dhiman]\\n *  @reviewers: []\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\npragma solidity 0.8.25;\\n\\nimport \\\"./interfaces/IRealitio.sol\\\";\\nimport {IForeignArbitrationProxy, IHomeArbitrationProxy} from \\\"./interfaces/IArbitrationProxies.sol\\\";\\nimport {ICrossDomainMessenger} from \\\"./interfaces/optimism/ICrossDomainMessenger.sol\\\";\\n\\n/**\\n * @title Arbitration proxy for Realitio on home chain (eg. Optimism, Unichain, Redstone etc).\\n * @dev https://docs.optimism.io/builders/app-developers/bridging/messaging\\n */\\ncontract RealitioHomeProxyOptimism is IHomeArbitrationProxy {\\n    ICrossDomainMessenger public constant messenger = ICrossDomainMessenger(0x4200000000000000000000000000000000000007); // Precompile for L2 -> L1 communication, identical for all OP L2s\\n    uint32 public constant MIN_GAS_LIMIT = 1500000; // Gas limit of the transaction call.\\n\\n    /// @dev The address of the Realitio contract (v3.0 required). TRUSTED.\\n    IRealitio public immutable realitio;\\n    /// @dev Address of the proxy on L1. Note that it needs to be precomputed before deployment by using deployer's address and tx nonce.\\n    address public immutable foreignProxy;\\n\\n    /// @dev ID of the foreign chain, required for Realitio.\\n    bytes32 public immutable foreignChainId;\\n\\n    /// @dev Metadata for Realitio interface.\\n    string public override metadata;\\n\\n    enum Status {\\n        None,\\n        Rejected,\\n        Notified,\\n        AwaitingRuling,\\n        Ruled,\\n        Finished\\n    }\\n\\n    struct Request {\\n        Status status;\\n        bytes32 arbitratorAnswer;\\n    }\\n\\n    /// @dev Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]\\n    mapping(bytes32 => mapping(address => Request)) public requests;\\n\\n    /// @dev Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]\\n    mapping(bytes32 => address) public questionIDToRequester;\\n\\n    modifier onlyForeignProxy() {\\n        require(msg.sender == address(messenger), \\\"NOT_MESSENGER\\\");\\n        require(messenger.xDomainMessageSender() == foreignProxy, \\\"Can only be called by Foreign Proxy\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @notice Creates an arbitration proxy on the home chain.\\n     * @param _realitio Realitio contract address.\\n     * @param _metadata Metadata for Realitio.\\n     * @param _foreignProxy Address of the proxy on L1. Note that it needs to be precomputed before deployment by using deployer's address and tx nonce.\\n     * @param _foreignChainId The ID of foreign chain (Sepolia/Mainnet).\\n     */\\n    constructor(IRealitio _realitio, string memory _metadata, address _foreignProxy, uint256 _foreignChainId) {\\n        realitio = _realitio;\\n        metadata = _metadata;\\n        foreignProxy = _foreignProxy;\\n        foreignChainId = bytes32(_foreignChainId);\\n    }\\n\\n    /**\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the user that requested arbitration.\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\n     */\\n    function receiveArbitrationRequest(\\n        bytes32 _questionID,\\n        address _requester,\\n        uint256 _maxPrevious\\n    ) external override onlyForeignProxy {\\n        Request storage request = requests[_questionID][_requester];\\n        require(request.status == Status.None, \\\"Request already exists\\\");\\n\\n        try realitio.notifyOfArbitrationRequest(_questionID, _requester, _maxPrevious) {\\n            request.status = Status.Notified;\\n            questionIDToRequester[_questionID] = _requester;\\n\\n            emit RequestNotified(_questionID, _requester, _maxPrevious);\\n        } catch Error(string memory reason) {\\n            /*\\n             * Will fail if:\\n             *  - The question does not exist.\\n             *  - The question was not answered yet.\\n             *  - Another request was already accepted.\\n             *  - Someone increased the bond on the question to a value > _maxPrevious\\n             */\\n            request.status = Status.Rejected;\\n\\n            emit RequestRejected(_questionID, _requester, _maxPrevious, reason);\\n        } catch {\\n            // In case `reject` did not have a reason string or some other error happened\\n            request.status = Status.Rejected;\\n\\n            emit RequestRejected(_questionID, _requester, _maxPrevious, \\\"\\\");\\n        }\\n    }\\n\\n    /**\\n     * @dev Relays arbitration request back to L1 after it has been notified by Realitio for a given question.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the user that requested arbitration.\\n     */\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external override {\\n        Request storage request = requests[_questionID][_requester];\\n        require(request.status == Status.Notified, \\\"Invalid request status\\\");\\n\\n        request.status = Status.AwaitingRuling;\\n\\n        bytes4 selector = IForeignArbitrationProxy.receiveArbitrationAcknowledgement.selector;\\n        bytes memory data = abi.encodeWithSelector(selector, _questionID, _requester);\\n        messenger.sendMessage(foreignProxy, data, MIN_GAS_LIMIT);\\n        emit RequestAcknowledged(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @dev Relays arbitration request back to L1 after it has been rejected.\\n     * Reasons why the request might be rejected:\\n     *  - The question does not exist\\n     *  - The question was not answered yet\\n     *  - The quesiton bond value changed while the arbitration was being requested\\n     *  - Another request was already accepted\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the user that requested arbitration.\\n     */\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external override {\\n        Request storage request = requests[_questionID][_requester];\\n        require(request.status == Status.Rejected, \\\"Invalid request status\\\");\\n\\n        // At this point, only the request.status is set, simply resetting the status to Status.None is enough.\\n        request.status = Status.None;\\n\\n        bytes4 selector = IForeignArbitrationProxy.receiveArbitrationCancelation.selector;\\n        bytes memory data = abi.encodeWithSelector(selector, _questionID, _requester);\\n        messenger.sendMessage(foreignProxy, data, MIN_GAS_LIMIT);\\n        emit RequestCanceled(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\n     * @dev Currently this can happen only if the arbitration cost increased.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the user that requested arbitration.\\n     */\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external override onlyForeignProxy {\\n        Request storage request = requests[_questionID][_requester];\\n        require(request.status == Status.AwaitingRuling, \\\"Invalid request status\\\");\\n\\n        // At this point, only the request.status is set, simply resetting the status to Status.None is enough.\\n        request.status = Status.None;\\n\\n        realitio.cancelArbitration(_questionID);\\n\\n        emit ArbitrationFailed(_questionID, _requester);\\n    }\\n\\n    /**\\n     * @notice Receives an answer to a specified question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external override onlyForeignProxy {\\n        address requester = questionIDToRequester[_questionID];\\n        Request storage request = requests[_questionID][requester];\\n        require(request.status == Status.AwaitingRuling, \\\"Invalid request status\\\");\\n\\n        request.status = Status.Ruled;\\n        request.arbitratorAnswer = _answer;\\n\\n        emit ArbitratorAnswered(_questionID, _answer);\\n    }\\n\\n    /**\\n     * @notice Reports the answer provided by the arbitrator to a specified question.\\n     * @dev The Realitio contract validates the input parameters passed to this method,\\n     * so making this publicly accessible is safe.\\n     * @param _questionID The ID of the question.\\n     * @param _lastHistoryHash The history hash given with the last answer to the question in the Realitio contract.\\n     * @param _lastAnswerOrCommitmentID The last answer given, or its commitment ID if it was a commitment,\\n     * to the question in the Realitio contract.\\n     * @param _lastAnswerer The last answerer to the question in the Realitio contract.\\n     */\\n    function reportArbitrationAnswer(\\n        bytes32 _questionID,\\n        bytes32 _lastHistoryHash,\\n        bytes32 _lastAnswerOrCommitmentID,\\n        address _lastAnswerer\\n    ) external {\\n        address requester = questionIDToRequester[_questionID];\\n        Request storage request = requests[_questionID][requester];\\n        require(request.status == Status.Ruled, \\\"Arbitrator has not ruled yet\\\");\\n\\n        realitio.assignWinnerAndSubmitAnswerByArbitrator(\\n            _questionID,\\n            request.arbitratorAnswer,\\n            requester,\\n            _lastHistoryHash,\\n            _lastAnswerOrCommitmentID,\\n            _lastAnswerer\\n        );\\n\\n        request.status = Status.Finished;\\n\\n        emit ArbitrationFinished(_questionID);\\n    }\\n}\\n\",\"keccak256\":\"0x08bc091c7f48f3f7ae883737f1961bccbb22a15d399f6bc70c2b9c35461bf418\",\"license\":\"MIT\"},\"src/0.8/interfaces/IArbitrationProxies.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity 0.8.25;\\n\\nimport {IArbitrable} from \\\"@kleros/erc-792/contracts/IArbitrable.sol\\\";\\nimport {IEvidence} from \\\"@kleros/erc-792/contracts/erc-1497/IEvidence.sol\\\";\\n\\ninterface IHomeArbitrationProxy {\\n    /**\\n     * @notice To be emitted when the Realitio contract has been notified of an arbitration request.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the previous bond for the question.\\n     */\\n    event RequestNotified(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice To be emitted when arbitration request is rejected.\\n     * @dev This can happen if the current bond for the question is higher than maxPrevious\\n     * or if the question is already finalized.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question.\\n     * @param _reason The reason why the request was rejected.\\n     */\\n    event RequestRejected(\\n        bytes32 indexed _questionID,\\n        address indexed _requester,\\n        uint256 _maxPrevious,\\n        string _reason\\n    );\\n\\n    /**\\n     * @notice To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestAcknowledged(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the arbitration request is canceled.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event RequestCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when the dispute could not be created on the Foreign Chain.\\n     * @dev This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice To be emitted when receiving the answer from the arbitrator.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    event ArbitratorAnswered(bytes32 indexed _questionID, bytes32 _answer);\\n\\n    /**\\n     * @notice To be emitted when reporting the arbitrator answer to Realitio.\\n     * @param _questionID The ID of the question.\\n     */\\n    event ArbitrationFinished(bytes32 indexed _questionID);\\n\\n    /**\\n     * @dev Receives the requested arbitration for a question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function receiveArbitrationRequest(bytes32 _questionID, address _requester, uint256 _maxPrevious) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been notified to Realitio for a given question.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\n     * the bridge and cannot send messages back to it.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleNotifiedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Handles arbitration request after it has been rejected.\\n     * @dev This method exists because `receiveArbitrationRequest` is called by\\n     * the bridge and cannot send messages back to it.\\n     * Reasons why the request might be rejected:\\n     *  - The question does not exist\\n     *  - The question was not answered yet\\n     *  - The quesiton bond value changed while the arbitration was being requested\\n     *  - Another request was already accepted\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleRejectedRequest(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives a failed attempt to request arbitration. TRUSTED.\\n     * @dev Currently this can happen only if the arbitration cost increased.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationFailure(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the answer to a specified question. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _answer The answer from the arbitrator.\\n     */\\n    function receiveArbitrationAnswer(bytes32 _questionID, bytes32 _answer) external;\\n\\n    /** @notice Provides a string of json-encoded metadata with the following properties:\\n         - tos: A URI representing the location of a terms-of-service document for the arbitrator.\\n         - template_hashes: An array of hashes of templates supported by the arbitrator. If you have a numerical ID for a template registered with Reality.eth, you can look up this hash by calling the Reality.eth template_hashes() function.\\n     *  @dev Template_hashes won't be used by this home proxy. \\n     */\\n    function metadata() external view returns (string calldata);\\n}\\n\\ninterface IForeignArbitrationProxy {\\n    /**\\n     * @notice Should be emitted when the arbitration is requested.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    event ArbitrationRequested(bytes32 indexed _questionID, address indexed _requester, uint256 _maxPrevious);\\n\\n    /**\\n     * @notice Should be emitted when the dispute is created.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     * @param _disputeID The ID of the dispute.\\n     */\\n    event ArbitrationCreated(bytes32 indexed _questionID, address indexed _requester, uint256 indexed _disputeID);\\n\\n    /**\\n     * @notice Should be emitted when the arbitration is canceled by the Home Chain.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationCanceled(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the dispute could not be created.\\n     * @dev This will happen if there is an increase in the arbitration fees\\n     * between the time the arbitration is made and the time it is acknowledged.\\n     * @param _questionID The ID of the question with the request for arbitration.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    event ArbitrationFailed(bytes32 indexed _questionID, address indexed _requester);\\n\\n    /**\\n     * @notice Should be emitted when the ruling is relayed to home proxy manually. Some implementations may not emit this event.\\n     * @param _questionID The ID of the question with the ruling to relay.\\n     * @param _ruling Ruling converted into Realitio format.\\n     */\\n    event RulingRelayed(bytes32 _questionID, bytes32 _ruling);\\n\\n    /**\\n     * @notice Requests arbitration for the given question and contested answer.\\n     * @param _questionID The ID of the question.\\n     * @param _maxPrevious The maximum value of the current bond for the question. The arbitration request will get rejected if the current bond is greater than _maxPrevious. If set to 0, _maxPrevious is ignored.\\n     */\\n    function requestArbitration(bytes32 _questionID, uint256 _maxPrevious) external payable;\\n\\n    /**\\n     * @notice Receives the acknowledgement of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationAcknowledgement(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Receives the cancelation of the arbitration request for the given question and requester. TRUSTED.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function receiveArbitrationCancelation(bytes32 _questionID, address _requester) external;\\n\\n    /**\\n     * @notice Cancels the arbitration in case the dispute could not be created.\\n     * @param _questionID The ID of the question.\\n     * @param _requester The address of the arbitration requester.\\n     */\\n    function handleFailedDisputeCreation(bytes32 _questionID, address _requester) external payable;\\n\\n    /**\\n     * @notice Gets the fee to create a dispute.\\n     * @param _questionID the ID of the question.\\n     * @return The fee to create a dispute.\\n     */\\n    function getDisputeFee(bytes32 _questionID) external view returns (uint256);\\n}\\n\\n\",\"keccak256\":\"0x4f2dd0c7372274a2382fad70cc8165c7c970e4d859f604b8441ebfe091fa62e3\",\"license\":\"MIT\"},\"src/0.8/interfaces/IRealitio.sol\":{\"content\":\"/* solhint-disable var-name-mixedcase */\\n// SPDX-License-Identifier: MIT\\n\\n/** \\n *  Interface of https://github.com/RealityETH/reality-eth-monorepo/blob/main/packages/contracts/flat/RealityETH-3.0.sol.\\n *  @reviewers: [@hbarcelos, @fnanni-0, @nix1g, @unknownunknown1, @ferittuncer, @jaybuidl]\\n *  @auditors: []\\n *  @bounties: []\\n *  @deployments: []\\n */\\n\\npragma solidity 0.8.25;\\n\\ninterface IRealitio {\\n    event LogNewAnswer(\\n        bytes32 answer,\\n        bytes32 indexed question_id,\\n        bytes32 history_hash,\\n        address indexed user,\\n        uint256 bond,\\n        uint256 ts,\\n        bool is_commitment\\n    );\\n\\n    event LogNewTemplate(uint256 indexed template_id, address indexed user, string question_text);\\n\\n    event LogNewQuestion(\\n        bytes32 indexed question_id,\\n        address indexed user,\\n        uint256 template_id,\\n        string question,\\n        bytes32 indexed content_hash,\\n        address arbitrator,\\n        uint32 timeout,\\n        uint32 opening_ts,\\n        uint256 nonce,\\n        uint256 created\\n    );\\n\\n    /**\\n     * @dev The arbitrator contract is trusted to only call this if they've been paid, and tell us who paid them.\\n     * @notice Notify the contract that the arbitrator has been paid for a question, freezing it pending their decision.\\n     * @param question_id The ID of the question.\\n     * @param requester The account that requested arbitration.\\n     * @param max_previous If specified, reverts if a bond higher than this was submitted after you sent your transaction.\\n     */\\n    function notifyOfArbitrationRequest(bytes32 question_id, address requester, uint256 max_previous) external;\\n\\n    /**\\n     * @notice Cancel a previously-requested arbitration and extend the timeout\\n     * @dev Useful when doing arbitration across chains that can't be requested atomically\\n     * @param question_id The ID of the question\\n     */\\n    function cancelArbitration(bytes32 question_id) external;\\n\\n    /**\\n     * @notice Submit the answer for a question, for use by the arbitrator, working out the appropriate winner based on the last answer details.\\n     * @dev Doesn't require (or allow) a bond.\\n     * @param question_id The ID of the question\\n     * @param answer The answer, encoded into bytes32\\n     * @param payee_if_wrong The account to be credited as winner if the last answer given is wrong, usually the account that paid the arbitrator\\n     * @param last_history_hash The history hash before the final one\\n     * @param last_answer_or_commitment_id The last answer given, or the commitment ID if it was a commitment.\\n     * @param last_answerer The address that supplied the last answer\\n     */\\n    function assignWinnerAndSubmitAnswerByArbitrator(\\n        bytes32 question_id,\\n        bytes32 answer,\\n        address payee_if_wrong,\\n        bytes32 last_history_hash,\\n        bytes32 last_answer_or_commitment_id,\\n        address last_answerer\\n    ) external;\\n}\\n\",\"keccak256\":\"0x8dbb0a31da3b2d24aa25868559be12a451bf9d677c4f302337cf48e451f5f6ed\",\"license\":\"MIT\"},\"src/0.8/interfaces/optimism/ICrossDomainMessenger.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\npragma solidity 0.8.25;\\n// @dev https://github.com/ethereum-optimism/optimism/blob/v1.7.7/packages/contracts-bedrock/src/universal/CrossDomainMessenger.sol\\ninterface ICrossDomainMessenger {\\n    function sendMessage(\\n        address _target,\\n        bytes calldata _message,\\n        uint32 _gasLimit\\n    ) external;\\n\\n    function xDomainMessageSender() external view returns (address);\\n}\\n\",\"keccak256\":\"0xd43d55bf1e86eecfde1319dc0b684d133cf958d3467b14642de26e8d63cc79b6\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "https://docs.optimism.io/builders/app-developers/bridging/messaging", "events": {"ArbitrationFailed(bytes32,address)": {"details": "This will happen if the arbitration fee increases in between the arbitration request and acknowledgement.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "ArbitrationFinished(bytes32)": {"params": {"_questionID": "The ID of the question."}}, "ArbitratorAnswered(bytes32,bytes32)": {"params": {"_answer": "The answer from the arbitrator.", "_questionID": "The ID of the question."}}, "RequestAcknowledged(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "RequestCanceled(bytes32,address)": {"params": {"_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "RequestNotified(bytes32,address,uint256)": {"params": {"_maxPrevious": "The maximum value of the previous bond for the question.", "_questionID": "The ID of the question.", "_requester": "The address of the arbitration requester."}}, "RequestRejected(bytes32,address,uint256,string)": {"details": "This can happen if the current bond for the question is higher than maxPrevious or if the question is already finalized.", "params": {"_maxPrevious": "The maximum value of the current bond for the question.", "_questionID": "The ID of the question.", "_reason": "The reason why the request was rejected.", "_requester": "The address of the arbitration requester."}}}, "kind": "dev", "methods": {"constructor": {"params": {"_foreignChainId": "The ID of foreign chain (Sepolia/Mainnet).", "_foreignProxy": "Address of the proxy on L1. Note that it needs to be precomputed before deployment by using deployer's address and tx nonce.", "_metadata": "Metadata for Realitio.", "_realitio": "Realitio contract address."}}, "handleNotifiedRequest(bytes32,address)": {"details": "Relays arbitration request back to L1 after it has been notified by Realitio for a given question.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "handleRejectedRequest(bytes32,address)": {"details": "Relays arbitration request back to L1 after it has been rejected. Reasons why the request might be rejected:  - The question does not exist  - The question was not answered yet  - The quesiton bond value changed while the arbitration was being requested  - Another request was already accepted", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "receiveArbitrationAnswer(bytes32,bytes32)": {"params": {"_answer": "The answer from the arbitrator.", "_questionID": "The ID of the question."}}, "receiveArbitrationFailure(bytes32,address)": {"details": "Currently this can happen only if the arbitration cost increased.", "params": {"_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "receiveArbitrationRequest(bytes32,address,uint256)": {"details": "Receives the requested arbitration for a question. TRUSTED.", "params": {"_maxPrevious": "The maximum value of the previous bond for the question.", "_questionID": "The ID of the question.", "_requester": "The address of the user that requested arbitration."}}, "reportArbitrationAnswer(bytes32,bytes32,bytes32,address)": {"details": "The Realitio contract validates the input parameters passed to this method, so making this publicly accessible is safe.", "params": {"_lastAnswerOrCommitmentID": "The last answer given, or its commitment ID if it was a commitment, to the question in the Realitio contract.", "_lastAnswerer": "The last answerer to the question in the Realitio contract.", "_lastHistoryHash": "The history hash given with the last answer to the question in the Realitio contract.", "_questionID": "The ID of the question."}}}, "stateVariables": {"foreignChainId": {"details": "ID of the foreign chain, required for Realitio."}, "foreignProxy": {"details": "Address of the proxy on L1. Note that it needs to be precomputed before deployment by using deployer's address and tx nonce."}, "metadata": {"details": "Metadata for Realitio interface."}, "questionIDToRequester": {"details": "Associates a question ID with the requester who succeeded in requesting arbitration. questionIDToRequester[questionID]"}, "realitio": {"details": "The address of the Realitio contract (v3.0 required). TRUSTED."}, "requests": {"details": "Associates an arbitration request with a question ID and a requester address. requests[questionID][requester]"}}, "title": "Arbitration proxy for Realitio on home chain (eg. Optimism, Unichain, Redstone etc).", "version": 1}, "userdoc": {"events": {"ArbitrationFailed(bytes32,address)": {"notice": "To be emitted when the dispute could not be created on the Foreign Chain."}, "ArbitrationFinished(bytes32)": {"notice": "To be emitted when reporting the arbitrator answer to <PERSON><PERSON><PERSON>."}, "ArbitratorAnswered(bytes32,bytes32)": {"notice": "To be emitted when receiving the answer from the arbitrator."}, "RequestAcknowledged(bytes32,address)": {"notice": "To be emitted when the arbitration request acknowledgement is sent to the Foreign Chain."}, "RequestCanceled(bytes32,address)": {"notice": "To be emitted when the arbitration request is canceled."}, "RequestNotified(bytes32,address,uint256)": {"notice": "To be emitted when the Realitio contract has been notified of an arbitration request."}, "RequestRejected(bytes32,address,uint256,string)": {"notice": "To be emitted when arbitration request is rejected."}}, "kind": "user", "methods": {"constructor": {"notice": "Creates an arbitration proxy on the home chain."}, "receiveArbitrationAnswer(bytes32,bytes32)": {"notice": "Receives an answer to a specified question. TRUSTED."}, "receiveArbitrationFailure(bytes32,address)": {"notice": "Receives a failed attempt to request arbitration. TRUSTED."}, "reportArbitrationAnswer(bytes32,bytes32,bytes32,address)": {"notice": "Reports the answer provided by the arbitrator to a specified question."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 20689, "contract": "src/0.8/RealitioHomeProxyOptimism.sol:RealitioHomeProxyOptimism", "label": "metadata", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 20710, "contract": "src/0.8/RealitioHomeProxyOptimism.sol:RealitioHomeProxyOptimism", "label": "requests", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_mapping(t_address,t_struct(Request)20702_storage))"}, {"astId": 20715, "contract": "src/0.8/RealitioHomeProxyOptimism.sol:RealitioHomeProxyOptimism", "label": "questionIDToRequester", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_address)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_enum(Status)20696": {"encoding": "inplace", "label": "enum RealitioHomeProxyOptimism.Status", "numberOfBytes": "1"}, "t_mapping(t_address,t_struct(Request)20702_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct RealitioHomeProxyOptimism.Request)", "numberOfBytes": "32", "value": "t_struct(Request)20702_storage"}, "t_mapping(t_bytes32,t_address)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_mapping(t_bytes32,t_mapping(t_address,t_struct(Request)20702_storage))": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => mapping(address => struct RealitioHomeProxyOptimism.Request))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(Request)20702_storage)"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Request)20702_storage": {"encoding": "inplace", "label": "struct RealitioHomeProxyOptimism.Request", "members": [{"astId": 20699, "contract": "src/0.8/RealitioHomeProxyOptimism.sol:RealitioHomeProxyOptimism", "label": "status", "offset": 0, "slot": "0", "type": "t_enum(Status)20696"}, {"astId": 20701, "contract": "src/0.8/RealitioHomeProxyOptimism.sol:RealitioHomeProxyOptimism", "label": "arbitratorAnswer", "offset": 0, "slot": "1", "type": "t_bytes32"}], "numberOfBytes": "64"}}}}